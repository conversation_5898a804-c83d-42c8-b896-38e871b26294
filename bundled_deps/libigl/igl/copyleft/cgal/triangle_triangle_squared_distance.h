// This file is part of libigl, a simple c++ geometry processing library.
// 
// Copyright (C) 2016 <PERSON> <ale<PERSON><PERSON><PERSON><PERSON>@gmail.com>
// 
// This Source Code Form is subject to the terms of the Mozilla Public License 
// v. 2.0. If a copy of the MPL was not distributed with this file, You can 
// obtain one at http://mozilla.org/MPL/2.0/.
#ifndef IGL_COPYLEFT_CGAL_TRIANGLE_TRIANGLE_SQUARED_DISTANCE_H
#define IGL_COPYLEFT_CGAL_TRIANGLE_TRIANGLE_SQUARED_DISTANCE_H
#include "../../igl_inline.h"
#include <CGAL/Triangle_3.h>
#include <CGAL/Point_3.h>
namespace igl
{
  namespace copyleft
  {
    namespace cgal
    {
      // Given two triangles T1 and T2 find the points on each of closest
      // approach and the squared distance thereof.
      // 
      // Inputs:
      //   T1  first triangle
      //   T2  second triangle
      // Outputs:
      //   P1  point on T1 closest to T2
      //   P2  point on T2 closest to T1
      //   d  distance betwee P1 and T2
      // Returns true if the closest approach is unique.
      template < typename Kernel>
      IGL_INLINE bool triangle_triangle_squared_distance(
        const CGAL::Triangle_3<Kernel> & T1,
        const CGAL::Triangle_3<Kernel> & T2,
        CGAL::Point_3<Kernel> & P1,
        CGAL::Point_3<Kernel> & P2,
        typename Kernel::FT & d);
    }
  }
}
#ifndef IGL_STATIC_LIBRARY
#  include "triangle_triangle_squared_distance.cpp"
#endif

#endif

