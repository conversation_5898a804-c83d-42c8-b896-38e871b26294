2002-12-12  <PERSON>  <<EMAIL>>

	* main.c: minor cleanup

2002-12-07  <PERSON>  <<EMAIL>>

	* avrdude.1, main.c:
	If the stk500 is being used, default to using the first serial port.

2002-12-03  <PERSON>  <<EMAIL>>

	* avrdude.1: Mention STK500 support.

2002-12-01  <PERSON>  <<EMAIL>>

	* stk500.c: Remove unused code.

	* CHANGELOG, stk500.c:
	Document changes since the previous version in the CHANGELOG.

	Cleanup stk500.c a bit.

	* stk500.c: Fix cut and paste braino.

	* avr.c, avrdude.conf.sample, main.c, pgm.h, stk500.c:
	The STK500 can perform paged read/write operations even on standard
	"non-paged" parts.  Take advantage of that and use the faster internal
	routines of the STK500 for those parts as well.

	* avr.c, avr.h, avrpart.h, main.c, pgm.c, pgm.h, stk500.c:
	Optimize reading and writing for the STK500 programmer if the part
	supports paged reads and writes.  This greatly decreases the
	program/verify time from about 4.5 minutes down to about 10 seconds in
	a 12K program size test case.

	Print out the hardware and firmware version for the STK500 if verbose
	is enabled.

	* avrdude.conf.sample, avrpart.h, config_gram.y, lexer.l, pgm.h:
	* ppi.c, ppi.h, stk500.c, stk500.h, stk500_private.h:
	Add basic support for STK500.

2002-11-30  Brian S. Dean  <<EMAIL>>

	* avrdude.conf.sample, config.c, config.h, config_gram.y, lexer.l:
	* main.c, pgm.c, pgm.h, ppi.c, ppi.h, term.c, term.h, Makefile:
	* avr.c, avr.h:
	Seperate programmer operations out into a driver-like interface so
	that programmers other than the direct parallel port connection can be
	supported.

2002-11-23  Brian S. Dean  <<EMAIL>>

	* CHANGELOG, main.c, term.c:
	term.c - when in interactive terminal mode and dumping memory using
	         the 'dump <memtype>' command without any address information,
	         and the end of memory is reached, wrap back around to zero on
	         the next invocation.

	CHANGELOG - describe changes

	main.c - update version number

	* main.c:
	When getting ready to initiate communications with the AVR device,
	first pull /RESET low for a short period of time before enabling the
	buffer chip.  This sequence allows the AVR to be reset before the
	buffer is enabled to avoid a short period of time where the AVR may be
	driving the programming lines at the same time the programmer tries
	to.  Of course, if a buffer is being used, then the /RESET line from
	the programmer needs to be directly connected to the AVR /RESET line
	and not via the buffer chip.

2002-11-06  Brian S. Dean  <<EMAIL>>

	* CHANGELOG: Update changelog.

	* avr.c, avr.h, main.c: Fix -Y option.  Reported by Joerg Wunsch.

2002-11-01  Brian S. Dean  <<EMAIL>>

	* CHANGELOG, main.c: Version update and CHANGELOG entry.

	* avr.c:
	Be backward compatible with the 2-byte rewrite cycle counter which
	appeared in version 2.1.0, but was changed to a 4 byte counter in
	version 2.1.1.  Reminded by Joerg Wunsch.

2002-10-29  Brian S. Dean  <<EMAIL>>

	* CHANGELOG, avrdude.1, main.c:
	Add '-V' (no verify) flag requested by Joerg Wunsch.  Update the man
	page.

2002-10-13  Brian S. Dean  <<EMAIL>>

	* CHANGELOG, avrdude.1: Update man page and changelog.

	* main.c: Update version number.

2002-10-12  Brian S. Dean  <<EMAIL>>

	* Makefile: Remove --pedantic and -g from the compiler options.

2002-10-11  Brian S. Dean  <<EMAIL>>

	* avr.c, term.c:
	Use a four byte value instead of a two byte value for the programming
	cycle count stored at the end of EEPROM.  It seems as though Atmel was
	greatly conservative in claiming a 1000 count reliability for the
	FLASH.  I current have a part that has been reprogrammed 173330 times,
	and counting.

	Fix a compiler warning.

	* avrdude.conf.sample:
	Fix ATMega128 instruction encoding for reading the low and high fuse
	bits.  Thanks to Joerg Wunsch for tripping over this.

2002-08-01  Brian S. Dean  <<EMAIL>>

	* avr.c, avrdude.1, main.c:
	Move erase-rewrite cycle increment to within the chip erase routine so
	that it is tracked no matter where the erase was initiated: command
	line mode or interactive mode, without code duplicaiton.

	* CHANGELOG: Recent updates.

	* avr.c: Eliminate unused variables.

	* avr.c, avr.h, avrdude.1, fileio.c, main.c:
	Implement a way of tracking how many erase-rewrite cycles a part has
	undergone.  This utilizes the last two bytes of EEPROM to maintain a
	counter that is incremented each time the part is erased.

2002-07-27  Brian S. Dean  <<EMAIL>>

	* avr.c, main.c:
	Fix a typo in a comment.  Display the size of memory being written.
	Display the correct memory name in an error message (previously
	hardcoded).

2002-06-22  Brian S. Dean  <<EMAIL>>

	* CHANGELOG, avrdude.conf.sample:
	Add support for ATtiny15 - contributed by Asher Hoskins
	<<EMAIL>>

2002-04-23  Brian S. Dean  <<EMAIL>>

	* CHANGELOG: Say what changed.

2002-04-07  Brian S. Dean  <<EMAIL>>

	* Makefile, avrdude.conf.sample:
	Backup the config file to a timestamped name to keep from possibly
	overwriting user-modified configs.

	Add read/write instructions for all memory types for ATMEGA103,
	ATMEGA128, ATMEGA16, and ATMEGA8.

2002-04-05  Brian S. Dean  <<EMAIL>>

	* avrdude.conf.sample:
	Add support for ATMEGA128; untested; requested by Jeff Gardner
	<<EMAIL>>.

2002-02-15  Brian S. Dean  <<EMAIL>>

	* avrdude.conf.sample: Minor ordering.

	* CHANGELOG, main.c: Update version numbers.

2002-02-14  Brian S. Dean  <<EMAIL>>

	* CHANGELOG: Summarize latest updates.

	* avrdude.conf.sample, config_gram.y:
	Make pwroff_after_write a yes/no field instead of a numeric.

	* avrdude.conf.sample: Document the pwroff_after_write flag.

	* avr.c: Enable the extra part verbosity when verbosity >= 3.

	* avr.c, avr.h, avrdude.conf.sample, config_gram.y, lexer.l:
	* main.c, term.c:
	Fix error reporting by avr_write_byte().

	Fix setting of status LEDs under various write-fail conditions.

	Add a flag to indicate that a memory type requires the device to
	possibly be powered off and back on after a write to it.  This is due
	to a hardware problem on some Atmel devices, see:

		http://www.atmel.com/atmel/acrobat/doc1280.pdf

	Add greater verbosity to the part-display code when verbose>1 to
	display avrprog's encoding of the defined programming instructions.
	This is primarily for debugging purposes.


	Part updates:

	  * add the AT90S4414 part

	  * add fuse and lock bit access instructions for the AT90S1200,
	    AT90S4434, and AT90S8515.

	  * add the pwroff_after_write flag to the fuse bits for the AT90S2333
	    and AT90S4433 parts

2002-02-09  Brian S. Dean  <<EMAIL>>

	* avrdude.conf.sample:
	Updates to the 2333 and 4433 parts, contributed by Joerg Wunsh.

2002-01-18  Brian S. Dean  <<EMAIL>>

	* CHANGELOG: Add changelog.

2002-01-12  Brian S. Dean  <<EMAIL>>

	* main.c: Add (c) to copyright.

	* fileio.c, fileio.h, lexer.l, lists.c, lists.h, main.c:
	* pindefs.h, ppi.c, ppi.h, term.c, term.h, avr.c, avr.h:
	* config.c, config.h, config_gram.y:
	Update version number.  Update copyright.

	* avrdude.1: Update copyright and add description of "default".

	Submitted by: Joerg Wunsch <<EMAIL>>

	* avr.c, term.c:
	Fix programming of write-only memories (such as lock bits on the
	2313).

