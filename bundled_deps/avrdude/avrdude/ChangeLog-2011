2011-12-30  <PERSON> <<PERSON><PERSON>@gmx.de>

	* avrdude.conf.in: Added is_at90s1200 option to part description
	* doc/avrdude.texi: Added missing options to part definition
	* config_gram.y: Fixed resetting of is_at90s1200 and is_avr32 flags

2011-12-30  <PERSON> <<PERSON><PERSON>@gmx.de>

	patch #7693: Fix config file atmel URLs
	* avrdude.conf.in: Updated URLs
	* avrpart.h: Updated URLs
	* doc/avrdude.texi: Updated URLs

2011-12-30  <PERSON><PERSON> <<EMAIL>>

	* ser_posix.c (baud_lookup_table): Conditionalize the inclusion of
	non-standard baud rates (only baud rates up to B38400 are
	standardized by the Single UNIX Specification).

2011-12-29  <PERSON> <<PERSON>.<PERSON>@gmx.de>

	bug #34302: Feature request : device configuration with parent classes
	* config_gram.y: Added part parent rule and allow overwriting existing
	data at several places
	* avrdude.conf.in: Added description comment and m328/m328p as example
	* avrpart.c: avr_dup_mem-functions now copy buf and tags memory block 
	only they are already allocated.
	* lexer.l: Added parent as valid token
	
	(not in original patch) 
	* avrpart.c: New function avr_dup_opcode. avr_dup_mem/avr_dup_part-
	functions now duplicate the opcodes in their op-array to avoid memory leaks.
	* doc/avrdude.texi: Added description of part parent feature

2011-12-29  Rene Liebscher <<EMAIL>>

	patch #7687: Autogenerating programmers and parts lists for docs
	(generating the parts lists, programmers lists follows later)
	* doc/Makefile.am: Add rule how to create avrdude before generating parts list

2011-12-29  Rene Liebscher <<EMAIL>>

	patch #7687: Autogenerating programmers and parts lists for docs
	(generating the parts lists, programmers lists follows later)
	* doc/avrdude.texi: Add include of generated table of parts
	* doc/Makefile.am: Add generating of table of parts in parts.texi
	* doc/parts_comments.txt: Adding file containing part commenz references
	* avrdude.1: Remove table of parts and mention "-p ?" option
	* avrpart.c: Use AVR_DESCLEN for strncasecmp at list sorting

2011-12-22  Rene Liebscher <<EMAIL>>

	* configure.ac: Add writing of definition of confsubst to config.status,
	so it can run alone, not only called by configure.

2011-12-17  Rene Liebscher <<EMAIL>>

	patch #7680: Fixing timeout problem in ser_recv in ser_win32.c
	* ser_win32.c: Return -1 at timeout in ser_recv().

2011-12-17  Rene Liebscher <<EMAIL>>

	* config_gram.y: Fixed another memory leak, when define an operation
	more than once
	* avrdude.conf.in: Fixed double definition at ATmega6490

2011-12-17  Rene Liebscher <<EMAIL>>

	* config_gram.y: Restructuring and compacting programmer definition 
	part of grammar (in preparation of patch #7688)

2011-12-17  Rene Liebscher <<EMAIL>>

	* avrdude.conf.in: Update documentation of programmer definition
	* doc/avrdude.texi: Update documentation of programmer definition 
	and add list of implemented programmer types

2011-12-17  Rene Liebscher <<EMAIL>>

	patch #7667: Minor memory handling fixes
	* config_gram.y: Added several free_token() calls.

2011-12-16  Rene Liebscher <<EMAIL>>

	patch #7671: Sorting programmers and parts lists for console output
	* avrdude.conf.in: change part desc of several parts to common pattern
	AT(mega|tiny|xmega)[0-9]+[A-Z]* (Upper case AT, lower case in middle)
	* list.[ch]: added sorting function lsort()
	* pgm.[ch]: added function sort_programmers()
	* avrpart.[ch]: added function sort_avrparts()
	* main.c: use sort functions in list_programmers() and list_parts()
	* main.c: list functions show config file info only at verbose mode

2011-10-19  Joerg Wunsch <<EMAIL>>

	* configure.ac: Replace "cvs" in version number by "svn".

2011-10-10  Joerg Wunsch <<EMAIL>>

	bug #34518: loading intel hex files > 64k using record-type 4
	(Extended Linear Address Record)
	fileio.c: Replace the change from r928 (handling of 0x8000000
	offset in AVR32 files) by a completely different logic that no
	longer breaks hex files for other devices starting with an
	offset; also apply a similar change to S-record files, as well
	as when writing files.
	fileio.c: (Ditto.)

2011-09-15  Joerg Wunsch <<EMAIL>>

	* avrftdi.c: Remove stray printf()s by fprintf(stderr)
	* usbtiny.c: (Ditto.)

2011-09-15  Joerg Wunsch <<EMAIL>>

	* main.c: Restrict the cyclecounter readout to those cases where
	it has been explicitly requested (by -y or -Y), rather than always
	attempting to read the last EEPROM bytes.

2011-09-15  Joerg Wunsch <<EMAIL>>

	* stk500v2.c (stk600_xprog_paged_load, stk600_xprog_paged_write):
	Fix regression in the AVRISPmkII/STK600 TPI handling introduced
	by the USBasp's TPI implementation which added a pagesize even for
	the minor memory regions of TPI devices.  Also fix wrong offset
	introduced by the memory tagging patch.

2011-09-15  Joerg Wunsch <<EMAIL>>

	* avr.c (avr_read, avr_write): Don't bail out on TPI parts if
	their programmer doesn't provide a (low-level) cmd_tpi method;
	instead, fall back to the normal programmer methods which are
	supposed to handle the situation.
	This fixes a regression where the recent bitbang-TPI implementation
	broke TPI handling of STK600/AVRISPmkII.

2011-09-14  Joerg Wunsch <<EMAIL>>

	Mega-commit to bring in memory tagging.
	Each memory image byte is now tagged as it's being read from a file.
	Only bytes read from a file will be written or verified (modulo page
	granularity requirements).
	* avrpart.h: Add memory tags.
	* avrpart.c: Allocate and initialize tag area.
	* update.h: Drop unused parameter "verify" from do_op().
	* pgm.h: Add parameter base_addr to the paged_load and paged_write
	methods, respectively.
	* avr.h: New parameter to avr_read: second AVRPART to verify against.
	* fileio.c: Track all memory regions that have been read from an
	input file by tagging them.
	* update.c: Call avr_read() with the new parameter list.
	* main.c: Call avr_initmem() to initialize the memory regions, rather
	than trying to duplicate an unitialized part, and then let the
	original part rot away.
	* avr.c: Implement the heart of the new featureset.  For paged memory
	areas, when writing or verifying, call the paged_write and paged_load
	methods, respectively, once per page instead of on the entire memory.
	When writing, only write bytes or pages that have content read from a
	file.  Whe verifying, only read memory bytes or pages where the
	verification data have been read from a file.  Only verify those bytes
	that have been read from a file.
	* avrftdi.c: Implement the new API for paged_load and paged_write,
	respectively.
	* jtagmkII.c: (Ditto.)
	* butterfly.c: (Ditto.)
	* jtagmkI.c: (Ditto.)
	* avr910.c: (Ditto.)
	* stk500.c: (Ditto.)
	* usbasp.c: (Ditto.)
	* stk500v2.c: (Ditto.)
	* usbtiny.c: (Ditto.)

2011-09-13  Joerg Wunsch <<EMAIL>>

	* stk500v2.c (stk500v2_command): Treat warnings as errors rather than
	success.

2011-08-30  Joerg Wunsch <<EMAIL>>

	bug #34027: avrdude AT90S1200 Problem (part 3 - documentation)
	* avrdude.1: Document the programmer type restrictions for AT90S1200
	devices.
	* doc/avrdude.texi: (Ditto.)

2011-08-30  Joerg Wunsch <<EMAIL>>

	bug #34027: avrdude AT90S1200 Problem (part 2 - stk500v2 and relatives)
	* stk500v2.c (stk500v2_initialize): For the AT90S1200, release
	/RESET for a moment before reinitializing, as this is required by
	its programming protocol.

2011-08-30  Joerg Wunsch <<EMAIL>>

	* configure.ac: In AC_CHECK_LIB for libftdi, check for
	ftdi_usb_get_strings() rathern than ftdi_init(), as this is a more
	specific thing to search for in order to make sure getting a
	recent enough libftdi.

2011-08-29  Joerg Wunsch <<EMAIL>>

	bug #34027: avrdude AT90S1200 Problem (part 1 - bitbang
	programmers)
	* config_gram.y: Introduce new keyword "is_at90s1200".
	* lexer.l: (Ditto.)
	* avrdude.conf.in: Applew new keyword to the AT90S1200 device.
	* avrpart.h: Introduce new flag AVRPART_IS_AT90S1200, reflecting
	the is_at90s1200 configuration keyword.
	* bitbang.c (bitbang_initialize): Replace existing test for
	AT90S1200 by AVRPART_IS_AT90S1200
	* avr.c (avr_write_byte_default): Avoid the pre-write reading for
	the AT90S1200, as this appears to sometimes corrupt the high byte
	by pre-programming the low byte just written into it.

2011-08-27  Joerg Wunsch <<EMAIL>>

	* configure.ac: Bump version for post-5.11.

2011-08-27  Joerg Wunsch <<EMAIL>>

	* configure.ac: Bump version for releasing AVRDUDE 5.11.

2011-08-26  Joerg Wunsch <<EMAIL>>

	* avrdude.1: Update the list of supported AVR devices.
	* doc/avrdude.texi: (Ditto).

2011-08-26  Joerg Wunsch <<EMAIL>>

	* configure.ac: add -lusb as "other libraries" when checking
	for libftdi.

2011-08-26  Joerg Wunsch <<EMAIL>>

	Submitted by Juergen Weigert:
	patch #7056: adding support for mikrokopter bootloader to butterfly
	* butterfly.c: Add some specific logic to handle the
	mikrokopter.de butterfly bootloader.
	* butterfly.h: Add one related function declaration.
	* config_gram.y: Add butterfly_mk keyword.
	* lexer.l: (Ditto.)
	* avrdude.conf.in: Add entry for butterfly_mk.

2011-08-26  Joerg Wunsch <<EMAIL>>

	Submitted by Stefan Tomanek:
	patch #7542: add default_bitclock to configuration files
	* config.c: Add the new keyword and its handling.
	* config.h: (Ditto.)
	* config_gram.y: (Ditto.)
	* avrdude.conf.in: (Ditto.)
	* main.c: (Ditto.)
	* lexer.l: (Ditto.)
	* avrdude.1: Document the change.
	* doc/avrdude.texi: (Ditto.)

2011-08-26  Joerg Wunsch <<EMAIL>>

	Submitted by Brett Hagman:
	patch #7603: wiring - programmer type for Wiring boards
	(based on STK500v2)
	* wiring.c: New file.
	* wiring.h: (Ditto.)
	* Makefile.am: Add new files.
	* stk500v2_private.h: Reorganize so some functions and struct
	pdata are globally known.
	* stk500v2.c: (Ditto.)
	* stk500v2.h: (Ditto.)
	* lexer.l: Add new programmer keywords.
	* config_gram.y: (Ditto.)
	* avrdude.conf.in: Add "wiring" programmer entry.
	* avrdude.1: Document the new programmer.
	* doc/avrdude.texi: (Ditto.)
	* AUTHORS: Add Brett Hagman.

2011-08-26  Joerg Wunsch <<EMAIL>>

	Submitted by an anonymous contributor on the mailinglist:
	* avrdude.conf (jtagkey): Add a definition for the Amontec
	JTAGKey

2011-08-26  Joerg Wunsch <<EMAIL>>

	Submitted by Juergen Weigert:
	bug #22720: avrdude-5.5 ignores buff settings in avrdude.conf
	(Note that the actual bug the subject is about has been fixed
	long ago.)
	* update.c (do_op): fix a diagnostic message
	* pgm.h: add exit_datahigh field
	* par.c: set and act upon the exit_datahigh field
	* avrdude.1: document the new -E options
	* doc/avrdude.texi: (Ditto.)

2011-08-26  Joerg Wunsch <<EMAIL>>

	bug #33811: Parallel make fails
	* Makefile.am (BUILT_SOURCES): Add this macro.

2011-08-26  Joerg Wunsch <<EMAIL>>

	bug #33114: Segfault after setting the DWEN fuse with Dragon
	* jtagII.c (jtagmkII_getsync): Instead of exit()ing from
	deep within the tree when detecting the "need debugWIRE"
	situation, properly pass this up as a return code.
	* jtagII_private.h (JTAGII_GETSYNC_FAIL_GRACEFUL): New constant.
	* stk500v2.c (stk500v2_jtagmkII_open): Don't tell anything
	anymore when receiving a JTAGII_GETSYNC_FAIL_GRACEFUL from
	jtagmkII_getsync(); silently give up (all necessary has been
	said already).

2011-08-26  Joerg Wunsch <<EMAIL>>

	Reported by Jason Hecker:
	* usbasp.c (libusb_to_errno): Conditionalize some error codes
	that apparently are lacking on MinGW.

2011-08-25  Joerg Wunsch <<EMAIL>>

	Fix warnings.
	* ser_avrdoper.c: add <stdlib.h> so exit() is declared.
	* usbtiny.c (usbtiny_open): provide an initializer to a
	"may be used uninitialized" variable (since GCC could not
	fully detect the logic behind).

2011-08-25  Joerg Wunsch <<EMAIL>>

	* configure.ac: Add a check for FreeBSD's libusb-1.0
	compatible library that is found in libusb.a/.so on
	FreeBSD 8+.

2011-08-25  Joerg Wunsch <<EMAIL>>

	Submitted by Doug Springer, based on work by
	Wolfgang Moser, Ville Voipio, Hannes Weisbach
	patch #7486: Patch to add FT2232C/D, FT2232H, FT4232H,
	usbvid, usbpid, usbdev for USB support - Based on #7062
	* avrftdi.c: New file.
	* avrftdi.h: (Ditto.)
	* configure.ac: Add check for libftdi.
	* config_gram.y: Add AVRFTDI and per-programmer USB string
	keywords.
	* lexer.l: (Ditto.)
	* avrdude.conf.in: Add avrftdi and 2232HIO programmers.
	* pgm.h: Add USB parameters.
	* Makefile.am: Add avrftdi.c and avrftdi.h.
	* AUTHORS: Mention the new authors.
	* avrdude.1: Document the changes.
	* doc/avrdude.texi: (Ditto.)

2011-08-23  Joerg Wunsch <<EMAIL>>

	bug #29585: Fix license
	* doc/avrdude.texi: Add FDL as an option to the licensing
	statement, as the savannah administration would like it
	that way.

2011-08-23  Joerg Wunsch <<EMAIL>>

	Submitted by Darell Tan:
	patch #7244: TPI bitbang implementation
	* bitbang.c: Add TPI bitbang stuff.
	* bitbang.h: (Ditto.)
	* avr.c: (Ditto.)
	* avr.h: (Ditto.)
	* pgm.c: (Ditto.)
	* pgm.h: (Ditto.)
	* serbb_posix.c: Wire bitbang_cmd_tpi into the struct pgm.
	* serbb_win32.c: (Ditto.)
	* par.c: (Ditto.)
	* doc/avrdude.texi: Document the TPI bitbang support.

2011-08-17  Joerg Wunsch <<EMAIL>>

	Submitted by Grygoriy Fuchedzhy:
	bug #31779: Add support for addressing usbtinyisp with -P option
	* usbtiny.c (usbtiny_open): Add logic to distinguish multiple USBtinyISP
	programmers by their bus:device tuple.
	* doc/avrdude.texi: Document the new functionality.
	* avrdude.1: (Ditto.)

2011-08-16  Joerg Wunsch <<EMAIL>>

	Submitted by Timon Van Overveldt:
	bug #30268: Debugwire broken in avrdude-5.10
	* jtagmkII.c (jtagmkII_initialize): only try setting up a JTAG chain when
	the programmer is using JTAG.

2011-08-16  Joerg Wunsch <<EMAIL>>

	bug #29636: AVRDude issues invalid CMD_CHECK_TARGET_CONNECTION
	on the AVRISP-MKII
	* stk500v2.c (stk500v2_program_enable): Rewrite the logic to
	explain ISP activation failures.
	* stk500v2_private.h: Fix the various STATUS_* constants;
	AVR069 and AVR079 disagreed in their values, even though they
	are apparently implementing the same logic behind.

2011-08-16  Joerg Wunsch <<EMAIL>>

	bug #29650: Programming timeouts in ATmega128RFA1 are too slow
	* avrdude.conf.in (ATmega128RFA1): Bump write delay values for flash and
	EEPROM to 50 ms.

2011-08-16  Joerg Wunsch <<EMAIL>>

	* avrdude.conf.in (ATmega8515, ATmega8535, ATmega48, ATmega88, ATmega88P,
	ATtiny88, ATmega168, ATmega168P, ATmega328P): Bump delay value for STK500v2
	EEPROM write operation to 5, according to the respective XML files.

2011-08-16  Joerg Wunsch <<EMAIL>>

	Submitted by Darcy Houlahan:
	bug #29694: error in avrdude.conf for attiny84 eeprom
	* avrdude.conf.in (ATtiny84, ATtiny85): fix A7 bit in EEPROM write
	command.

2011-08-16  Joerg Wunsch <<EMAIL>>

	Submitted by Durant Gilles:
	* avrdude.conf.in (ATtiny4313): Fix flash addressing bits for manual ISP
	algorithm.

2011-08-16  Joerg Wunsch <<EMAIL>>

	Submitted by Philip:
	bug #31386: A "BUILD.svn" or similar "how to get started" doc would be helpful
	* BUILD-FROM-SVN: New file.

2011-08-15  Joerg Wunsch <<EMAIL>>

	Submitted by Nic Jones:
	bug #32539: [Documentation][Patch] Man page is misleading
	re: Dragon & PDI
	* doc/avrdude.texi: Update information about PDI connections
	on AVR Dragon

2011-08-12  Joerg Wunsch <<EMAIL>>

	* usbasp.c: Add <stdint.h> so this actually compiles
	again.

2011-08-12  Joerg Wunsch <<EMAIL>>

	<NAME_EMAIL>:
	bug #33345: File auto detection as binary doesn't open
	file in binary mode on Windows
	* fileio.c: Move the decision about opening files in
	binary mode until before the fopen() call.

2011-06-16  Thomas Fischl <<EMAIL>>

	* avrdude.conf.in: Fix part id of ATtiny9.

2011-05-28  Thomas Fischl <<EMAIL>>

	Based on patch #7440 commited by Slawomir Fraś:
	* usbasp.c: added TPI support for USBasp
	* usbasp.h: (Ditto.)

2011-05-11  Joerg Wunsch <<EMAIL>>

	* avrdude.conf.in: Add support for ATmega168P.

2011-05-11  Joerg Wunsch <<EMAIL>>

	* avrdude.conf.in: Fix abbreviated name for ATmega324PA.

2011-05-11  Joerg Wunsch <<EMAIL>>

	Submitted by Lech Perczak:
	bug #30946: Added support for ATmega8/16/32U2
	* avrdude.conf.in: Add ATmega8/16/32U2 entries.

2011-05-11  Joerg Wunsch <<EMAIL>>

	Submitted by David A Lyons:
	patch #7393: Adding ATtiny4313 Device to avrdude.conf.in
	* avrdude.conf.in: Add ATtiny4313 data.

2011-05-11  Joerg Wunsch <<EMAIL>>

	* usb_libusb.c: Bump timeout values to allow for slow clock
	speeds.
	* jtagmkII.c: (Ditto.)

2011-03-04  Eric B. Weddington  <<EMAIL>>

	Thanks to Vitaly Chernookiy for the patch.
	* avrdude.conf.in: Add support for atmega324pa.
	* ChangeLog-2010: New file, rotate ChangeLog for new year.
