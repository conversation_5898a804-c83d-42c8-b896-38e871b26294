2012-12-18  <PERSON><PERSON> <<EMAIL>>

	* usbdefs.h (USBDEV_BULK_EP_WRITE_STK600)
	(USBDEV_BULK_EP_READ_STK600): new define values
	* stk500v2.c (stk600_open): use the STK600 EP values,
	as they are different from AVRISPmkII

2012-12-18  <PERSON><PERSON> <<EMAIL>>

	bug #37942: Latest SVN can't program in dragon_jtag mode
	* jtagmkII.c (jtagmkII_initialize): For Xmega devices, and
	firmware >= 7.x, don't trigger a RESET, in order to work around a
	firmware bug that appears to be present in at least firmware 7.24
	for the Dragon.

2012-12-04  <PERSON><PERSON> <<EMAIL>>

	* config_gram.y: Implement the "ocdrev" keyword
	* avrpart.c: (Dito)
	* avrpart.h: (Dito)
	* lexer.l: (Dito)
	* avrdude.conf.in: Add "ocdrev" key/value pairs, based
	on the AS6 XML file information.
	* jtag3.c: Use the ocdrev in the parameter block.

2012-12-03  <PERSON><PERSON> <<EMAIL>>

	* jtag3.c: Make jtag3_command() public
	* jtag3.h: (Dito.)
	* jtag3_private.h: Add two new commands
	* stk500v2.c: Implement the "MonCon disable" hack that
	allows temporarily falling back to ISP when trying to
	talk to a part that has debugWIRE enabled

2012-12-03  Rene Liebscher <<EMAIL>>

	* pickit2.c: reordered #includes for non-usb configuration

2012-12-03  Joerg Wunsch <<EMAIL>>

	* jtag3.c: Enable interactive adjustment of the various
	clock frequencies (JTAG Xmega, JTAG megaAVR, PDI Xmega)
	through the set_sck_period() callback.

2012-12-03  Joerg Wunsch <<EMAIL>>

	* jtag3.c: Remove unused code that was left over from
	cloning the jtagmkII.c implementation

2012-12-03  Joerg Wunsch <<EMAIL>>

	* pgm_type.c: Add "jtagice3_isp" programmer hook
	* avrdude.conf.in: Add "jtag3isp" programmer
	* jtag3.c: jtag3_setparm() is now public
	* jtag3.h: (Dito)
	* stk500v2_private.h: Command 0x1D is CMD_SPI_MULTI only
	for STK500v2, AVRISPmkII, and JTAGICEmkII; for JTAGICE3,
	it's CMD_SET_SCK now; also add CMD_GET_SCK
	* avrpart.c (avr_get_output_index): New function
	* avrpart.h: (Dito)
	* stk500v2.c: Implement the pasthrough programmer glue logic
	for JTAGICE3 in ISP mode
	* stk500v2.h: (Dito)
	* avrdude.1: Document the JTAGICE3 support.

2012-11-30  Joerg Wunsch <<EMAIL>>

	* jtag3.c (jtag3_read_byte, jtag3_write_byte): Remove the
	m->offset from addr, JTAGICE3 doesn't need it anymore (similar
	to JTAGICEmkII with 7+ firmware)
	* jtag3.c (jtag3_read_byte): Allow for full-page reads of
	EEPROM also for Xmega and debugWIRE, allow for signature
	read in debugWIRE

2012-11-30  Joerg Wunsch <<EMAIL>>

	* jtag3_private.h: Add two more error detail codes I stumbled
	across during development
	* jtag3.c: (Dito.)
	* usb_libusb.c: Reduce timeouts from 100 to 10 s, still long
	enough, but not getting cold feet when something goes wrong.

2012-11-29  Joerg Wunsch <<EMAIL>>

	* jtag3.c: Handle events returned by the ICE
	* usbdevs.h: Add defines that mark an event in return
	from usb_recv_frame().
	* usb_libusb.c: (Dito.)

2012-11-29  Joerg Wunsch <<EMAIL>>

	* avrdude.conf.in: Remove "has_jtag" from Xmega A4 and D4
	devices, as they only have PDI.
	* jtag3.c (jtag3_page_erase): Actually implement this.

2012-11-29  Joerg Wunsch <<EMAIL>>

	bug #37265: wrong page sizes for XMega64xx in avrdude.conf
	* avrdude.conf.in: Fix page sizes for all Xmega devices,
	by cross-checking against Atmel Studio's device XML files

2012-11-29  Joerg Wunsch <<EMAIL>>

	* jtag3.c: Fill in the missing pieces for Xmega support (both,
	PDI and JTAG).
	* jtagmkII.c (jtagmkII_set_xmega_params): Use "fuse1" rather
	than "fuse0" memory space to fill in the NVM offset from, as
	there is no "fuse0" on some Xmega devices.

2012-11-29  Joerg Wunsch <<EMAIL>>

	* avrdude.conf.in (ATmega256RFR2, ATmega128RFR2, ATmega64RFR2):
	New devices

2012-11-28  Joerg Wunsch <<EMAIL>>

	First support for Atmel JTAGICE3.  Guessed from USB sniffer
	traces made by Knut Schwichtenberg, and by similarity to
	JTAGICEmkII.
	Still quite incomplete, just megaAVR/JTAG is done by now.
	* jtag3.c: New file.
	* jtag3.h: (Dito.)
	* jtag3_private.h: (Dito.)
	* pgm_type.c: Add new programmers
	* avrdude.conf.in: (Dito.)
	* usbdevs.h: Add new parameters
	* Makefile.am: Add new files
	* usb_libusb.c: Handle separate event endpoint, and larger
	(USB 2.0) packet sizes

2012-11-26  Joerg Wunsch <<EMAIL>>

	* jtagmkII.c: Change all the USB details (endpoint numbers,
	max transfer size etc.) to a per-programmer adjustable value.
	* serial.h: (Dito.)
	* stk500v2.c: (Dito.)
	* usbdevs.h: (Dito.)
	* usb_libusb.c: (Dito.)

2012-11-20  Joerg Wunsch <<EMAIL>>

	* buspirate.c: Replace outdated FSF postal address by a reference to
	the GPL info on their website.
	* jtagmkII.c: (Dito.)
	* avrftdi.c: (Dito.)
	* wiring.c: (Dito.)
	* linux_ppdev.h: (Dito.)
	* serbb.h: (Dito.)
	* usbtiny.h: (Dito.)
	* confwin.c: (Dito.)
	* buspirate.h: (Dito.)
	* avrftdi.h: (Dito.)
	* wiring.h: (Dito.)
	* jtagmkII.h: (Dito.)
	* pickit2.c: (Dito.)
	* config.c: (Dito.)
	* term.c: (Dito.)
	* confwin.h: (Dito.)
	* avrdude.1: (Dito.)
	* windows/Makefile.am: (Dito.)
	* config.h: (Dito.)
	* pickit2.h: (Dito.)
	* term.h: (Dito.)
	* tools/get-hv-params.xsl: (Dito.)
	* tools/get-stk600-cards.xsl: (Dito.)
	* tools/get-stk600-devices.xsl: (Dito.)
	* tools/get-dw-params.xsl: (Dito.)
	* butterfly.c: (Dito.)
	* configure.ac: (Dito.)
	* doc/Makefile.am: (Dito.)
	* pgm_type.c: (Dito.)
	* butterfly.h: (Dito.)
	* jtagmkI.c: (Dito.)
	* ft245r.c: (Dito.)
	* COPYING: (Dito.)
	* pgm_type.h: (Dito.)
	* jtagmkI.h: (Dito.)
	* pindefs.h: (Dito.)
	* config_gram.y: (Dito.)
	* arduino.c: (Dito.)
	* arduino.h: (Dito.)
	* ser_win32.c: (Dito.)
	* serbb_win32.c: (Dito.)
	* avr910.c: (Dito.)
	* stk500.c: (Dito.)
	* freebsd_ppi.h: (Dito.)
	* avr910.h: (Dito.)
	* solaris_ecpp.h: (Dito.)
	* stk500.h: (Dito.)
	* jtagmkII_private.h: (Dito.)
	* avrdude.h: (Dito.)
	* bitbang.c: (Dito.)
	* bitbang.h: (Dito.)
	* avrpart.c: (Dito.)
	* safemode.c: (Dito.)
	* stk500generic.c: (Dito.)
	* serial.h: (Dito.)
	* avrpart.h: (Dito.)
	* jtagmkI_private.h: (Dito.)
	* ppi.c: (Dito.)
	* avr.c: (Dito.)
	* safemode.h: (Dito.)
	* stk500generic.h: (Dito.)
	* ser_avrdoper.c: (Dito.)
	* avr.h: (Dito.)
	* ppi.h: (Dito.)
	* usbasp.c: (Dito.)
	* lists.c: (Dito.)
	* stk500v2.c: (Dito.)
	* my_ddk_hidsdi.h: (Dito.)
	* tpi.h: (Dito.)
	* usbasp.h: (Dito.)
	* lists.h: (Dito.)
	* stk500v2.h: (Dito.)
	* ppiwin.c: (Dito.)
	* fileio.c: (Dito.)
	* ser_posix.c: (Dito.)
	* fileio.h: (Dito.)
	* serbb_posix.c: (Dito.)
	* usbdevs.h: (Dito.)
	* par.c: (Dito.)
	* update.c: (Dito.)
	* pgm.c: (Dito.)
	* main.c: (Dito.)
	* par.h: (Dito.)
	* update.h: (Dito.)
	* lexer.l: (Dito.)
	* Makefile.am: (Dito.)
	* pgm.h: (Dito.)
	* usb_libusb.c: (Dito.)
	* usbtiny.c: (Dito.)

2012-11-13  Rene Liebscher <<EMAIL>>

	bug #35186 inverting pins with "~" doesn't work for pin lists (i.e. vcc)
	bug #37727 Add support for LM3S811 dev board as a programmer
	* lexer.l,config_gram.y: accepting inverted pins at pin lists 
	        syntax: ~num or ~(num,num,...)
	* par.c: par_set_many_bits is now usable with inverted pins
	* avrftdi.c: fixed wrong index in ftdi_pin_name
	* avrdude.conf.in: added programmer lm3s811

2012-11-04  Rene Liebscher <<EMAIL>>

	* lexer.l,config_gram.y,config.[hc]: changed reading of numbers to integers
	        except of default_bitclock which is the only real number.
	        No signs are allowed as negative values do not make sense for current
	        config values.
	* buspirate.c: include own header file buspirate.h
	* doc/.cvsignore: add programmers.texi to ignore list

2012-09-06  Joerg Wunsch <<EMAIL>>

	* doc/Makefile.am: add EXTRA_DIST, replace $(srcdir) by
	$(builddir) for generated files, so "make distcheck"
	works again

2012-09-05  Rene Liebscher <<EMAIL>>

	* doc/Makefile.am: add $(srcdir) to name of generated files, so BSD make
	                   find the files ( GNU make sees no difference if the 
	                   file is called version.texi or ./version.texi )

2012-08-15  Rene Liebscher <<EMAIL>>

	patch #7184 Support for PICKit2 programmer
	* Makefile.am: add pickit2 files
	* pickit2.[ch]: new programmer implementation
	* pgm_type.c: add pickit to list
	* avrdude.1: documentation for pickit2
	* doc/avrdude.texi: documentation for pickit2
	* avrdude.conf.in: add pickit2 programmer entry

2012-08-15  Rene Liebscher <<EMAIL>>

	bug #30559 Ft232 bit-bang support, see comment #30
	* ft245r.c: added semaphore workaround for MacOS X,
	            added pthread_testcancel in reader thread
	
	* configure.ac: added check for TYPE_232H in libftdi (not in libftdi < 0.20)
	* avrftdi.c: do not use TYPE_232H if not declared

2012-08-13  Hannes Weisbach <<EMAIL>>

	* avrftdi.c: fixes pin_limit for different FTDI devices (there was a mixup
	between 2232C and 2232H)

2012-07-29  Hannes Weisbach <<EMAIL>>

	* avrftdi.c: bugfixes (synchronisation) and maintenance (paged programming,
	nicer output, separation of parameter checking and actual code)

2012-07-25  Joerg Wunsch <<EMAIL>>

	* jtagmkII.c (jtagmkII_memtype): return MTYPE_FLASH rather than
	MTYPE_SPM for non-Xmega flash regions

2012-07-20 Hannes Weisbach <<EMAIL>>

	* avrpart.c, avrpart.h: adds avr_pin_name()

2012-07-18  Joerg Wunsch <<EMAIL>>

	* configure.ac: check for libelf.h also in libelf/
	* fileio.c: include <libelf/libelf.h> if configure found this
	to be the case

2012-06-13  Rene Liebscher <<EMAIL>>

	* configure.ac: Check for presence of <pthread.h>
	* ft245r.c: Depend on HAVE_PTHREAD_H
	* Makefile.am: Add -lpthread if needed.

2012-06-07  Joerg Wunsch <<EMAIL>>

	* usbtiny.c (usbtiny_paged_load, usbtiny_paged_write):
	fix breakage introduced by the recent page handling reorg;
	it used to cause an infinite loop

2012-05-04  Joerg Wunsch <<EMAIL>>

	Xmega page erase implementation for XPROG (AVRISPmkII, STK600)
	* stk500v2.c (stk600_xprog_page_erase): New function.

2012-05-04  Joerg Wunsch <<EMAIL>>

	Xmega page erase implementation for JTAGICEmkII
	* jtagmkII.c: Handle flash pages sizes > 256 bytes, implement
	page_erase() method
	* avrdude.conf.in: Change flash pagesize for all Xmega devices
	to 512 bytes
	* avr.c: Implement auto_erase, using page_erase if available
	* avr.h: Remove unused parameters from avr_read(), replace
	unused parameter in avr_write)() by auto_erase
	* stk500v2.c: Handle flash page sizes > 256 bytes
	* update.c (do_op): Handle new updateflags parameter
	* main.c: Implement auto_erase as page_erase if possible
	* update.h (enum updateflags): New enum
	* pgm.h (struct programmer_t): Add page_erase method

2012-04-26  Joerg Wunsch <<EMAIL>>

	* jtagmkII.c (jtagmkII_paged_load, jtagmkII_paged_write): fix bug
	in memory type calculation for Xmega "boot" memory region.

2012-04-25  Joerg Wunsch <<EMAIL>>

	* update.c (parse_op): do not assume default memtype here
	* main.c: after locating the part information, determine default
	memtype for all update options that didn't have a memtype
	specified; this is "application" for Xmega parts, and "flash" for
	everything else.

2012-04-24  Joerg Wunsch <<EMAIL>>

	* fileio.c: Rework the way ELF file sections are considered: while
	scanning the program header table, the offsets from a program
	header entry must never be used directly when checking the bounds
	of the current AVR memory region.  Instead, they must always be
	checked based on the corresponding section's entry.  That way,
	Xmega devices now properly take into account whether the segment
	fits into any of the application/apptable/boot memory region.

2012-04-20  Joerg Wunsch <<EMAIL>>

	bug #30756: When setting SUT to 64ms on XMEGA, avrdude doesn't
	read device signature
	* main.c: When reading the signature yields 0x000000 or 0xffffff,
	retry (up to twice) after some progressive delay.

2012-04-20  Joerg Wunsch <<EMAIL>>

	* avrdude.conf.in (ATxmega16D4, ATxmega32D4, ATxmega64D4,
	ATxmega128D4): New devices.  As Xmega D doesn't feature a fuse0
	memory cell, move that one out from the generic .xmega part into
	the individual Xmega A parts.

2012-04-19  Joerg Wunsch <<EMAIL>>

	bug #29019: pagel/bs2 warning when uploading using stk500 to xmega
	* stk500.c (stk500_initialize): Insert dummy values for PAGEL and
	BS2 if they are not present in the config file, in order to be able
	to proceed with the stk500_set_extended_parms() anyway.

2012-04-19  Joerg Wunsch <<EMAIL>>

	* stk500v2_private.h (struct pdata): add boot_start
	* stk500v2.c: For the "flash" pseudo-memory of Xmega devices,
	distinguish addresses between "application" and "boot" area.

2012-04-18  Joerg Wunsch <<EMAIL>>

	* fileio.c (elf2b): When checking the bounds of the current
	program header segment, subtract `low' from ph[n].p_paddr in order
	to correct the magic section offsets for the AVR's non-flash
	memory regions.

2012-04-18  Joerg Wunsch <<EMAIL>>

	* fileio.c (elf_get_scn): Rather than trying to just match whether
	any given section maps straight to a program header segment, use a
	more sophisticated decision that matches any section as long as it
	fits into the segment.  This is needed for situations where the
	program header segment spans a larger area than the section data
	provided.  (This can e.g. happen in an ELF file that contains no
	data at address 0, like a bootloader only.)

2012-04-13  Joerg Wunsch <<EMAIL>>

	bug #28744: Can't load bootloader to xmega128a1 (part 2, fix for
	firmware >= V7.x)
	* jtagmkII.c: Add firmware-version dependent handling of Xmega parameters.
	V7.x firmware expects the NVM offsets being specified through the Xmega
	parameters command, but left out as part of the memory address itself.
	* jtagmkII_private.h: Add CMND_SET_XMEGA_PARAMS, and struct xmega_device_desc.
	* config_gram.y: Add mcu_base keyword.
	* avrpart.h: (Dito.)
	* lexer.l: (Dito.)
	* avrdude.conf.in (.xmega): add mcu_base, and data memory segment.

2012-03-30  Joerg Wunsch <<EMAIL>>

	bug #28744: Can't load bootloader to xmega128a1 (part 1, fix for
	firmware < V7.x)
	* jtagmkII.c: When going to write to the boot section of flash,
	use MTYPE_BOOT_FLASH rather than MTYPE_FLASH
	* jtagmkII_private.h: add MTYPE_BOOT_FLASH constant

2012-03-30  Joerg Wunsch <<EMAIL>>

	* jtagmkII_private.h: Sort commands, response codes and events
	into numerical order.

2012-03-29  Joerg Wunsch <<EMAIL>>

	bug #30451: Accessing some Xmega memory sections gives not
	supported error
	* stk500v2.c: Handle all Xmega memory sections (except
	"prodsig" which is not documented in AVR079)
	* fileio.c: Treat the "boot", "application", and "apptable"
	regions (which are actually subregions of "flash") all as
	being flash, i.e. suppress trailing 0xFF bytes when reading
	them
	* avr.c: (Dito.)

2012-03-20  Joerg Wunsch <<EMAIL>>

	* jtagmkII.c (jtagmkII_close): The GO command before signing off
	turned out to be not required for normal megaAVR devices, and to
	cause the exact opposite (i.e. the target stopping) on Xmega
	devices being programmed to JTAG.  However, programming Xmega
	devcies through PDI *does* need the GO command.

2012-03-20  Joerg Wunsch <<EMAIL>>

	* configure.ac: Print a configuration summary at the end of the
	configure run

2012-02-11  Rene Liebscher <<EMAIL>>

	patch #7718: Merge global data of avrftdi in a private data structure
	* avrftdi.[ch]: moved global data into private data structure, moved
	private defines from header file into source file

2012-02-06  Rene Liebscher <<EMAIL>>

	patch #7720 Bug in EEPROM write
	* avrftdi.c: fixed wrong buffer address initialization in paged_write
	* fileio.c: added #include <stdint.h>

2012-02-05  Rene Liebscher <<EMAIL>>

	bug #30559 Ft232 bit-bang support
	* ft245r.c: cancel reader thread before exiting program

2012-02-04  Rene Liebscher <<EMAIL>>

	patch #7717 avrftdi_flash_write is broken
	* avrftdi.c: fixed wrong buffer address initialization in paged_write
	bug #35296 Extraneous newlines in output.
	* main.c: fixed output of newlines at 100% progress

2012-02-03  Rene Liebscher <<EMAIL>>

	patch #7715 FT4232H support
	* avrdude.conf.in: added programmer 4232h

2012-02-03  Rene Liebscher <<EMAIL>>

	patch #7687: Autogenerating programmers and parts lists for docs
	(generating the programmers lists)
	* doc/avrdude.texi: Add include of generated table of programmers
	* doc/Makefile.am: Add generating of table of programmers in programmers.texi

2012-02-03  Rene Liebscher <<EMAIL>>

	bug #34768 Proposition: Change the name of the AVR32 devices
	* avrdude.conf.in: renamed ucr2 to uc3a0512
	* avrpart.c: added cast to avoid compiler warning

2012-02-03  Joerg Wunsch <<EMAIL>>

	* fileio.c (fileio_elf): Fix a copy'n-paste-o.

2012-02-03  Joerg Wunsch <<EMAIL>>

	* par.c (par_desc): Move to end of file, outside the #if
	HAVE_PARPORT

2012-02-02  Joerg Wunsch <<EMAIL>>

	Implement ELF file reading (finally).  Requires libelf(3) to be
	present on the host system.
	* configure.ac (HAVE_LIBELF): Add logic to detect presence of
	libelf(3)
	* Makefile.am (avrdude_LDADD): Add @LIBELF@
	* fileio.h (FILEFMT): add FMT_ELF
	* fileio.c: Implement ELF file reader.
	* update.c (parse_op): add 'e' format specifier
	* avrdude.1: Document the ELF file reading capability
	* doc/avrdude.texi: (Dito.)

2012-02-01  Rene Liebscher <<EMAIL>>

	bug #30559 Ft232 bit-bang support
	* ft245r.[ch]: new programmer type implementation
	* configure.ac: add pthread as link library
	* avrdude.conf.in: added some new programmers
	* Makefile.am: added new source files to compile
	* pindefs.h: change PIN_MASK, PIN_INVERSE to highest bit of unsigned int
	* pgm.[ch]: added generic function to print pin assignments (taken from par.c)
	* par.c: moved pin assigment print function to pgm.c

2012-02-01  Joerg Wunsch <<EMAIL>>

	* lexer.l: Sort keyword tokens into alphabetic order.

2012-01-31  Rene Liebscher <<EMAIL>>

	* config_gram.y, lexer.l: removed unused ID/TKN_ID definitions
	* config.[hc]: removed unused function id(), use value.type to select
	               values

2012-01-31  Rene Liebscher <<EMAIL>>

	patch #7437 modifications to Bus Pirate module
	patch #7686 Updating buspirate ascii mode to current firmware, use AUX
	            as clock generator, and setting of serial receive timeout
	* buspirate.c: added paged_write, changed binary mode setup/detection,
	  added clock output on AUX pin
	* avrdude.1: updated documentation
	* doc/avrdude.texi: updated documentation

2012-01-31  Rene Liebscher <<EMAIL>>

	Parser does not need to know all programmer types now, new programmers
	will update only the table in pgm_type.c.
	* config_gram.y, lexer.l: removed programmer type keywords,
	use now locate_programmer_type() function
	* pgm_type.[ch]: added new files for table of programmer types
	* main.c: allow list of programmer types by -c ?type
	* avrdude.conf.in: changed all type keywords to quoted strings
	* doc/avrdude.texi: changed description of type definition, list
	of valid types is now included from generated file
	* doc/Makefile.am: generate list of programmer types for doc
	* all programmers [hc]: add xxx_desc string for description of programmer

2012-01-30  Rene Liebscher <<EMAIL>>

	* configure.ac: fixed detection of yylex_destroy availability
	by checking the version number of flex; bump required autoconf
	version to 2.60 (for AC_PROG_SED)

2012-01-30  Joerg Wunsch <<EMAIL>>

	* lexer.l: Replace the old, now-defunct #define YY_NO_UNPUT by
	the new %option nounput.  This gets rid of a compiler warning.

2012-01-30  Joerg Wunsch <<EMAIL>>

	Add a connection_type attribute to each programmer, rather than
	trying to hard-code the default port name in main.c.
	* pgm.h: Add conntype to struct pgm.
	* lexer.l: Extend grammar for connection_type.
	* config_gram.y: (Dito.)
	* config.h: Add DEFAULT_USB, for symmetry with default_parallel
	and default_serial.
	* main.c: Replace old default portname hack by avrdude.conf-based
	knowledge.
	* usbtiny.c: Drop an old hack that's no longer necessary.
	* avrdude.conf.in: Add connection_type to each programmer
	definition.

2012-01-27  Rene Liebscher <<EMAIL>>

	* avrdude.conf.in: used parent parts for some other parts, added 
	abstract .xmega part as parent for xmegas
	* main.c: hide parts starting with '.' from parts list

2012-01-22  Rene Liebscher <<EMAIL>>

	patch #7688: Implement parent programmers feature
	* avrdude.conf.in: updated documentation comment and some programmers 
	have now parents
	* config_gram.y: initpgm will now called at first use of programmer 
	in main. parser sets only the function pointer in the pgm structure.
	Pin and pin lists definitions can now be empty to remove the parents
	setting.
	* doc/avrdude.texi: updated documentation
	* main.c: added call to pgm->initpgm after locate_programmer
	* pgm.[hc]: added field initpgm in structure, added function pgm_dup

2012-01-21  Rene Liebscher <<EMAIL>>

	bug #21797: AT90PWM316: New part description
	* avrdude.conf.in: added pwm316 with parent pwm3b but 16KB flash

2012-01-20  Joerg Wunsch <<EMAIL>>

	* configure.ac: Check for presence of lusb_usb.h as an alternative
	to usb.h; libusb-win32 switched to this name in version *******.
	* avrftdi.c: Decide whether to include <usb.h>, or <lusb0_usb.h>.
	* ser_avrdoper.c: (Dito.)
	* usbasp.c: (Dito.)
	* usb_libusb.c: (Dito.)
	* usbtiny.c: (Dito.)

2012-01-19  Rene Liebscher <<EMAIL>>

	* avr.c: Unsigned variable was used for return code of paged_write/load
	functions. So a negative return code led never to a fallback to byte
	functions.

2012-01-17  Rene Liebscher <<EMAIL>>

	bug #34302: Feature request : device configuration with parent classes
	* config_gram.y: if memory section is overwritten old entry is removed
	
	(not in original patch)
	* config_gram.y: if programmer or part is defined twice, a warning is
	output and the first instance is removed
	
	General cleanup and free functions, so valgrind does not report any lost
	blocks at program end.
	* avrpart.[hc]: added avr_free_(opcode|mem|part) functions
	* pgm.[hc]: added pgm_free function
	* update.[hc]: added free_update functions
	* config.[hc]: added cleanup_config function, use yylex_destroy to reset
	the lexer after usage. (So it can be reused.)
	* main.c: add cleanup_main function which is called by atexit() (This 
	frees all lists so that at program exit only really lost memory is 
	reported by valgrind.)
	* usbasp.c: added libusb_free_device_list() and libusb_exit() calls to
	avoid lost memory
	* buspirate.c: moved memory allocation from initpgm to setup and added 
	free in teardown
	* configure.ac: add definition of HAVE_YYLEX_DESTROY if $LEX is flex.
	* Makefile.am: added . in front of SUBDIRS to build avrdude before trying
	to use it for creating the part list for the docs.

2012-01-17  Rene Liebscher <<EMAIL>>

	* usbasp.c: USB vid/pid/vendor/product from config file are used, for
	id "usbasp" nibobee and old usbasp are tried as they were currently
	implemented within usbasp
	* avrdude.conf.in: added usb params to "usbasp", added new entry "nibobee"
	with params which were hardcoded in usbasp.c, and added an entry
	"usbasb-clone" which only checks vid/pid.

2012-01-10  Rene Liebscher <<EMAIL>>

	bug #35261 avrftdi uses wrong interface in avrftdi_paged_(write|load)
	* avrftdi.c: Fixed interface and implementation of avrftdi_paged_(write|load)
	patch #7672 adding support for O-Link (FTDI based JTAG) as programmer
	* avrdude.conf.in: added o-link entry

2012-01-10  Rene Liebscher <<EMAIL>>

	patch #7699 Read additional config files
	* main.c: Added reading of additional config files
	* avrdude.1: updated man page
	* doc/avrdude.texi: updated documentation

2012-01-10  Joerg Wunsch <<EMAIL>>

	Submitted by Bob Frazier:
	bug #35208: avrdude 5.11 on freebsd 8.2-STABLE does not reset
	Arduino Uno properly
	* arduino.c (arduino_open): Bump the timeout between pulling
	the DTR and RTS lines low and high.

2012-01-08  Rene Liebscher <<EMAIL>>

	Fixed following findings reported by cppcheck
	* avr910.c:625 (error) Possible null pointer dereference: cmd - otherwise it is redundant to check if cmd is null at line 624
	* avr910.c:626 (error) Possible null pointer dereference: cmd - otherwise it is redundant to check if cmd is null at line 624
	* avr910.c:168 (information) The scope of the variable 'devtype_1st' can be reduced
	* avr910.c:169 (information) The scope of the variable 'dev_supported' can be reduced
	* avrftdi.c:647 (error) Using sizeof for array given as function argument returns the size of pointer.
	* stk500v2.c:3347 (error) Memory leak: b
	* stk500v2.c:3452 (error) Memory leak: b
	* usbasp.c:554 (error) Using sizeof for array given as function argument returns the size of pointer.
	* usbasp.c:485 (information) The scope of the variable 'dly' can be reduced

2012-01-03  Joerg Wunsch <<EMAIL>>

	Reported by Jason Kotzin:
	* usbasp.c (usbasp_spi_paged_load, usbasp_spi_paged_write):
	Fix buffer address calculation.

2012-01-03  Rene Liebscher <<EMAIL>>

	patch #7629 add support for atmega48p
	* avrdude.conf.in: Added m48p with parent m48 + different signature
	
	* avrdude.conf.in: made part parents (m88p = m88 + different signature,
	m168p = m168 + different signature)

2012-01-02  Rene Liebscher <<EMAIL>>

	bug #21663 AT90PWM efuse incorrect
	bug #30438 efuse bits written as 0 on at90pwmxx parts
	* avrdude.conf.in: (pwm2, pwm2b, pwm3, pwm3b) <efuse.write>: Write 
	eight bits
	
	* avrdude.conf.in: made part parents (pwm3 = pwm2, pwm3b = pwm2b, 
	pwm2b = pwm2 + different signature)
	
	* ChangeLog-2011: New file, rotate ChangeLog for new year.
