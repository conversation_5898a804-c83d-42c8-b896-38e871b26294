2013-12-15  <PERSON><PERSON> <<EMAIL>>

	* pgm.c/pgm.h: fixed syntax error in const pointer to const

2013-12-05  <PERSON><PERSON> <<EMAIL>>

	* configure.ac: bump version to 6.1-svn-20131205

2013-12-05  <PERSON><PERSON> <<EMAIL>>

	bug #40817: Elf file support (possibly) not working on 6.0.1 windows build
	* fileio.c (fileio): open file in binary mode also for FMT_ELF

2013-12-04  <PERSON> <<PERSON><PERSON>@gmx.de>

	Rework of bitbanging functions setpin, getpin, highpulsepin to make simplier use
	of new pindefs data in pgm structure
	* linuxgpio.c, bitbang.c, buspirate.c, par.c, pgm.h, term.c, serbb_*.c: changed 
	  interface of setpin, getpin, highpulsepin to take pin function as parameter 
	  (not the real number, which can be found by pgm->pinno[function])

2013-11-30  <PERSON> <<PERSON><PERSON>@gmx.de>

	bug #40748 linuxgpio doesn't work on Raspberry PI rev. 2.
	* linuxgpio.c: fixed check for unused pins to ignore the inverse flag 
	* pindefs.c: fixed fill_old_pinlist to not create an empty mask with inverse flag set

2013-10-18  Nils Springob <<EMAIL>>

	* avrdude.conf.in (atmega1284): ATmega1284 variant added (same as ATmega1284p but with different signature)

2013-09-25  Hannes Weisbach <<EMAIL>>

	First part of patch #7720:
	* avrdude.conf.in: Add UM232H and C232H programmers

2013-09-22  Joerg Wunsch <<EMAIL>>

	Submitted by Daniel Rozsnyo:
	bug #40085: Typo fix in fuses report (for 6.1-svn-20130917)
	* main.c: Fix a typo.

2013-09-19  Hannes Weisbach <<EMAIL>>

	task #12798: Please cleanup #ifdef notyet entries in avrftdi.c
	* avrftdi.c: ditto.
	avrftdi.c: Remove DRYRUN-option.

2013-09-17  Joerg Wunsch <<EMAIL>>

	bug #40055: AVRDUDE segfaults when writing eeprom
	* main.c: Always clear the UF_AUTO_ERASE flag if either a
	non-Xmega device was found, or the programmer does not offer a
	page_erase method.

2013-09-17  Joerg Wunsch <<EMAIL>>

	* configure.ac (AC_INIT): Bump version to post-6.0.

2013-09-17  Joerg Wunsch <<EMAIL>>

	* configure.ac (AC_INIT): Bump version to 6.0.

2013-09-17  Joerg Wunsch <<EMAIL>>

	* jtag3.c (jtag3_initialize): Fix a buffer overflow by limiting
	the flash page cache size to at most "readsize".  For Xmegas with
	a page size of 512 bytes, the maximum USB packet size was
	overflowed, and subsequently, a memmove copied beyond the end of
	the allocated buffer.
	* jtag3.c (jtag3_read_byte): Add the correct offset also for the
	various flash regions, so reading the apptable or boot regions
	yields the correct data.

2013-09-16  Joerg Wunsch <<EMAIL>>

	Submitted by Joakim Lubeck:
	bug #40040: Support for ATtiny20 and ATtiny40
	* avrdude.conf.in: Restructure the reduced-core tiny devices
	to use a common entry .reduced_core_tiny; add ATtiny20 and
	ATtiny40

2013-09-15  Joerg Wunsch <<EMAIL>>

	Submitted by Joakim Lubeck:
	bug #40033: Support for the XMegaE5 family
	* avrdude.conf.in (ATxmega8E5, ATxmega16E5, ATxmega32E5): New
	entries.

2013-09-13  Joerg Wunsch <<EMAIL>>

	* stk500v2.c (stk500v2_set_sck_period): Revamp this to match the
	description/pseudo-code in appnote AVR068.

2013-09-13  Joerg Wunsch <<EMAIL>>

	Submitted by Stephen Roe:
	patch #7710: usb_libusb: Check VID/PID before opening device
	* usb_libusb.c (usbdev_open): Swap the sequence of verifying the
	VID:PID, and opening the device.

2013-09-13  Joerg Wunsch <<EMAIL>>

	patch #8176: butterfly.c (AVR109 protocol implementation) clean-up and bug-fixing
	* butterfly.c (butterfly_page_erase): Add dummy function to avoid
	segfault when writing to EEPROM.

2013-09-13  Joerg Wunsch <<EMAIL>>

	bug #35474 Feature request: print fuse values in safemode output
	* config_gram.y: New configuration token "default_safemode".
	* lexer.l: (Dito.)
	* avrdude.conf.in: (Dito.)
	* config.h: Add variable default_safemode.
	* config.c: (Dito.)
	* main.c: Handle default_safemode, including -u option.
	* avrdude.1: Document all this.
	* doc/avrdude.texi: (Dito.)

2013-09-13  Joerg Wunsch <<EMAIL>>

	Submitted by HubertB:
	patch #7657 Add ATmega406 support for avrdude using DRAGON + JTAG
	* avrdude.conf.in (ATmega406): New entry.

2013-09-13  Joerg Wunsch <<EMAIL>>

	Submitted by Marc de Hoop:
	patch #7606 ATtiny43u support
	* avrdude.conf.in (ATtiny43U): New entry.

2013-09-13  Joerg Wunsch <<EMAIL>>

	patch #5708 avrdude should make 10 synchronization attempts instead of just one
	* stk500.c (stk500_getsync): Loop 10 times trying to get in
	sync with the programmer.

2013-09-13  Joerg Wunsch <<EMAIL>>

	Contributed by Ricardo Martins:
	bug #36384 ATxmega32A4 usersig size
	* avrdude.conf.in: Revamp all the ATxmega* entries.  Add new
	entries for ATxmega128A1U, ATxmega128A3U, ATxmega128A4U,
	ATxmega128B1, ATxmega128B3, ATxmega128C3, ATxmega128D3,
	ATxmega16A4U, ATxmega16C4, ATxmega192A3U, ATxmega192C3,
	ATxmega192D3, ATxmega256A3BU, ATxmega256A3U, ATxmega256C3,
	ATxmega256D3, ATxmega32A4U, ATxmega32C4, ATxmega384C3,
	ATxmega384D3, ATxmega64A1U, ATxmega64A3U, ATxmega64A4U,
	ATxmega64B1, ATxmega64B3, ATxmega64C3, ATxmega64D3

2013-09-13  Joerg Wunsch <<EMAIL>>

	bug #35456 The progress bar for STK500V2 programmer is "wrong".
	* avr.c (avr_read, avr_write): Change the progress reporting for
	paged read/write from per-address to per-considered-page.  This
	ought to give a realistic estimation about the time still to be
	spent.

2013-09-13  Joerg Wunsch <<EMAIL>>

	bug #34277: avrdude reads wrong byte order if using avr911 (aka butterfly)
	* butterfly.c (butterfly_read_byte_flash): Swap bytes received.

2013-09-12  Joerg Wunsch <<EMAIL>>

	bug #37768 Poll usbtiny 100 times at init time to handle low-clock devices
	* doc/avrdude.texi: Add a FAQ entry about how to connect to a
	target where the firmware has reduced the internal clock speed.

2013-09-11  Joerg Wunsch <<EMAIL>>

	bug #28344 chip_erase_delay too short for ATmega324P, 644, 644P, and 1284P
	* avrdude.conf: Bump the chip_erase_delay for all ATmega*4 devices
	to 55 ms.  While the datasheet still claims 9 ms, all the XML files
	tell either 45 or 55 ms, depending on STK600 or not.

2013-09-11  Joerg Wunsch <<EMAIL>>

	* fileio.c (fileio): Don't exit(1) if something goes wrong; return
	-1 instead.  Don't refer to obsolete option -f to specify the file
	format.

2013-09-10  Joerg Wunsch <<EMAIL>>

	Submitted by Matthias Trute:
	bug #36901 flashing Atmega32U4 EEPROM produces garbage on chip
	* avrdude.conf.in (ATmega32U4): Fix EEPROM pagesize to 4, the
	datasheet is wrong here.

2013-09-09  Joerg Wunsch <<EMAIL>>

	* configure.ac: check for ar and ranlib in the target tool
	namespace, rather than on the host.

2013-09-08  Joerg Wunsch <<EMAIL>>

	Fix byte-wise EEPROM and flash writes on Xmega
	* jtagmkII_private.h (MTYPE_EEPROM_XMEGA): New memory type.
	* jtagmkII.c (jtagmkII_write_byte): For Xmega EEPROM, use
	memory type MTYPE_EEPROM_XMEGA; for flash writes, always
	write 2 bytes starting on an even address.

2013-09-08  Joerg Wunsch <<EMAIL>>

	* term.c: Implement the "verbose" terminal mode command.
	* avrdude.1: Document this.
	* doc/avrdude.texi: (Dito.)

2013-09-07  Joerg Wunsch <<EMAIL>>

	* jtag3.c (jtag3_write_byte): Do not attempt to start the paged
	algorithm for EEPROM when being connected through debugWIRE.

2013-09-06  Joerg Wunsch <<EMAIL>>

	Extend the single-byte algorithm to all devices, both flash and
	EEPROM.  (Flash cells must have been erased before though.)
	* jtag3.c (jtag3_initialize): OCDEN no longer needs to be
	considered; a session with "programming" purpose is sufficient
	* jtag3.c (jtag3_write_byte): Use the paged algorithm for all
	flash and EEPROM areas, not just Xmega.

2013-09-05  Joerg Wunsch <<EMAIL>>

	Fix single-byte EEPROM updates on Xmega:
	* jtag3_private.h (MTYPE_EEPROM_XMEGA): New define.
	* jtag3.c (jtag3_write_byte): When updating flash or
	EEPROM on Xmega devices, resort to jtag3_paged_write()
	after filling and modifying the page cache.
	* jtag3.c (jtag3_paged_write): use MTYPE_EEPROM_XMEGA
	where appropriate.
	* jtag3.c (jtag3_initialize): Open with debugging intent
	for Xmega devices, so single-byte EEPROM updates will
	work.

2013-09-04  Joerg Wunsch <<EMAIL>>

	Submitted by Matthias Neeracher:
	bug #38732: Support for ATtiny1634
	* avrdude.conf.in (ATtiny1634): New entry.

2013-09-03  Joerg Wunsch <<EMAIL>>

	Submitted by Brane Ždralo:
	patch #7769: Write flash fails for AVR910 programmers
	* avr910.c (avr910_paged_write): Fix flash addresses in
	'A' command.

2013-09-03  Joerg Wunsch <<EMAIL>>

	Submitted by Fred (magister):
	bug #38951: AVR109 use byte offset instead of word offset
	patch #8045: AVR109 butterfly failing
	* butterfly.c (butterfly_paged_load, butterfly_paged_write):
	fix calculation of 'A' address when operating on flash memory.
	It must be given in terms of 16-bit words rather than bytes.

2013-09-03  Rene Liebscher <<EMAIL>>

	* avrftdi.c, avrftdi_private.h: added tx buffer size, and use
	smaller block sizes as larger sometimes hang

2013-09-03  Joerg Wunsch <<EMAIL>>

	* avrdude.h: Remove the erase cycle counter (options -y / -Y).
	* avr.c: (Dito.)
	* main.c: (Dito.)
	* avrdude.1: Undocument -y / -Y.
	* doc/avrdude.texi: (Dito.)

2013-09-03  Joerg Wunsch <<EMAIL>>

	bug #39691 Buffer overrun when reading EEPROM byte with JTAGICE3
	* jtag3.c (jtag3_initialize): initialize the eeprom_pagesize
	private attribute so the page cache will actually be usable

2013-09-03  Joerg Wunsch <<EMAIL>>

	bug #38580 Current svn head, xmega and fuses, all fuses tied to fuse0
	* jtag3.c (jtag3_read_byte, jtag3_write_byte): Correctly apply the
	relevant part of mem->offset as the address to operate on.

2013-09-03  Joerg Wunsch <<EMAIL>>

	* fileio.c: Fix "unused variable" warnings.
	* avr.c: (Dito.)
	* stk500v2.c: (Dito.)
	* stk500.c: (Dito.)
	* jtagmkII.c: (Dito.)
	* term.c: (Dito.)
	* ser_posix.c: (Dito.)

2013-09-02  Joerg Wunsch <<EMAIL>>

	Submitted by Travis Griggs:
	bug #38307: Can't write usersig of an xmega256a3
	* stk500v2.c (stk600_xprog_page_erase): allow erasing the usersig space.

2013-09-02  Joerg Wunsch <<EMAIL>>

	Submitted by Robert Niemi:
	bug #35800: Compilation error on certain systems if parport is disabled
	* linux_ppdev.h: Conditionalize inclusion of <linux/parport.h> and
	<linux/ppdev.h> on HAVE_PARPORT

2013-09-02  Joerg Wunsch <<EMAIL>>

	bug #39794: warnings when building avrdude 6.0rc1 under CentOS 6.4
	* pickit.c (usb_open_device): Use %p rather than %X to print "handle"
	which is a pointer
	* jtag3.c (jtag3_initialize): Initialize "flashsize" to be sure it
	proceeds with a valid value.

2013-09-02  Joerg Wunsch <<EMAIL>>

	bug #39794: warnings when building avrdude 6.0rc1 under CentOS 6.4
	* buspirate.c: Turn the "cmd" argument of the various methods into
	a "const unsigned char *"; while doing this, declare all arrays being
	passed as arguments to be pointers rather than arrays, as the latter
	obfuscates the way arrays are being passed to a callee in C.
	* avrftdi.c: (Dito.)
	* pickit2.c: (Dito.)
	* ft245r.c: (Dito.)
	* avr910.c: (Dito.)
	* stk500.c: (Dito.)
	* bitbang.c: (Dito.)
	* bitbang.h: (Dito.)
	* avrftdi_tpi.c: (Dito.)
	* avrftdi_tpi.h: (Dito.)
	* usbasp.c: (Dito.)
	* stk500v2.c: (Dito.)
	* pgm.h: (Dito.)
	* usbtiny.c: (Dito.)

2013-09-02  Joerg Wunsch <<EMAIL>>

	bug #38023: avrdude doesn't return an error code when attempting
	to upload an invalid Intel HEX file
	* fileio.c (ihex2b): Turn the "No end of file record found" warning
	into an error if no valid record was found at all.

2013-09-02  Joerg Wunsch <<EMAIL>>

	Submitted by Claus-Justus Heine:
	bug #38713: Compilation of the documentation breaks with texinfo-5
	* doc/avrdude.texi: Turn @itemx into @item, add @headitem to STK600
	Routing/Socket card table

2013-09-02  Joerg Wunsch <<EMAIL>>

	* usbasp.c: Add trace output for -vvv to non-TPI functions, too.

2013-09-01  Joerg Wunsch <<EMAIL>>

	* usbasp.c (usbasp_tpi_paged_load): Calculate correct
	buffer address.
	* usbasp.c (usbasp_tpi_paged_write): Calculate correct
	buffer address; don't issue a SECTION_ERASE command for
	each page (a CHIP_ERASE has been done before anyway);
	remove the code that attempted to handle partial page
	writes, as all writes are now done with a full page.

2013-09-01  Joerg Wunsch <<EMAIL>>

	* usbasp.c: Add more trace output, by now only for the TPI
	functions.

2013-08-31  Joerg Wunsch <<EMAIL>>

	* usbasp.c (usbasp_transmit): Add -vvvv trace output.

2013-08-30  Joerg Wunsch <<EMAIL>>

	bug #39893: Verification failure with AVRISPmkII and Xmega
	* stk500v2.c (stk600_xprog_page_erase): Fix argument that is
	passed to stk600_xprog_memtype()

2013-07-11  Joerg Wunsch <<EMAIL>>

	* fileio.c (elf2b): replace elf_getshstrndx() by
	elf_getshdrstrndx() as the former one is deprecated

2013-06-19  Rene Liebscher <<EMAIL>>

	use bitbanging on ftdi mpsse when wrong pins are used
	* avrftdi.c, avrftdi_private.h: added additional pin check 
	and bitbanging fallback
	* pindefs.[ch]: added a flag to enable/disable output
	* ft245r.c: changes because of added flag above

2013-05-17  Joerg Wunsch <<EMAIL>>

	Submitted by "Malte" and John McCorquodale:
	patch #7876 JTAGICE mkII fails to connect to attiny if debugwire
	is enabled AND target has a very slow clock
	* jtagmkII.c (jtagmkII_getsync): When leaving debugWIRE mode
	temporarily, immediately retry with ISP, rather than leaving.
	* stk500v2 (stk500v2_program_enable): Implemented similar logic
	for the JTAGICE3.

2013-05-16  Rene Liebscher <<EMAIL>>

	* configure.ac: reactivate check for TYPE_232H, which does not
	exist in libftdi < 0.20
	* avrftdi*.*: changed include check for libftdi/libusb, deactivate
	232H if not available
	* ft245r.c: changed include check for libftdi/libusb

2013-05-08  Joerg Wunsch <<EMAIL>>

	* main.c (main): Add option -l logfile.
	* avrdude.1: Document -l option.
	* doc/avrdude.texi: (Dito.)

2013-05-15  Rene Liebscher <<EMAIL>>

	* configure.ac: if both found libftdi and libftdi1 use only libftdi1
	* avrdude.conf.in: fixed buff pins of avrftdi programmers (low
	active buffer need now inverted numbers)
	* avrftdi*.*: accept also old libftdi (0.20 still works with it),
	added powerup to initialize
	* ft245r.c: accept libftdi1, code cleanup and make it more similar
	to avrfdti (os they might be merged someday)

2013-05-08  Joerg Wunsch <<EMAIL>>

	* configure.ac (AC_INIT): Bump version to 6.0rc1.

2013-05-07  Hannes Weisbach <<EMAIL>>

	* avrftdi_private.h: Change size of pin_checklist to N_PINS (from N_PINS-1)
	* avrftdi.c: Adapt code to new size of pin_checklist. Remove pins_check()
	from set_pin().
	Add pgm->power[up|down] functions as well as fill pgm->enable|disable with
	proper content as suggested by Rene Liebscher.

2013-05-05  Rene Liebscher <<EMAIL>>

	* pindefs.h: use unsigned int if stdint.h is not available and UINT_MAX is 0xffffffff
	otherwise use unsinged long
	* ft245r.c: added support for more pin functions led, vcc, buff

2013-05-06  Hannes Weisbach <<EMAIL>>

	* avrftdi_tpi.c: instead of private set_pin() function pointer use the one
	declared in struct PROGRAMMER.
	* avrftdi_private.h: remove set_pin function pointer. Add pin_checklist_t
	member to check pgm->setpin calls during runtime.
	* avrftdi.c: remove set_pin function pointer init, add pgm->setpin init.
	Convert avrftdi to new 0-based pindefs infrastructure.
	* avrdude.conf.in: Change all avrftdi-based programmers' pin definitions to
	0-based.

2013-05-06  Joerg Wunsch <<EMAIL>>

	* pindefs.h: Include "ac_cfg.h" before testing for HAVE_* macros.

2013-05-05  Rene Liebscher <<EMAIL>>

	* main.c: revert to rev 1159 (doing pgm_display after pgm_open)
	* avrpart.[ch]: moved avr_pin_name to pindefs.[ch]
	* pgm.c: moved pins_to_str to pindefs.[ch], added initialization of 
          new pin definitions in pgm_new()
	* pindefs.[ch]: added moved functions from other files, added a lot of 
          documentation, reformatted files using astyle to have consistent spacing,
          added a new generic check function for pins
	* ft245r.c: used new generic pin check function

2013-05-03  Rene Liebscher <<EMAIL>>

	Create new pin definition data structures to support 0-based pin numbers,
	and mixed inverse/non-inverse pin lists.
	* avrftdi.c,buspirate.c,linuxgpio.c,par.c,serbb_*.c: added function call 
          to fill old pinno entries from new pin definitions.
	* pindefs.[hc]: added data struct and helper functions for new pin definitions
	* avrdude.conf.in: pins in entries using ftdi_syncbb are now 0-based
	* config_gram.y: allow combinations of inverted and non-inverted pins in pin lists
	* ft245r.c: reworked to work directly with the new pin definitions,
          pins are now 0-based, inverse pins are supported, buff is supported
	* pgm.[ch]: added new pin definitions field to programmer structure, 
          adapted pin display functions

2013-05-03  Hannes Weisbach <<EMAIL>>

	* avrftdi_private.h: Remove update forward declaration from avrftdi_print to
	avrftdi_log.
	* avrftdi_tpi.c: Do all I/O in terms of pgm->cmd_tpi()-calls instead of
	avrftdi_tpi_[read,write]_byte().
	Remove unnecessary set_pin call to set MOSI high, speeds up I/O.
	Removes SKEY array, moves it to tpi.h.
	Integrate new avr_tpi_[program_enable,chip_erase]() and functions into
	avrftdi_tpi.
	* avrftdi_tpi.h: Remove avrftdi_tpi_[program_enable,chip_erase] forward
	declarations.
	* avr.c: Adds avr_tpi_chip_erase() generic TPI chip erase function.
	Adds avr_tpi_program_enable() - generic TPI external programming enable
	function. Sets guard time, reads identification register, sends SKEY command
	and key, checks NVMEN bit. The required guard time has to be passed as
	parameter.
	* tpi.h: Adds SKEY array including CMD_SKEY in "correct" order.

2013-05-02  Hannes Weisbach <<EMAIL>>

	* avrftdi_private.h: Add libusb-1.0 include to fix include order in windows.
	* NEWS: Add notice avrftdi supporting TPI
	* avr.c: Fix avr_tpi_poll_nvmbsy() - poll read data instead of return code
	* avrftdi_private.h, avrftdi.c: move logging #defines to from avrftdi.c to
	avrftdi_private.h, so that they are available for avrftdi_tpi, too.

2013-04-30  Hannes Weisbach <<EMAIL>>

	* tpi.h: Add definition for TPI Identification Code
	* avrftdi_tpi.c: Add TPI-support for FTDI-based programmers
	* avrftdi_private.h: Add common include file for FTDI-based programmers

2013-04-28  Hannes Weisbach <<EMAIL>>

	* avrftdic: Rework of textual output. Messages are divided by severity and
	printed accordingly to the verbosity,	as specified by the user. The provided
	severity level are (ERROR, WARN, INFO, DEBUG, TRACE). Where "ERROR" messages
	are always printed. Shortcut-macros	including function, from which the
	output was generated, and line number were also added.
	Some log messages were updated and other code warnings removed.

2013-04-27  Hannes Weisbach <<EMAIL>>

	* configure.ac: Add libftdi1 library check, remove TYPE_232H DECL check
	* Makefile.am: Add @LIBFTDI1@ to avrdude_LDADD
	* avrftdi.c: Update from libftdi0 to libftdi1. Use libftdi1's function to
	find a device by vid/pid/serial instead of doing it ourself and add/update
	error messages. avrftdi_print is changed so that a message is printed when
	the verbosity level is greater or equal the message level, to have always-on
	messages.
	Fix a bug where the RX fifo of the FTDI chip is full, resulting in STALL/NAK
	of the ongoing OUT request and subsequently timeout, because an IN request
	cannot be issued due to the synchronous part of libftdi. This should fix
	#38831 and #38659.

2013-04-25  Joerg Wunsch <<EMAIL>>

	* configure.ac(AC_CONFIG_HEADERS): Replace the old AM_CONFIG_HEADER
	by this; automake 1.13+ barfs.

2013-03-12  Joerg Wunsch <<EMAIL>>

	* avrdude.conf.in (ATmega2564RFR2, ATmega1284RFR2, ATmega644RFR2):
	New devices

2013-01-30  Rene Liebscher <<EMAIL>>

	patch #7724 Add TPI support for Bus Pirate using bitbang mode
	* buspirate.[ch]: added support for BusPirate Bitbanging
	* pgm_type.c: added entry for buspirate_bb
	* avrdude.conf.in: added entry for buspirate_bb

2013-01-30  Rene Liebscher <<EMAIL>>

	patch #7936 Patch to support BusPirate AVR Extended Commands mode
	* buspirate.c: added support for BusPirate AVR Extended Commands mode
	* avrdude.1: added doc for nopagedread parameter
	* doc/avrdude.texi: added doc for nopagedread parameter

2013-01-30  Rene Liebscher <<EMAIL>>

	patch #7723 Bus Pirate “raw-wire” mode which can run down to 5 kHz
	* buspirate.c: added raw wire mode
	* avrdude.1: added doc for rawfreq parameter
	* doc/avrdude.texi: added doc for rawfreq parameter

2013-01-30  Rene Liebscher <<EMAIL>>

	bug #37977 Support for Openmoko Debug Board
	* avrdude.conf.in: added openmoko entry

2013-01-29  Rene Liebscher <<EMAIL>>

	patch #7932 Read USBtiny VID and PID from avrdude.conf if provided.
	* avrdude.conf.in: added usbpid, usbvid to usbtiny
	* usbtiny.[ch]: use usbpid, usbpid if provided in config file

2013-01-26  Joerg Wunsch <<EMAIL>>

	bug #38172: avrftdi: Incorrect information in avrdude.conf
	* avrdude.conf.in (avrftdi): fix comments about ACBUS vs. ADBUS;
	add a comment that the MPSSE signals are fixed by the FTDI
	hardware and cannot be changed

2013-01-09  Rene Liebscher <<EMAIL>>

	patch #7165 Add support for bitbanging GPIO lines using the Linux sysf GPIO interface
	* doc/avrdude.texi,avrdude.1: added doc for linuxgpio 
	* avrdude.conf.in: added template for linuxgpio programmer
	* config_gram.y: pin numbers restricted to [PIN_MIN, PIN_MAX]
	* pindefs.h: added PIN_MIN, PIN_MAX, removed unused LED_ON/OFF
	* configure.ac: configure option enable-linuxgpio, print of enabled options
	* linuxgpio.[ch]: new source for linuxgpio programmer
	* Makefile.am: added linuxgpio to sources list
	* pgm_type.c: added linuxgpio to programmer types list

2013-01-08  Joerg Wunsch <<EMAIL>>

	* jtagmkI.c (jtagmkI_prmsg): replace a putchar() by putc(...stderr)
	* jtagmkII.c (jtagmkII_prmsg): (Dito.)
	* jtag3.c (jtag3_prevent, jtag3_prmsg): (Dito.)

2013-01-02  Joerg Wunsch <<EMAIL>>

	* usb_libusb.c (usbdev_open): Downgrade the max transfer size for
	the main data endpoints when being forced so by the USB; this can
	happen when attaching the JTAGICE3 to a USB 1.1 connection
	* jtag3.c (jtag3_initialize): When detecting a downgraded max
	transfer size on the JTAGICE3 (presumably, due to being connected
	to USB 1.1 only), bail out as its firmware cannot properly handle
	this (by now)

2013-01-02  Joerg Wunsch <<EMAIL>>

	* ChangeLog: annual ChangeLog rotation time
