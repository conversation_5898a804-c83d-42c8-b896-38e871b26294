THIS IS A PRUSA3D BRANCH, WORKING AROUND A SPECIFIC PROBLEM 
IN THE EARLY I3 MK2 USB COMMUNICATION CHIPS.

Some of the early Prusa3D i3 MK2 printers were shipped with a buggy
USB communication controller firmware. This fork of avrdude contains
a workaround inside the stk500v2 protocol implementation. 

The workaround depends on a specific behavior of the Arduino AVR 2560
bootloader, which is installed on the i3 MK2 printers:

https://github.com/arduino/Arduino-stk500v2-bootloader

The avrdude binary modified by <PERSON>rusa3<PERSON> could replace the avrdude bianary
of arduino to program the RAMBo board. In that case the modified binary
is identified by a "-prusa3d" suffix to the version information.

-------------------------------------------------------------------

See the documentation file for the details.

The latest version of AVRDUDE is always available here:

  http://savannah.nongnu.org/projects/avrdude


Important environment variables for ./configure:
================================================

CPPFLAGS: C preprocessor flags (*not* "C++")

This is the place to put additional (non-standard) -I options into.
For example, if your Windows system has LibUSB-Win32 installed into
\\WINDOWS\ProgramFiles\LibUSB-Win32, use

CPPFLAGS=-I/WINDOWS/ProgramFiles/LibUSB-Win32/include

to tell configure where to search for the header files.  (The use of
forward slashes rather than backslashes can often simplify things.
Note that the Windows system services internally treat both the same.
It's only cmd.exe which requires backslashes as the directory
separator.)

LDFLAGS: Linker options

This is the place to make additional library locations known to the
linker.  To continue the above example, use

LDFLAGS=-L/WINDOWS/ProgramFiles/LibUSB-Win32/lib/gcc

to make the linker search for "libusb.a" in that directory.


Linux users: make sure the header files are installed
=====================================================

While many Linux distributions install the libraries needed by AVRDUDE
(libusb, libelf) by default, they leave out the corresponding header
files.  Consequently, the configure script won't find them, so these
libraries could not be used.

Usually, the packages with the header files (and static libraries) are
derived from the regular package name by appending "-devel".  Thus,
make sure you have "libusb-devel" and "libelf-devel" installed before
running the configure script.  (Same goes for libftdi.)
