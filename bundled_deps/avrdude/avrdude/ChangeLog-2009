2009-11-09  <PERSON> <<EMAIL>>

	* fileio.c: ihex2bin did not properly handle files > 64K bytes
	* usb_libusb.c: re-enabled usb_reset for Macs (no reset causes lots of failures)
	* avrdude.1: spacing issue for avr32 fixed.

2009-11-09  <PERSON><PERSON>  <<EMAIL>>

	* buspirate.c: Implemented reset= and speed= extended parameters.
	* avrdude.1: Document the change.

2009-11-04  <PERSON><PERSON>  <<EMAIL>>

	* configure.ac, Makefile.am: Test if GCC accepts -Wno-pointer-sign

2009-11-04  <PERSON><PERSON>vi<PERSON>  <<EMAIL>>

	* buspirate.c: Implemented 'BinMode' support for
	  firmware 2.7 and higher.
	* avrdude.1: Added info about BusPirate.

2009-11-03  Michal <PERSON>dvig  <<EMAIL>>

	* arduino.c: Add on to bug #26703 / patch #6866 - clear DTR/RTS
	  when closing the port.
	* Makefile.am: Silent warnings about signedness - they're useless
	  and annoying, especially for 'char' vars.

2009-10-22  <PERSON> <<EMAIL>>

	* usb_libusb.c: disabled usb_reset for Macs (same as FreeBSD)

2009-10-12  Michal Ludvig  <<EMAIL>>

	* main.c: Re-added default to serial port for BusPirate.

2009-10-12  David Hoerl <<EMAIL>>

	*  main.c: removed some avr32 code that was pushed into jtagmkII.c
	*  jtagmkII.c: consolodated the avr32 reset code and avr32_chipreset
	*  avrpart.h: modified AVRPART flags for avr32
	*  lexer.l: added is_avr32 flag - only way to get yacc code to set flag
	*  avrdude.conf.in: updated avr32 section to include "is_avr32" flag

2009-10-12  David Hoerl <<EMAIL>>

	*  config_gram.y: Restored inadvertantly removed buspirate entry
	*  lexer.l: Restored inadvertantly removed buspirate entry

2009-10-12  Michal Ludvig  <<EMAIL>>

	* buspirate.c: Replace GNU-only %as with %s in sscanf call.
	* ser_win32.c(ser_set_dtr_rts): Fixed typo in parameter name.
	* NEWS: Announce BusPirate.

2009-10-11  David Hoerl <<EMAIL>>

	Support for AVR32

	* AUTHORS: added myself
	* NEWS: announced AVR32 support
	* main.c: AVR32 flag tests to avoid several code blocks
	* fileio.c: mods to ihex read function to handle address offsets and 
	  size of avr32
	* jtagmkI.c: added cast to printf call to remove warning
	* arduino.c: added header file to bring in prototype for usleep()
	* config_gram.y: added defines for avr32, new jtag_mkii variant for avr32
	* jtagmkII_private.h: new jtag_mkii message types defined (used by
	  avr32program)
	* jtagmkII.h: extern jtagmkII_avr32_initpgm() addition
	* jtagmkII.c: huge amount of code in support of avr32
	* avrpart.h: additional flags to AVRPART for avr32
	* usb_libusb.c: modified verbose test for USB read per-byte messages by
	  by one, so with verbose=3 you get just full messages, 4 gives you bytes
	  too
	* lexer.l: additions for avr32

2009-10-10  Michal Ludvig  <<EMAIL>>

	Support for Arduino auto-reset:
	* serial.h, ser_avrdoper.c, ser_posix.c, ser_win32.c: Added 
	  serial_device.set_dtr_rts implementations.
	* arduino.c, stk500.c, stk500.h: Call serial_set_dtr_rts()
	  to reset Arduino board before program upload.
	Inspired by patch #6866, resolves bug #26703

2009-10-08  Michal Ludvig  <<EMAIL>>

	* buspirate.c: Optimised buspirate_cmd() - reading 1kB EEPROM now
	  takes only 14 sec instead of almost 2 mins with the original
	  implementation.

2009-10-08  Michal Ludvig  <<EMAIL>>

	* buspirate.c, buspirate.h: Support for the BusPirate programmer
	* config_gram.y, avrdude.conf.in, main.c, lexer.l, Makefile.am:
	  Glue for BusPirate.

2009-08-17  Joerg Wunsch <<EMAIL>>

	* usb_libusb.c (usbdev_close): Repair the logic around the
	conditional compilation of usb_reset() introduced in r798.

2009-07-11  Joerg Wunsch <<EMAIL>>

	* configure.ac: We are post-5.8 now.

2009-07-11  Joerg Wunsch <<EMAIL>>

	* configure.ac: Prepare for releasing version 5.8

2009-07-11  Joerg Wunsch <<EMAIL>>

	Submitted by Roger Wolff:
	bug #26527: bug in unicode conversion
	* ser_avrdoper.c (convertUniToAscii): when encountering a UTF-16
	character that cannot be converted to ASCII, increment the UTF-16
	pointer anyway when proceeding.

2009-07-11  Joerg Wunsch <<EMAIL>>

	* jtagmkI.c (jtagmkI_send): Replace %zd format by %u since not all
	implementations do understand the C99 formatting options (sigh).
	* jtagmkII.c (jtagmkII_send): (Ditto.)
	* stk500v2.c (stk500v2_recv): (Ditto.)

2009-07-11  Joerg Wunsch <<EMAIL>>

	bug #26002: HVPP of EEPROM with AVR Dragon and ATmega8 Fails
	* avrdude.conf.in (ATmega8): add page size for EEPROM.

2009-07-07  Joerg Wunsch <<EMAIL>>

	* stk500v2.c: Fix a serious memory corruption problem resulting
	out of the chaining of both, the stk500v2 and the jtagmkII
	programmers for some programming hardware (JTAG ICE mkII and AVR
	Dragon running in ISP, HVSP or PP mode), where both programmers
	have to maintain their private programmer data.

2009-07-02  Joerg Wunsch <<EMAIL>>

	* configure.ac: Post-release (is pre-release...)

2009-07-02  Joerg Wunsch <<EMAIL>>

	* configure.ac: Prepare for releasing version 5.7

2009-07-02  Joerg Wunsch <<EMAIL>>

	* main.c: Add my name to the copyright output when being verbose.

2009-07-02  Joerg Wunsch <<EMAIL>>

	Contributed by Shaun Jackman  <<EMAIL>>
	bug #21798: Fix both XSLT scripts
	* tools/get-dw-params.xsl (format-hex): Add the parameter count.
	* tools/get-hv-params.xsl (format_cstack): Ditto.

2009-07-02  Joerg Wunsch <<EMAIL>>

	bug #21922: ATmega163 still not working in version 5.5
	* avrdude.conf.in (atmega163): fill in stk500v2 parameters, correct
	some flash programming parameters as well.

2009-07-02  Joerg Wunsch <<EMAIL>>

	bug #22206: avrdude: ser_setspeed(): tcsetattr() failed
	* ser_posix.c (ser_setspeed): Don't pass TCSAFLUSH to tcsetattr() as
	it apparently fails to work on Solaris.  After reading the
	documentation again, it seems TCSAFLUSH and TCSANOW are indeed
	mutually exclusive.

2009-07-02  Joerg Wunsch <<EMAIL>>

	bug #22234: WINDOWS version: HOWTO: Specify Serial Ports Larger than COM9
	* ser_win32.c (ser_open): prepend \\.\ to any COM port name, so it is
	safe to be used for COM ports above 9.

2009-07-02  Joerg Wunsch <<EMAIL>>

	bug #26408: Crash in stk500v2_open()
	* stk500generic.c: Implement setup and teardown hooks, calling in turn
	the respective hooks of the stk500v2 implementation.

2009-07-02  Joerg Wunsch <<EMAIL>>

	bug #26130: Avrdude doesn't display it's version.
	* main.c (usage): add a version number display to the default usage
	message.

2009-07-01  Joerg Wunsch <<EMAIL>>

	bug #26412: avrdude segfaults when called with a programmer that does not
	support it
	* main.c: do not call pgm->perform_osccal() unless it is != 0.

2009-06-24  Joerg Wunsch <<EMAIL>>

	Contributed by Zoltan Laday:
	patch #6825: xmega problems with JTAGICEmkII
	* jtagmkII.c: Many fixes for Xmega devices.
	* jtagmkII_private.h: Add various new constants required for
	Xmega devices.
	* avrdude.conf.in: New devices: ATXMEGA64A1, ATXMEGA192A1,
	ATXMEGA256A1, ATXMEGA64A3, ATXMEGA128A3, ATXMEGA192A3,
	ATXMEGA256A3, ATXMEGA256A3B, ATXMEGA16A4, ATXMEGA32A4,
	ATXMEGA64A4, ATXMEGA128A4
	* avr.c (avr_read, avr_write): Add more names for (Xmega)
	memory areas that require paged operation.

2009-06-24  Joerg Wunsch <<EMAIL>>

	* stk500v2.c (stk600_xprog_write_byte): Handle writing fuse bytes.

2009-04-28  Joerg Wunsch <<EMAIL>>

	Submitted by Carl Hamilton:
	* update.c (parse_op): correctly \0-terminate buf after filling
	it, before it is potentially used as the source of a call to
	strlen or strcpy.

2009-04-14  Joerg Wunsch <<EMAIL>>

	* doc/avrdude.texi: Merge the -P 0xXXX option description from
	avrdude.1.

2009-04-14  Joerg Wunsch <<EMAIL>>

	* configure.ac: declare AM_PROG_CC_C_O to avoid the warning
	"compiling `config_gram.c' with per-target flags
	requires `AM_PROG_CC_C_O' in `configure.ac'"

2009-03-22  Joerg Wunsch <<EMAIL>>

	bug #25971: "error writing to <stdout>" with multiple -U params.
	* fileio.c: Do not close the input/output stream when working on an
	stdio stream.

2009-02-28  Thomas Fischl <<EMAIL>>

	Based on patch #6484 commited by Jurgis Brigmanis:
	* usbasp.c: added software control for ISP speed
	* usbasp.h: (Ditto.)

2009-02-28  Joerg Wunsch <<EMAIL>>

	* avr910.c (avr910_read_byte_flash): Eliminate a static variable that
	hasn't been in use for 5 years.

2009-02-27  Joerg Wunsch <<EMAIL>>

	* configure.ac: Post-release 5.6.

2009-02-27  Joerg Wunsch <<EMAIL>>

	* configure.ac: Prepare for releasing version 5.6.

2009-02-27  Joerg Wunsch <<EMAIL>>

	Submitted by Ed Okerson:
	* jtagmkII.c (jtagmkII_read_byte): Fix signature reading of
	Xmega.

2009-02-26  Joerg Wunsch <<EMAIL>>

	Submitted by Mikael Hermansson:
	* avrdude.conf.in (ATxmega256A3): new device.
	* stk500v2 (stk500v2_initialize): Enable the AVRISPmkII as a
	PDI-capable device for ATxmega parts.

2009-02-25  Joerg Wunsch <<EMAIL>>

	Submitted by Lars Immisch:
	patch #6750: Arduino support - new programmer-id
	* arduino.c: New file, inherits stk500.c.
	* arduino.h: New file.
	* Makefile.am: Add arduino.c and arduino.h.
	* config_gram.y: Add arduino keyword.
	* lexer.l: (Ditto.)
	* avrdude.conf.in: (Ditto.)
	* avrdude.1: Document the new programmer type.
	* doc/avrdude.texi: (Ditto.)

2009-02-25  Joerg Wunsch <<EMAIL>>

	* stk500v2.c: Turn all non-const static data into instance data.

2009-02-25  Joerg Wunsch <<EMAIL>>

	* Makefile.am: Move term.[ch] from the library into the CLI
	application section, as it is not useful for anything else but
	the CLI frontend.

2009-02-25  Joerg Wunsch <<EMAIL>>

	* avrdude.conf.in (ATmega1284P): new device.

2009-02-23  Joerg Wunsch <<EMAIL>>

	More fixes for Solaris, including fixes for the Sunpro compiler:
	* avr.h: Remove stray semicolon.
	* configure.ac: Add check for predefined types uint_t and ulong_t.
	* confwin.c: Include "avrdude.h" on top to avoid empty translation
	unit warning.
	* ppwin.c: (Ditto.)
	* ser_win32.c: (Ditto.)
	* serbb_win32.c: (Ditto.)
	* jtagmkII.c (jtagmkII_recv): remove unreachable "return".
	* stk500.c (stk500_initialize): (Ditto.)
	* par.c: Test for both, __sun__ and __sun to see whether we are
	being compiled on Solaris.
	* ppi.c: (Ditto.)
	* stk500v2.c: Implement the DEBUG and DEBUGRECV macros in a way
	that is compatible with the ISO C99 standard.
	* usbtiny.c: Only typedef uint_t and ulong_t if they have not
	been found already by the autoconf checks.

2009-02-23  Joerg Wunsch <<EMAIL>>

	bug #22204: Solaris10/11 Undefiniertes Symbol gethostbyname socket
	connect
	* configure.ac: Add checks for gethostent() and socket().
	While being here, remove some old cruft left from ancient days.

2009-02-22  Joerg Wunsch <<EMAIL>>

	* lexer.l: Bump the %p size so AT&T lex will continue to work.

2009-02-19  Joerg Wunsch <<EMAIL>>

	(Partially) submitted by John Voltz:
	bug #20004: AVRDUDE update (-U) operations do not close files
	* fileio.c (fmt_autodetect, fileio): fclose() files.

2009-02-18  Joerg Wunsch <<EMAIL>>

	* usbtiny.c: Replace all but one (very unlikely to trigger) exit(1)
	by return -1.

2009-02-18  Joerg Wunsch <<EMAIL>>

	Submitted by Dick Streefland:
	patch #6749: make reading from the USBtinyISP programmer more robust
	* usbtiny.c: Add code to retry failed communication attempts.

2009-02-17  Joerg Wunsch <<EMAIL>>

	Submitted by Nick Hibma:
	bug #22271: usb_reset in usb_libusb.c not necessary in FreeBSD 6.x
	* usb_libusb.c (usbdev_close): Do not call usb_reset() on FreeBSD.
	It is not necessary there.

2009-02-17  Joerg Wunsch <<EMAIL>>

	Submitted by Andrew O. Shadoura:
	bug #25156: add direct SPI transfer mode
	* bitbang.c: Implement direct SPI transfers.
	* bitbang.h: (Ditto.)
	* par.c: (Ditto.)
	* pgm.c: (Ditto.)
	* pgm.h: (Ditto.)
	* term.c: Add the "spi" and "pgm" commands.
	* avrdude.1: Document the changes.
	* doc/avrdude.texi: (Ditto.)

2009-02-17  Joerg Wunsch <<EMAIL>>

	Submitted by Limor ("Lady Ada"):
	bug #24749: add support for '328p
	* avrdude.conf.in (ATmega328P): new device support.

2009-02-17  Joerg Wunsch <<EMAIL>>

	Submitted by "Womo":
	bug #25241: AT90USB162, AT90USB82 device support patch for avrdude-5.5
	(also: bug #21745: AT90USBxx2 support)
	* avrdude.conf.in (AT90USB162, AT90USB82): new device support.

2009-02-17  Joerg Wunsch <<EMAIL>>

	Submitted by Evangelos Arkalis:
	patch #6069: Atmel AT89ISP Cable
	* avrdude.conf.in (89isp): new programmer support.

2009-02-16  Joerg Wunsch <<EMAIL>>

	Submitted by Bob Paddock:
	patch #6748: ATTiny88 Config
	* avrdude.conf.in (ATtiny88): new device support.

2009-02-16  Joerg Wunsch <<EMAIL>>

	Submitted by Mark Litwack:
	patch #6261: avrdude won't use dragon/debugwire to write a file
	to eeprom
	* jtagmkII.c (jtagmkII_paged_write): when in debugWire mode,
	implement a paged write to EEPROM as a series of byte writes.

2009-02-16  Joerg Wunsch <<EMAIL>>

	Submitted by Janos Sallai:
	patch #6542: paged_load fails on the MIB510 programming board
	* stk500.c: Add a workaround for the different signon sequence on
	MIB510 programmers.

2009-02-05  Joerg Wunsch <<EMAIL>>

	* avrdude.conf.in: Add the ATmega128RFA1.
	* avrdude.1: document the addition of ATmega128RFA1.
	* doc/avrdude.texi: (Ditto.)

