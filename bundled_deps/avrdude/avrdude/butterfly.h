/*
 * avrdude - A Downloader/Uploader for AVR device programmers
 * Copyright (C) 2003-2004  <PERSON>  <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */

/* $Id$ */

#ifndef butterfly_h
#define butterfly_h

#ifdef __cplusplus
extern "C" {
#endif

extern const char butterfly_desc[];
extern const char butterfly_mk_desc[];
void butterfly_initpgm (PROGRAMMER * pgm);
void butterfly_mk_initpgm (PROGRAMMER * pgm);

#ifdef __cplusplus
}
#endif

#endif /* butterfly_h */
