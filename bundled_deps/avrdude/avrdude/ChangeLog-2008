2008-11-20  <PERSON><PERSON> <<EMAIL>>

	* avrdude.h: Change the prototype for usleep() to be more Cygwin-
	friendly.
	* ppiwin.c: (Ditto.)

2008-11-06  <PERSON><PERSON> <<EMAIL>>

	Submitted by limor <<EMAIL>>
	* usbtiny.c (usbtiny_cmd): Replace sizeof() by a fixed constant
	4 for the result array, because otherwise it would take the size
	of a pointer which miserably fails on 64-bit machines.

2008-11-05  <PERSON><PERSON> <<EMAIL>>

	patch #6609: Using PCI parallel port cards on Windows
	* ppiwin.c (ppi_open): If the port parameter passed from the
	-p option is neither lpt1/2/3, try interpreting it directly as
	a base address.
	* avrdude.1: Document the change.
	* doc/avrdude.texi: (Ditto.)

2008-11-04  <PERSON><PERSON> <<EMAIL>>

	bug #22882: Erase Cycle Counter does not work for stk500v2
	* stk500v2.c (stk500v2_chip_erase,stk500hv_chip_erase): Return
	the expected 0 for success rather than a protocol-dependant
	number.
 
2008-11-04  <PERSON><PERSON>nsch <<EMAIL>>

	bug #22883: Chip Erase performed even with no-write flag (-n)
	* main.c: Do not erase the chip if both, -e and -n options have
	been specified.

2008-11-04  Joerg Wunsch <<EMAIL>>

	bug #24589: AT90USB64* have wrong signature
	* avrdude.conf.in: Uncomment the correct, and delete the wrong
	signature for AT90USB646/647.  Alas, the datasheet has never been
	corrected for years.

2008-10-31  Joerg Wunsch <<EMAIL>>

	* jtagmkII.c: Fix a serious memory corruption that happened when
	using the JTAG ICE mkII (or AVR Dragon) in ISP mode.  The wrong
	set of per-programmer private data had been allocated (stk500v2
	vs. jtagmkII) which was too small to hold the actual data.
	* jtagmkII.h: (Ditto.)
	* stk500v2.c: (Ditto.)

2008-07-29  Joerg Wunsch <<EMAIL>>

	* jtagmkII.c: Implement Xmega JTAG support.
	* jtagmkII_private.h: Add EMULATOR_MODE_JTAG_XMEGA.

2008-07-29  Joerg Wunsch <<EMAIL>>

	* main.c: Remember whether the device initialization worked, and
	allow to continue with -F if it failed yet do not attempt to
	perform anything on the device itself.  That way, -tF could be
	specified for programmers like the STK500/STK600 even without a
	device connected, just in order to allow changing parameters on
	the programmer itself.
	* avrdude.1: Document that possible use of the -F option.
	* doc/avrdude.texi: (Ditto.)

2008-07-29  Joerg Wunsch <<EMAIL>>

	* stk500v2.c (stk600_xprog_paged_write): Fix a fatal miscalculation
	of the number of bytes to be written which caused a malloc chunk
	corruption.

2008-07-27  Joerg Wunsch <<EMAIL>>

	First implementation of ATxmega support.  By now, only the
	PDI mode of the STK600 is supported.  Single-byte EEPROM
	(and flash) updates do not work yet.
	* avr.c: "boot" memory is a candidate memory region for paged
	operations, besides "flash" and "eeprom".
	* avrdude.conf.in: add ATxmega128A1 and ATxmega128A1revD
	* avrpart.h: add the AVRPART_HAS_PDI flag (used to distinguish
	ATxmega parts from classic AVRs), the nvm_base part field, and
	the offset field for a memory region.
	* config_gram.y: add "has_pdi", "nvm_base", and "offset"
	* lexer.l: (Ditto.)
	* main.c: disable auto_erase for ATxmega parts
	* stk500v2.c: implement the XPROG functionality, and divert to
	this for ATxmega parts
	* avrdude.1: Document the changes.
	* doc/avrdude.texi: (Ditto.)

2008-07-25  Joerg Wunsch <<EMAIL>>

	Fix a bunch of warnings.
	* avr910.c (avr910_paged_load): possible unitialized use of
	rd_size
	* jtagmkI.c (jtagmkI_initialize): pointer signedness mixup
	* jtagmkII.c (jtagmkII_print_parms1): propagate const'ness
	of parameter
	* usbasp.c (usbasp_transmit): pointer signedness mixup
	* ser_avrdoper.c (usbGetReport): remove useless pointer deref

2008-07-25  Joerg Wunsch <<EMAIL>>

	Contributed by Ville Voipio:
	patch #6501: New autotools support for avrdude
	* Makefile.am: add @WINDOWS_DIRS@ to SUBDIR
	* bootstrap: allow for autconf-2.61 and automake-1.10, too
	* configure.ac: fix @WINDOWS_DIRS@ recursion, replace
	AC_PROG_CC by AM_PROG_CC_C_O, for esoteric reasons

2008-06-13  Joerg Wunsch <<EMAIL>>

	Contributed by Janos Sallai <<EMAIL>>:
	patch #6074: added support for crossbow's MIB510 programmer
	* avrdude.conf.in: Add entry for mib510.
	* stk500.c: Add special hooks to handle the MIB510 programmer.
	It mostly talks STK500v1 protocol but has a special hello and
	goodbye sequence, and uses a fixed block size of 256 bytes.
	* doc/avrdude.texi: Document support for mib510.

2008-06-07  Joerg Wunsch <<EMAIL>>

	Contributed by Klaus Leidinger <<EMAIL>>:
	* main.c: Realign verbose messages.
	* avrpart.c: (Ditto.)
	* avr910.c: Print the device code selected in verbose mode.
	* butterfly.c: (Ditto.)

2008-06-07  Joerg Wunsch <<EMAIL>>

	Contributed by Klaus Leidinger <<EMAIL>>:
	Add check for buffermode feature, and use it if present.  Can be
	turned off using -x no_blockmode.
	* avr910.c: Implement buffermode test and usage.
	* avrdude.1: Document -x no_blockmode.
	* doc/avrdude.texi: (Ditto.)

2008-03-24  Joerg Wunsch <<EMAIL>>

	* usb_libusb.c: #undef interface for Win32

2008-03-24  Joerg Wunsch <<EMAIL>>

	* avr910.c: Add support for the -x devcode option.
	* avrdude.1: Document -x devcode for avr910.
	* doc/avrdude.texi: (Ditto.)

2008-03-14  Joerg Wunsch <<EMAIL>>

	Add initial support for the Atmel STK600, for
	"classic" AVRs (AT90, ATtiny, ATmega) in both,
	ISP and high-voltage programming modes.
	* Makefile.am: Add -lm.
	* avrdude.conf.in: Add stk600, stk600pp, and stk600hvsp.
	* config_gram.y: Add support for the stk600* keywords.
	* lexer.l: (Ditto.)
	* pgm.h: Add the "chan" parameter to set_varef().
	* stk500.c: (Ditto.)
	* serial.h: Add USB endpoint support to struct filedescriptor.
	* stk500v2.c: Implement the meat of the STK600 support.
	* stk500v2.h: Add new prototypes for stk600*() programmers.
	* stk500v2_private.h: Add new constants used in the STK600.
	* term.c: Add AREF channel support.
	* usb_libusb.c: Automatically determine the correct write
	endpoint ID, the STK600 uses 0x83 while all other tools use
	0x82.  Propagate the EP to use through struct filedescriptor.
	* usbdevs.h: Add the STK600 USB product ID.
	* tools/get-stk600-cards.xsl: XSL transformation for
	targetboards.xml to obtain the list of socket and routing
	card IDs, to be used in stk500v2.c (for displaying the
	names).
	* tools/get-stk600-devices.xsl: XSL transformation for
	targetboards.xml to obtain the table of socket/routing cards
	and their respective AVR device support for doc/avrdude.texi.
	* avrdude.1: Document all the STK600 stuff.
	* doc/avrdude.texi: Ditto.  Added a new chapter for
	Programmer Specific Information.

2008-01-26  Joerg Wunsch <<EMAIL>>

	* stk500v2.c (stk500v2_recv): Make length computation unsigned so
	it cannot accidentally become negative.

