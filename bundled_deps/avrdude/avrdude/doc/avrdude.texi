%% -*-texinfo-*-
\input texinfo

@c $Id$

@setfilename avrdude.info
@settitle AVRDUDE
@finalout

@include version.texi

@c
@c These are set in version.texi which is automatically generated by automake.
@c
@c @set UPDATED 26 Febuary 2003
@c @set EDITION 3.2.0
@c @set VERSION 3.2.0

@c This is a dir.info fragment to support semi-automated addition of
@c manuals to an info tree.
@dircategory AVR Programming & development tools.
@direntry
* AvrDude: (avrdude).            AVR program downloader/uploader.
@end direntry

@ifinfo
This file documents the avrdude program.

For avrdude version @value{VERSION}, @value{UPDATED}.

Copyright @copyright{} 2003, 2005 <PERSON>

Copyright @copyright{} 2006 - 2016 J@"org Wunsch

Permission is granted to make and distribute verbatim copies of
this manual provided the copyright notice and this permission notice
are preserved on all copies.

@ignore
Permission is granted to process this file through TeX and print the
results, provided the printed document carries copying permission
notice identical to this one except for the removal of this paragraph
(this paragraph not being relevant to the printed manual).

@end ignore
Permission is granted to copy and distribute modified versions of this
manual under the conditions for verbatim copying, provided that the entire
resulting derived work is distributed under the terms of a permission
notice identical to this one.

Permission is granted to copy and distribute translations of this manual
into another language, under the above conditions for modified versions,
except that this permission notice may be stated in a translation approved
by the Free Software Foundation.

Alternatively, this documentation may be copied and distributed under
the terms of the GNU Free Documentation License (FDL), version 1.3.
@end ifinfo

@titlepage
@title AVRDUDE
@subtitle A program for download/uploading AVR microcontroller flash and eeprom.
@subtitle For AVRDUDE, Version @value{VERSION}, @value{UPDATED}.
<AUTHOR> Brian S. Dean

@page
Send comments on AVRDUDE to @w{@email{avrdude-dev@@nongnu.org}}.

Use @uref{http://savannah.nongnu.org/bugs/?group=avrdude} to report bugs.

Copyright @copyright{} 2003,2005 Brian S. Dean

Copyright @copyright{} 2006 - 2013 J@"org Wunsch
@sp 2

Permission is granted to make and distribute verbatim copies of
this manual provided the copyright notice and this permission notice
are preserved on all copies.

Permission is granted to copy and distribute modified versions of this
manual under the conditions for verbatim copying, provided that the entire
resulting derived work is distributed under the terms of a permission
notice identical to this one.

Permission is granted to copy and distribute translations of this manual
into another language, under the above conditions for modified versions,
except that this permission notice may be stated in a translation approved
by the Free Software Foundation.
@end titlepage

@contents

@c
@c Top Node
@c
@node Top, Introduction, (dir), (dir)
@comment  node-name,  next,  previous,  up

@ifinfo
This file documents the avrdude program for downloading/uploading
programs to Atmel AVR microcontrollers.

For avrdude version @value{VERSION}, @value{UPDATED}.

Send comments on AVRDUDE to @w{@email{avrdude-dev@@nongnu.org}}.

Use @uref{http://savannah.nongnu.org/bugs/?group=avrdude} to report bugs.

Copyright @copyright{} 2003,2005 Brian S. Dean

Copyright @copyright{} 2006 J@"org Wunsch
@end ifinfo

@menu
* Introduction::                
* Command Line Options::
* Terminal Mode Operation::
* Configuration File::          
* Programmer Specific Information::
* Platform Dependent Information::
* Troubleshooting::
@end menu

@c
@c Node
@c
@node Introduction, Command Line Options, Top, Top
@comment  node-name,  next,  previous,  up
@chapter Introduction
@cindex introduction

AVRDUDE - AVR Downloader Uploader - is a program for downloading and
uploading the on-chip memories of Atmel's AVR microcontrollers. It can
program the Flash and EEPROM, and where supported by the serial
programming protocol, it can program fuse and lock bits. AVRDUDE also
supplies a direct instruction mode allowing one to issue any programming
instruction to the AVR chip regardless of whether AVRDUDE implements
that specific feature of a particular chip.

AVRDUDE can be used effectively via the command line to read or write
all chip memory types (eeprom, flash, fuse bits, lock bits, signature
bytes) or via an interactive (terminal) mode. Using AVRDUDE from the
command line works well for programming the entire memory of the chip
from the contents of a file, while interactive mode is useful for
exploring memory contents, modifying individual bytes of eeprom,
programming fuse/lock bits, etc.

AVRDUDE supports the following basic programmer types: Atmel's STK500,
Atmel's AVRISP and AVRISP mkII devices,
Atmel's STK600,
Atmel's JTAG ICE (the original one, mkII, and 3, the latter two also in ISP mode), appnote
avr910, appnote avr109 (including the AVR Butterfly),
serial bit-bang adapters,
and the PPI (parallel port interface). PPI represents a class
of simple programmers where the programming lines are directly
connected to the PC parallel port. Several pin configurations exist
for several variations of the PPI programmers, and AVRDUDE can be
configured to work with them by either specifying the appropriate
programmer on the command line or by creating a new entry in its
configuration file. All that's usually required for a new entry is to
tell AVRDUDE which pins to use for each programming function.

A number of equally simple bit-bang programming adapters that connect
to a serial port are supported as well, among them the popular
Ponyprog serial adapter, and the DASA and DASA3 adapters that used to
be supported by uisp(1).  Note that these adapters are meant to be
attached to a physical serial port.  Connecting to a serial port
emulated on top of USB is likely to not work at all, or to work
abysmally slow.

If you happen to have a Linux system with at least 4 hardware GPIOs 
available (like almost all embedded Linux boards) you can do without 
any additional hardware - just connect them to the MOSI, MISO, RESET 
and SCK pins on the AVR and use the linuxgpio programmer type. It bitbangs
the lines using the Linux sysfs GPIO interface. Of course, care should
be taken about voltage level compatibility. Also, although not strictly 
required, it is strongly advisable to protect the GPIO pins from 
overcurrent situations in some way. The simplest would be to just put
some resistors in series or better yet use a 3-state buffer driver like
the 74HC244. Have a look at http://kolev.info/avrdude-linuxgpio for a more
detailed tutorial about using this programmer type.

The STK500, JTAG ICE, avr910, and avr109/butterfly use the serial port to communicate with the PC.
The STK600, JTAG ICE mkII/3, AVRISP mkII, USBasp, avrftdi (and derivatives), and USBtinyISP
programmers communicate through the USB, using @code{libusb} as a
platform abstraction layer.
The avrftdi adds support for the FT2232C/D, FT2232H, and FT4232H devices. These all use 
the MPSSE mode, which has a specific pin mapping. Bit 1 (the lsb of the byte in the config
file) is SCK. Bit 2 is MOSI, and Bit 3 is MISO. Bit 4 usually reset. The 2232C/D parts
are only supported on interface A, but the H parts can be either A or B (specified by the
usbdev config parameter).
The STK500, STK600, JTAG ICE, and avr910 contain on-board logic to control the programming of the target
device.
The avr109 bootloader implements a protocol similar to avr910, but is
actually implemented in the boot area of the target's flash ROM, as
opposed to being an external device.
The fundamental difference between the two types lies in the
protocol used to control the programmer. The avr910 protocol is very
simplistic and can easily be used as the basis for a simple, home made
programmer since the firmware is available online. On the other hand,
the STK500 protocol is more robust and complicated and the firmware is
not openly available.
The JTAG ICE also uses a serial communication protocol which is similar
to the STK500 firmware version 2 one.  However, as the JTAG ICE is
intended to allow on-chip debugging as well as memory programming, the
protocol is more sophisticated.
(The JTAG ICE mkII protocol can also be run on top of USB.)
Only the memory programming functionality of the JTAG ICE is supported
by AVRDUDE.
For the JTAG ICE mkII/3, JTAG, debugWire and ISP mode are supported, provided
it has a firmware revision of at least 4.14 (decimal).
See below for the limitations of debugWire.
For ATxmega devices, the JTAG ICE mkII/3 is supported in PDI mode, provided it
has a revision 1 hardware and firmware version of at least 5.37 (decimal).

The Atmel-ICE (ARM/AVR) is supported (JTAG, PDI for Xmega, debugWIRE, ISP modes).

Atmel's XplainedPro boards, using EDBG protocol (CMSIS-DAP compliant), are
supported by the ``jtag3'' programmer type.

Atmel's XplainedMini boards, using mEDBG protocol, are also
supported by the ``jtag3'' programmer type.

The AVR Dragon is supported in all modes (ISP, JTAG, PDI, HVSP, PP, debugWire).
When used in JTAG and debugWire mode, the AVR Dragon behaves similar to a
JTAG ICE mkII, so all device-specific comments for that device
will apply as well.
When used in ISP and PDI mode, the AVR Dragon behaves similar to an
AVRISP mkII (or JTAG ICE mkII in ISP mode), so all device-specific
comments will apply there.
In particular, the Dragon starts out with a rather fast ISP clock
frequency, so the @code{-B @var{bitclock}}
option might be required to achieve a stable ISP communication.
For ATxmega devices, the AVR Dragon is supported in PDI mode, provided it
has a firmware version of at least 6.11 (decimal).

Wiring boards are supported, utilizing STK500 V2.x protocol, but
a simple DTR/RTS toggle to set the boards into programming mode.
The programmer type is ``wiring''.

The Arduino (which is very similar to the STK500 1.x) is supported via
its own programmer type specification ``arduino''.

The BusPirate is a versatile tool that can also be used as an AVR programmer.
A single BusPirate can be connected to up to 3 independent AVRs. See
the section on
@emph{extended parameters}
below for details.

The USBasp ISP and USBtinyISP adapters are also supported, provided AVRDUDE
has been compiled with libusb support.
They both feature simple firmware-only USB implementations, running on
an ATmega8 (or ATmega88), or ATtiny2313, respectively.

The Atmel DFU bootloader is supported in both, FLIP protocol version 1
(AT90USB* and ATmega*U* devices), as well as version 2 (Xmega devices).
See below for some hints about FLIP version 1 protocol behaviour.


@menu
* History::                     
@end menu

@c
@c Node
@c
@node History,  , Introduction, Introduction
@section History and Credits

AVRDUDE was written by Brian S. Dean under the name of AVRPROG to run on
the FreeBSD Operating System.  Brian renamed the software to be called
AVRDUDE when interest grew in a Windows port of the software so that the
name did not conflict with AVRPROG.EXE which is the name of Atmel's
Windows programming software.

The AVRDUDE source now resides in the public CVS repository on
savannah.gnu.org (@url{http://savannah.gnu.org/projects/avrdude/}),
where it continues to be enhanced and ported to other systems.  In
addition to FreeBSD, AVRDUDE now runs on Linux and Windows.  The
developers behind the porting effort primarily were Ted Roth, Eric
Weddington, and Joerg Wunsch.

And in the spirit of many open source projects, this manual also draws
on the work of others.  The initial revision was composed of parts of
the original Unix manual page written by Joerg Wunsch, the original web
site documentation by Brian Dean, and from the comments describing the
fields in the AVRDUDE configuration file by Brian Dean.  The texi
formatting was modeled after that of the Simulavr documentation by Ted
Roth.


@c
@c Node
@c
@node Command Line Options, Terminal Mode Operation, Introduction, Top
@chapter Command Line Options
@cindex options

@menu
* Option Descriptions::
* Programmers accepting extended parameters::
* Example Command Line Invocations::
@end menu

@c
@c Node
@c
@node Option Descriptions, Programmers accepting extended parameters, Command Line Options, Command Line Options
@section Option Descriptions

@noindent
AVRDUDE is a command line tool, used as follows:

@smallexample
avrdude -p partno @var{options} @dots{}
@end smallexample

@noindent
Command line options are used to control AVRDUDE's behaviour.  The
following options are recognized:

@table @code
@item -p @var{partno}
This is the only mandatory option and it tells AVRDUDE what type of part
(MCU) that is connected to the programmer.  The @var{partno} parameter
is the part's id listed in the configuration file.  Specify -p ? to list
all parts in the configuration file.  If a part is unknown
to AVRDUDE, it means that there is no config file entry for that part,
but it can be added to the configuration file if you have the Atmel
datasheet so that you can enter the programming specifications.
Currently, the following MCU types are understood:

@multitable @columnfractions .15 .3
@include parts.texi
@end multitable

(*)   The AT90S2323 and ATtiny22 use the same algorithm.

(**)  Flash addressing above 128 KB is not supported by all
programming hardware.  Known to work are jtag2, stk500v2,
and bit-bang programmers.

(***)
The ATtiny11 can only be
programmed in high-voltage serial mode.

(****)
The ISP programming protocol of the AT90S1200 differs in subtle ways
from that of other AVRs.  Thus, not all programmers support this
device.  Known to work are all direct bitbang programmers, and all
programmers talking the STK500v2 protocol.

@item -b @var{baudrate}
Override the RS-232 connection baud rate specified in the respective
programmer's entry of the configuration file.

@item -B @var{bitclock}
Specify the bit clock period for the JTAG interface or the ISP clock (JTAG ICE only).
The value is a floating-point number in microseconds.
Alternatively, the value might be suffixed with "Hz", "kHz", or "MHz",
in order to specify the bit clock frequency, rather than a period.
The default value of the JTAG ICE results in about 1 microsecond bit
clock period, suitable for target MCUs running at 4 MHz clock and
above.
Unlike certain parameters in the STK500, the JTAG ICE resets all its
parameters to default values when the programming software signs
off from the ICE, so for MCUs running at lower clock speeds, this
parameter must be specified on the command-line.
It can also be set in the configuration file by using the 'default_bitclock'
keyword.

@item -c @var{programmer-id}
Specify the programmer to be used.  AVRDUDE knows about several common
programmers.  Use this option to specify which one to use.  The
@var{programmer-id} parameter is the programmer's id listed in the
configuration file.  Specify -c ? to list all programmers in the
configuration file.  If you have a programmer that is unknown to
AVRDUDE, and the programmer is controlled via the PC parallel port,
there's a good chance that it can be easily added to the configuration
file without any code changes to AVRDUDE.  Simply copy an existing entry
and change the pin definitions to match that of the unknown programmer.
Currently, the following programmer ids are understood and supported:

@multitable @columnfractions .2 .6
@include programmers.texi
@end multitable



@item -C @var{config-file}
Use the specified config file for configuration data.  This file
contains all programmer and part definitions that AVRDUDE knows about.
If not
specified, AVRDUDE reads the configuration file from
/usr/local/etc/avrdude.conf (FreeBSD and Linux). See Appendix A for
the method of searching for the configuration file for Windows.

If @var{config-file} is written as @var{+filename}
then this file is read after the system wide and user configuration 
files. This can be used to add entries to the configuration
without patching your system wide configuration file. It can be used 
several times, the files are read in same order as given on the command 
line.


@item -D
Disable auto erase for flash.  When the -U option with flash memory is 
specified, avrdude will perform a chip erase before starting any of the 
programming operations, since it generally is a mistake to program the flash
without performing an erase first.  This option disables that.
Auto erase is not used for ATxmega devices as these devices can
use page erase before writing each page so no explicit chip erase
is required.
Note however that any page not affected by the current operation
will retain its previous contents.

@item -e
Causes a chip erase to be executed.  This will reset the contents of the
flash ROM and EEPROM to the value `0xff', and clear all lock bits.
Except for ATxmega devices which can use page erase,
it is basically a
prerequisite command before the flash ROM can be reprogrammed again.
The only exception would be if the new contents would exclusively cause
bits to be programmed from the value `1' to `0'.  Note that in order
to reprogram EERPOM cells, no explicit prior chip erase is required
since the MCU provides an auto-erase cycle in that case before
programming the cell.


@item -E @var{exitspec}[,@dots{}]
By default, AVRDUDE leaves the parallel port in the same state at exit
as it has been found at startup.  This option modifies the state of the
`/RESET' and `Vcc' lines the parallel port is left at, according to
the exitspec arguments provided, as follows:

@table @code
@item reset
The `/RESET' signal will be left activated at program exit, that is it
will be held low, in order to keep the MCU in reset state afterwards.
Note in particular that the programming algorithm for the AT90S1200
device mandates that the `/RESET' signal is active before powering up
the MCU, so in case an external power supply is used for this MCU type,
a previous invocation of AVRDUDE with this option specified is one of
the possible ways to guarantee this condition.

@item noreset
The `/RESET' line will be deactivated at program exit, thus allowing the
MCU target program to run while the programming hardware remains
connected.

@item vcc
This option will leave those parallel port pins active (i. e. high) that
can be used to supply `Vcc' power to the MCU.

@item novcc
This option will pull the `Vcc' pins of the parallel port down at
program exit.

@item d_high
This option will leave the 8 data pins on the parallel port active
(i. e. high).

@item d_low
This option will leave the 8 data pins on the parallel port inactive
(i. e. low).

@end table

Multiple @var{exitspec} arguments can be separated with commas.


@item -F
Normally, AVRDUDE tries to verify that the device signature read from
the part is reasonable before continuing.  Since it can happen from time
to time that a device has a broken (erased or overwritten) device
signature but is otherwise operating normally, this options is provided
to override the check.
Also, for programmers like the Atmel STK500 and STK600 which can
adjust parameters local to the programming tool (independent of an
actual connection to a target controller), this option can be used
together with @option{-t} to continue in terminal mode.

@item -i @var{delay}
For bitbang-type programmers, delay for approximately
@var{delay}
microseconds between each bit state change.
If the host system is very fast, or the target runs off a slow clock
(like a 32 kHz crystal, or the 128 kHz internal RC oscillator), this
can become necessary to satisfy the requirement that the ISP clock
frequency must not be higher than 1/4 of the CPU clock frequency.
This is implemented as a spin-loop delay to allow even for very
short delays.
On Unix-style operating systems, the spin loop is initially calibrated
against a system timer, so the number of microseconds might be rather
realistic, assuming a constant system load while AVRDUDE is running.
On Win32 operating systems, a preconfigured number of cycles per
microsecond is assumed that might be off a bit for very fast or very
slow machines.

@item -l @var{logfile}
Use @var{logfile} rather than @var{stderr} for diagnostics output.
Note that initial diagnostic messages (during option parsing) are still
written to @var{stderr} anyway.

@item -n
No-write - disables actually writing data to the MCU (useful for
debugging AVRDUDE).

@item -O
Perform a RC oscillator run-time calibration according to Atmel
application note AVR053.
This is only supported on the STK500v2, AVRISP mkII, and JTAG ICE mkII
hardware.
Note that the result will be stored in the EEPROM cell at address 0.

@item -P @var{port}
Use port to identify the device to which the programmer is attached.
Normally, the default parallel port is used, but if the programmer type
normally connects to the serial port, the default serial port will be
used. See Appendix A, Platform Dependent Information, to find out the
default port names for your platform. If you need to use a different
parallel or serial port, use this option to specify the alternate port name.

On Win32 operating systems, the parallel ports are referred to as lpt1
through lpt3, referring to the addresses 0x378, 0x278, and 0x3BC,
respectively.  If the parallel port can be accessed through a different
address, this address can be specified directly, using the common C
language notation (i. e., hexadecimal values are prefixed by @var{0x}).

For the JTAG ICE mkII, if AVRDUDE has been built with libusb support,
@var{port} may alternatively be specified as
@code{usb}[:@var{serialno}].  In that case, the JTAG ICE mkII will be
looked up on USB.  If @var{serialno} is also specified, it will be
matched against the serial number read from any JTAG ICE mkII found on
USB.  The match is done after stripping any existing colons from the
given serial number, and right-to-left, so only the least significant
bytes from the serial number need to be given.
For a trick how to find out the serial numbers of all JTAG ICEs
attached to USB, see @ref{Example Command Line Invocations}.

As the AVRISP mkII device can only be talked to over USB, the very
same method of specifying the port is required there.

For the USB programmer "AVR-Doper" running in HID mode, the port must
be specified as @var{avrdoper}. Libusb support is required on Unix
but not on Windows. For more information about AVR-Doper see
@url{http://www.obdev.at/avrusb/avrdoper.html}.

For the USBtinyISP, which is a simplistic device not implementing
serial numbers, multiple devices can be distinguished by their
location in the USB hierarchy.
@xref{Troubleshooting}, for examples.

For programmers that attach to a serial port using some kind of
higher level protocol (as opposed to bit-bang style programmers),
@var{port} can be specified as @code{net}:@var{host}:@var{port}.
In this case, instead of trying to open a local device, a TCP
network connection to (TCP) @var{port} on @var{host}
is established.
Square brackets may be placed around @var{host} to improve
readability for numeric IPv6 addresses (e.g.
@code{net:[2001:db8::42]:1337}).
The remote endpoint is assumed to be a terminal or console server
that connects the network stream to a local serial port where the
actual programmer has been attached to.
The port is assumed to be properly configured, for example using a
transparent 8-bit data connection without parity at 115200 Baud
for a STK500.

Note: The ability to handle IPv6 hostnames and addresses is limited to
Posix systems (by now).

@item -q
Disable (or quell) output of the progress bar while reading or writing
to the device.  Specify it a second time for even quieter operation.

@item -u
Disables the default behaviour of reading out the fuses three times before
programming, then verifying at the end of programming that the fuses have not
changed. If you want to change fuses you will need to specify this option,
as avrdude will see the fuses have changed (even though you wanted to) and
will change them back for your "safety". This option was designed to
prevent cases of fuse bits magically changing (usually called @emph{safemode}).

If one of the configuration files contains a line

@code{default_safemode = no;}

safemode is disabled by default.
The @option{-u} option's effect is negated in that case, i. e. it
@emph{enables} safemode.

Safemode is always disabled for AVR32, Xmega and TPI devices.

@item -s
Disable safemode prompting.  When safemode discovers that one or more
fuse bits have unintentionally changed, it will prompt for
confirmation regarding whether or not it should attempt to recover the
fuse bit(s).  Specifying this flag disables the prompt and assumes
that the fuse bit(s) should be recovered without asking for
confirmation first.

@item -t
Tells AVRDUDE to enter the interactive ``terminal'' mode instead of up-
or downloading files.  See below for a detailed description of the
terminal mode.

@item -U @var{memtype}:@var{op}:@var{filename}[:@var{format}]
Perform a memory operation.
Multiple @option{-U} options can be specified in order to operate on
multiple memories on the same command-line invocation.  The
@var{memtype} field specifies the memory type to operate on. Use
the @option{-v} option on the command line or the @code{part} command from
terminal mode to display all the memory types supported by a particular
device.
Typically, a device's memory configuration at least contains
the memory types
@code{flash}
and
@code{eeprom}.
All memory types currently known are:
@table @code
@item calibration
One or more bytes of RC oscillator calibration data.
@item eeprom
The EEPROM of the device.
@item efuse
The extended fuse byte.
@item flash
The flash ROM of the device.
@item fuse
The fuse byte in devices that have only a single fuse byte.
@item hfuse
The high fuse byte.
@item lfuse
The low fuse byte.
@item lock
The lock byte.
@item signature
The three device signature bytes (device ID).
@item fuse@emph{N}
The fuse bytes of ATxmega devices, @emph{N} is an integer number
for each fuse supported by the device.
@item application
The application flash area of ATxmega devices.
@item apptable
The application table flash area of ATxmega devices.
@item boot
The boot flash area of ATxmega devices.
@item prodsig
The production signature (calibration) area of ATxmega devices.
@item usersig
The user signature area of ATxmega devices.
@end table

The @var{op} field specifies what operation to perform:

@table @code
@item r
read the specified device memory and write to the specified file

@item w
read the specified file and write it to the specified device memory

@item v
read the specified device memory and the specified file and perform a verify operation

@end table

The @var{filename} field indicates the name of the file to read or
write.  The @var{format} field is optional and contains the format of
the file to read or write.  Possible values are:

@table @code
@item i
Intel Hex

@item s
Motorola S-record

@item r
raw binary; little-endian byte order, in the case of the flash ROM data

@item e
ELF (Executable and Linkable Format), the final output file from the
linker; currently only accepted as an input file

@item m
immediate mode; actual byte values specified on the command line,
separated by commas or spaces in place of the @var{filename} field of
the @option{-U} option.  This is useful
for programming fuse bytes without having to create a single-byte file
or enter terminal mode.  If the number specified begins with @code{0x},
it is treated as a hex value.  If the number otherwise begins with a
leading zero (@code{0}) it is treated as octal.  Otherwise, the value is
treated as decimal.

@item a
auto detect; valid for input only, and only if the input is not provided
at stdin.

@item d
decimal; this and the following formats are only valid on output.
They generate one line of output for the respective memory section,
forming a comma-separated list of the values.
This can be particularly useful for subsequent processing, like for
fuse bit settings.

@item h
hexadecimal; each value will get the string @emph{0x} prepended.

@item o
octal; each value will get a @emph{0}
prepended unless it is less than 8 in which case it gets no prefix.

@item b
binary; each value will get the string @emph{0b} prepended.

@end table

The default is to use auto detection for input files, and raw binary
format for output files.

Note that if @var{filename} contains a colon, the @var{format} field is
no longer optional since the filename part following the colon would
otherwise be misinterpreted as @var{format}.

When reading any kind of flash memory area (including the various sub-areas
in Xmega devices), the resulting output file will be truncated to not contain
trailing 0xFF bytes which indicate unprogrammed (erased) memory.
Thus, if the entire memory is unprogrammed, this will result in an output
file that has no contents at all.

As an abbreviation, the form @code{-U} @var{filename}
is equivalent to specifying
@code{-U} @emph{flash:w:}@var{filename}@emph{:a}.
This will only work if @var{filename} does not have a colon in it.

@item -v
Enable verbose output.
More @code{-v} options increase verbosity level.

@item -V
Disable automatic verify check when uploading data.

@item -x @var{extended_param}
Pass @var{extended_param} to the chosen programmer implementation as
an extended parameter.  The interpretation of the extended parameter
depends on the programmer itself.  See below for a list of programmers
accepting extended parameters.

@end table

@page
@c
@c Node
@c
@node Programmers accepting extended parameters, Example Command Line Invocations, Option Descriptions, Command Line Options
@section Programmers accepting extended parameters

@table @code

@item JTAG ICE mkII/3
@itemx AVR Dragon

When using the JTAG ICE mkII/3 or AVR Dragon in JTAG mode, the
following extended parameter is accepted:
@table @code
@item @samp{jtagchain=UB,UA,BB,BA}
Setup the JTAG scan chain for @var{UB} units before, @var{UA} units
after, @var{BB} bits before, and @var{BA} bits after the target AVR,
respectively.
Each AVR unit within the chain shifts by 4 bits.
Other JTAG units might require a different bit shift count.
@end table

@item AVR910

The AVR910 programmer type accepts the following extended parameter:
@table @code
@item @samp{devcode=VALUE}
Override the device code selection by using @var{VALUE}
as the device code.
The programmer is not queried for the list of supported
device codes, and the specified @var{VALUE}
is not verified but used directly within the
@code{T} command sent to the programmer.
@var{VALUE} can be specified using the conventional number notation of the
C programming language.
@item @samp{no_blockmode}
Disables the default checking for block transfer capability.
Use 
@samp{no_blockmode} only if your @samp{AVR910} 
programmer creates errors during initial sequence.
@end table

@item BusPirate

The BusPirate programmer type accepts the following extended parameters:
@table @code
@item @samp{reset=cs,aux,aux2}
The default setup assumes the BusPirate's CS output pin connected to
the RESET pin on AVR side. It is however possible to have multiple AVRs
connected to the same BP with MISO, MOSI and SCK lines common for all of them.
In such a case one AVR should have its RESET connected to BusPirate's
@emph{CS}
pin, second AVR's RESET connected to BusPirate's
@emph{AUX}
pin and if your BusPirate has an
@emph{AUX2}
pin (only available on BusPirate version v1a with firmware 3.0 or newer)
use that to activate RESET on the third AVR.

It may be a good idea to decouple the BusPirate and the AVR's SPI buses from
each other using a 3-state bus buffer. For example 74HC125 or 74HC244 are some
good candidates with the latches driven by the appropriate reset pin (cs,
aux or aux2). Otherwise the SPI traffic in one active circuit may interfere
with programming the AVR in the other design.

@item @samp{spifreq=@var{0..7}}
@multitable @columnfractions .05 .3
@item @code{0} @tab  30 kHz (default)
@item @code{1} @tab 125 kHz
@item @code{2} @tab 250 kHz
@item @code{3} @tab   1 MHz
@item @code{4} @tab   2 MHz
@item @code{5} @tab   2.6 MHz
@item @code{6} @tab   4 MHz
@item @code{7} @tab   8 MHz
@end multitable

@item @samp{rawfreq=0..3}
Sets the SPI speed and uses the Bus Pirate's binary ``raw-wire'' mode instead
of the default binary SPI mode:

@multitable @columnfractions .05 .3
@item @code{0} @tab 5 kHz
@item @code{1} @tab 50 kHz
@item @code{2} @tab 100 kHz (Firmware v4.2+ only)
@item @code{3} @tab 400 kHz (v4.2+)
@end multitable

The only advantage of the ``raw-wire'' mode is that different SPI frequencies
are available. Paged writing is not implemented in this mode.

@item @samp{ascii}
Attempt to use ASCII mode even when the firmware supports BinMode (binary
mode).
BinMode is supported in firmware 2.7 and newer, older FW's either don't
have BinMode or their BinMode is buggy. ASCII mode is slower and makes
the above
@samp{reset=}, @samp{spifreq=}
and
@samp{rawfreq=}
parameters unavailable. Be aware that ASCII mode is not guaranteed to work
with newer firmware versions, and is retained only to maintain compatibility
with older firmware versions.

@item @samp{nopagedwrite}
Firmware versions 5.10 and newer support a binary mode SPI command that enables
whole pages to be written to AVR flash memory at once, resulting in a
significant write speed increase. If use of this mode is not desirable for some
reason, this option disables it.

@item @samp{nopagedread}
Newer firmware versions support in binary mode SPI command some AVR Extended 
Commands. Using the ``Bulk Memory Read from Flash'' results in a
significant read speed increase. If use of this mode is not desirable for some
reason, this option disables it.

@item @samp{cpufreq=@var{125..4000}}
This sets the @emph{AUX}  pin to output a frequency of @var{n} kHz. Connecting
the @emph{AUX} pin to the XTAL1 pin of your MCU, you can provide it a clock, 
for example when it needs an external clock because of wrong fuses settings.
Make sure the CPU frequency is at least four times the SPI frequency.  

@item @samp{serial_recv_timeout=@var{1...}}
This sets the serial receive timeout to the given value. 
The timeout happens every time avrdude waits for the BusPirate prompt. 
Especially in ascii mode this happens very often, so setting a smaller value 
can speed up programming a lot. 
The default value is 100ms. Using 10ms might work in most cases.  

@end table

@item Wiring

When using the Wiring programmer type, the
following optional extended parameter is accepted:
@table @code
@item @samp{snooze=@var{0..32767}}
After performing the port open phase, AVRDUDE will wait/snooze for
@var{snooze} milliseconds before continuing to the protocol sync phase.
No toggling of DTR/RTS is performed if @var{snooze} > 0.
@end table

@item PICkit2
Connection to the PICkit2 programmer:
@multitable @columnfractions .05 .3
@item @code{(AVR)} @tab      @code{(PICkit2)}
@item @code{RST} @tab      @code{VPP/MCLR (1) }
@item @code{VDD} @tab      @code{VDD Target (2) -- possibly optional if AVR self powered }
@item @code{GND} @tab      @code{GND (3) }
@item @code{MISO} @tab      @code{PGD (4) }
@item @code{SCLK} @tab      @code{PDC (5) }
@item @code{OSI} @tab      @code{AUX (6) }
@end multitable

Extended command line parameters:
@table @code
@item @samp{clockrate=@var{rate}}
Sets the SPI clocking rate in Hz (default is 100kHz). Alternately the -B or -i options can be used to set the period.
@item @samp{timeout=@var{usb-transaction-timeout}}
Sets the timeout for USB reads and writes in milliseconds (default is 1500 ms).
@end table

@end table

@page
@c
@c Node
@c
@node Example Command Line Invocations,  , Programmers accepting extended parameters, Command Line Options
@section Example Command Line Invocations

@noindent
Download the file @code{diag.hex} to the ATmega128 chip using the
STK500 programmer connected to the default serial port:

@smallexample
@cartouche
% avrdude -p m128 -c stk500 -e -U flash:w:diag.hex 

avrdude: AVR device initialized and ready to accept instructions

Reading | ################################################## | 100% 0.03s

avrdude: Device signature = 0x1e9702
avrdude: erasing chip
avrdude: done.
avrdude: performing op: 1, flash, 0, diag.hex
avrdude: reading input file "diag.hex"
avrdude: input file diag.hex auto detected as Intel Hex
avrdude: writing flash (19278 bytes):

Writing | ################################################## | 100% 7.60s

avrdude: 19456 bytes of flash written
avrdude: verifying flash memory against diag.hex:
avrdude: load data flash data from input file diag.hex:
avrdude: input file diag.hex auto detected as Intel Hex
avrdude: input file diag.hex contains 19278 bytes
avrdude: reading on-chip flash data:

Reading | ################################################## | 100% 6.83s

avrdude: verifying ...
avrdude: 19278 bytes of flash verified

avrdude: safemode: Fuses OK

avrdude done.  Thank you.

%
@end cartouche
@end smallexample

@page
@noindent
Upload the flash memory from the ATmega128 connected to the STK500
programmer and save it in raw binary format in the file named
@code{c:/diag flash.bin}:

@smallexample
@cartouche
% avrdude -p m128 -c stk500 -U flash:r:"c:/diag flash.bin":r 

avrdude: AVR device initialized and ready to accept instructions

Reading | ################################################## | 100% 0.03s

avrdude: Device signature = 0x1e9702
avrdude: reading flash memory:

Reading | ################################################## | 100% 46.10s

avrdude: writing output file "c:/diag flash.bin"

avrdude: safemode: Fuses OK

avrdude done.  Thank you.

% 
@end cartouche
@end smallexample

@page
@noindent
Using the default programmer, download the file @code{diag.hex} to
flash, @code{eeprom.hex} to EEPROM, and set the Extended, High, and Low
fuse bytes to 0xff, 0x89, and 0x2e respectively:

@smallexample
@cartouche

% avrdude -p m128 -u -U flash:w:diag.hex \
>                 -U eeprom:w:eeprom.hex \
>                 -U efuse:w:0xff:m      \
>                 -U hfuse:w:0x89:m      \
>                 -U lfuse:w:0x2e:m

avrdude: AVR device initialized and ready to accept instructions

Reading | ################################################## | 100% 0.03s

avrdude: Device signature = 0x1e9702
avrdude: NOTE: FLASH memory has been specified, an erase cycle will be performed
         To disable this feature, specify the -D option.
avrdude: erasing chip
avrdude: reading input file "diag.hex"
avrdude: input file diag.hex auto detected as Intel Hex
avrdude: writing flash (19278 bytes):

Writing | ################################################## | 100% 7.60s

avrdude: 19456 bytes of flash written
avrdude: verifying flash memory against diag.hex:
avrdude: load data flash data from input file diag.hex:
avrdude: input file diag.hex auto detected as Intel Hex
avrdude: input file diag.hex contains 19278 bytes
avrdude: reading on-chip flash data:

Reading | ################################################## | 100% 6.84s

avrdude: verifying ...
avrdude: 19278 bytes of flash verified

[ ... other memory status output skipped for brevity ... ]

avrdude done.  Thank you.

% 
@end cartouche
@end smallexample

@page
@noindent
Connect to the JTAG ICE mkII which serial number ends up in 1C37 via
USB, and enter terminal mode:

@smallexample
@cartouche

% avrdude -c jtag2 -p m649 -P usb:1c:37 -t

avrdude: AVR device initialized and ready to accept instructions

Reading | ################################################## | 100% 0.03s

avrdude: Device signature = 0x1e9603

[ ... terminal mode output skipped for brevity ... ]

avrdude done.  Thank you.

@end cartouche
@end smallexample

@noindent
List the serial numbers of all JTAG ICEs attached to USB.  This is
done by specifying an invalid serial number, and increasing the
verbosity level.

@smallexample
@cartouche

% avrdude -c jtag2 -p m128 -P usb:xx -v
[...]
         Using Port            : usb:xxx
         Using Programmer      : jtag2
avrdude: usbdev_open(): Found JTAG ICE, serno: 00A000001C6B
avrdude: usbdev_open(): Found JTAG ICE, serno: 00A000001C3A
avrdude: usbdev_open(): Found JTAG ICE, serno: 00A000001C30
avrdude: usbdev_open(): did not find any (matching) USB device "usb:xxx"

@end cartouche
@end smallexample


@c
@c Node
@c
@node Terminal Mode Operation, Configuration File, Command Line Options, Top
@chapter Terminal Mode Operation

AVRDUDE has an interactive mode called @var{terminal mode} that is
enabled by the @option{-t} option.  This mode allows one to enter
interactive commands to display and modify the various device memories,
perform a chip erase, display the device signature bytes and part
parameters, and to send raw programming commands.  Commands and
parameters may be abbreviated to their shortest unambiguous form.
Terminal mode also supports a command history so that previously entered
commands can be recalled and edited.

@menu
* Terminal Mode Commands::      
* Terminal Mode Examples::      
@end menu

@node Terminal Mode Commands, Terminal Mode Examples, Terminal Mode Operation, Terminal Mode Operation
@section Terminal Mode Commands

@noindent
The following commands are implemented:

@table @code

@item dump @var{memtype} @var{addr} @var{nbytes}
Read @var{nbytes} from the specified memory area, and display them in
the usual hexadecimal and ASCII form.

@item dump
Continue dumping the memory contents for another @var{nbytes} where the
previous dump command left off.

@item write @var{memtype} @var{addr} @var{byte1} @dots{} @var{byteN}
Manually program the respective memory cells, starting at address addr,
using the values @var{byte1} through @var{byteN}.  This feature is not
implemented for bank-addressed memories such as the flash memory of
ATMega devices.

@item erase
Perform a chip erase.

@item send @var{b1} @var{b2} @var{b3} @var{b4}
Send raw instruction codes to the AVR device.  If you need access to a
feature of an AVR part that is not directly supported by AVRDUDE, this
command allows you to use it, even though AVRDUDE does not implement the
command.   When using direct SPI mode, up to 3 bytes
can be omitted.

@item sig
Display the device signature bytes.

@item spi
Enter direct SPI mode.  The @emph{pgmled} pin acts as slave select.
@emph{Only supported on parallel bitbang programmers.}

@item part
Display the current part settings and parameters.  Includes chip
specific information including all memory types supported by the
device, read/write timing, etc.

@item pgm
Return to programming mode (from direct SPI mode).

@item verbose [@var{level}]
Change (when @var{level} is provided), or display the verbosity
level.
The initial verbosity level is controlled by the number of @code{-v} options
given on the command line.

@item ?
@itemx help
Give a short on-line summary of the available commands.

@item quit
Leave terminal mode and thus AVRDUDE.

@end table

@noindent
In addition, the following commands are supported on the STK500
and STK600 programmer:

@table @code

@item vtarg @var{voltage}
Set the target's supply voltage to @var{voltage} Volts.

@item varef [@var{channel}] @var{voltage}
Set the adjustable voltage source to @var{voltage} Volts.
This voltage is normally used to drive the target's
@emph{Aref} input on the STK500 and STK600.
The STK600 offers two reference voltages, which can be
selected by the optional parameter @var{channel} (either
0 or 1).

@item fosc @var{freq}[@code{M}|@code{k}]
Set the master oscillator to @var{freq} Hz.
An optional trailing letter @code{M}
multiplies by 1E6, a trailing letter @code{k} by 1E3.

@item fosc off
Turn the master oscillator off.

@item sck @var{period}
@emph{STK500 and STK600 only:}
Set the SCK clock period to @var{period} microseconds.

@emph{JTAG ICE only:}
Set the JTAG ICE bit clock period to @var{period} microseconds.
Note that unlike STK500 settings, this setting will be reverted to
its default value (approximately 1 microsecond) when the programming
software signs off from the JTAG ICE.
This parameter can also be used on the JTAG ICE mkII/3 to specify the
ISP clock period when operating the ICE in ISP mode.

@item parms
@emph{STK500 and STK600 only:}
Display the current voltage and master oscillator parameters.

@emph{JTAG ICE only:}
Display the current target supply voltage and JTAG bit clock rate/period.

@end table

@c
@c Node
@c
@node Terminal Mode Examples,  , Terminal Mode Commands, Terminal Mode Operation
@section Terminal Mode Examples

@noindent
Display part parameters, modify eeprom cells, perform a chip erase:

@smallexample
@cartouche
% avrdude -p m128 -c stk500 -t

avrdude: AVR device initialized and ready to accept instructions
avrdude: Device signature = 0x1e9702
avrdude: current erase-rewrite cycle count is 52 (if being tracked)
avrdude> part
>>> part 

AVR Part              : ATMEGA128
Chip Erase delay      : 9000 us
PAGEL                 : PD7
BS2                   : PA0
RESET disposition     : dedicated
RETRY pulse           : SCK
serial program mode   : yes
parallel program mode : yes
Memory Detail         :

                            Page                       Polled
  Memory Type Paged  Size   Size #Pages MinW  MaxW   ReadBack
  ----------- ------ ------ ---- ------ ----- ----- ---------
  eeprom      no       4096    8     0  9000  9000 0xff 0xff
  flash       yes    131072  256   512  4500  9000 0xff 0x00
  lfuse       no          1    0     0     0     0 0x00 0x00
  hfuse       no          1    0     0     0     0 0x00 0x00
  efuse       no          1    0     0     0     0 0x00 0x00
  lock        no          1    0     0     0     0 0x00 0x00
  calibration no          1    0     0     0     0 0x00 0x00
  signature   no          3    0     0     0     0 0x00 0x00

avrdude> dump eeprom 0 16
>>> dump eeprom 0 16 
0000  ff ff ff ff ff ff ff ff  ff ff ff ff ff ff ff ff  |................|

avrdude> write eeprom 0 1 2 3 4
>>> write eeprom 0 1 2 3 4 

avrdude> dump eeprom 0 16
>>> dump eeprom 0 16 
0000  01 02 03 04 ff ff ff ff  ff ff ff ff ff ff ff ff  |................|

avrdude> erase
>>> erase 
avrdude: erasing chip
avrdude> dump eeprom 0 16
>>> dump eeprom 0 16 
0000  ff ff ff ff ff ff ff ff  ff ff ff ff ff ff ff ff  |................|

avrdude> 
@end cartouche
@end smallexample


@noindent
Program the fuse bits of an ATmega128 (disable M103 compatibility,
enable high speed external crystal, enable brown-out detection, slowly
rising power).  Note since we are working with fuse bits the -u (unsafe)
option is specified, which allows you to modify the fuse bits. First 
display the factory defaults, then reprogram:

@smallexample
@cartouche
% avrdude -p m128 -u -c stk500 -t

avrdude: AVR device initialized and ready to accept instructions
avrdude: Device signature = 0x1e9702
avrdude: current erase-rewrite cycle count is 52 (if being tracked)
avrdude> d efuse
>>> d efuse 
0000  fd                                                |.               |

avrdude> d hfuse
>>> d hfuse 
0000  99                                                |.               |

avrdude> d lfuse
>>> d lfuse 
0000  e1                                                |.               |

avrdude> w efuse 0 0xff
>>> w efuse 0 0xff 

avrdude> w hfuse 0 0x89
>>> w hfuse 0 0x89 

avrdude> w lfuse 0 0x2f
>>> w lfuse 0 0x2f 

avrdude> 
@end cartouche
@end smallexample


@c
@c Node
@c
@node Configuration File, Programmer Specific Information, Terminal Mode Operation, Top
@chapter Configuration File

@noindent
AVRDUDE reads a configuration file upon startup which describes all of
the parts and programmers that it knows about.  The advantage of this is
that if you have a chip that is not currently supported by AVRDUDE, you
can add it to the configuration file without waiting for a new release
of AVRDUDE.  Likewise, if you have a parallel port programmer that is
not supported by AVRDUDE, chances are good that you can copy and
existing programmer definition, and with only a few changes, make your
programmer work with AVRDUDE.

AVRDUDE first looks for a system wide configuration file in a platform
dependent location.  On Unix, this is usually
@code{/usr/local/etc/avrdude.conf}, while on Windows it is usually in the
same location as the executable file.  The name of this file can be
changed using the @option{-C} command line option.  After the system wide
configuration file is parsed, AVRDUDE looks for a per-user configuration
file to augment or override the system wide defaults.  On Unix, the
per-user file is @code{.avrduderc} within the user's home directory.  On
Windows, this file is the @code{avrdude.rc} file located in the same
directory as the executable.

@menu
* AVRDUDE Defaults::            
* Programmer Definitions::      
* Part Definitions::            
* Other Notes::                 
@end menu

@c
@c Node
@c
@node AVRDUDE Defaults, Programmer Definitions, Configuration File, Configuration File
@section AVRDUDE Defaults

@table @code

@item default_parallel = "@var{default-parallel-device}";
Assign the default parallel port device.  Can be overridden using the
@option{-P} option.

@item default_serial = "@var{default-serial-device}";
Assign the default serial port device.  Can be overridden using the
@option{-P} option.

@item default_programmer = "@var{default-programmer-id}";
Assign the default programmer id.  Can be overridden using the @option{-c}
option.

@item default_bitclock = "@var{default-bitclock}";
Assign the default bitclock value.  Can be overridden using the @option{-B}
option.

@end table


@c
@c Node
@c
@node Programmer Definitions, Part Definitions, AVRDUDE Defaults, Configuration File
@section Programmer Definitions

@noindent
The format of the programmer definition is as follows:

@smallexample
programmer
    parent <id>                                 # <id> is a quoted string
    id       = <id1> [, <id2> [, <id3>] ...] ;  # <idN> are quoted strings
    desc     = <description> ;                  # quoted string
    type     = "par" | "stk500" | ... ;         # programmer type (see below for a list)
    baudrate = <num> ;                          # baudrate for serial ports
    vcc      = <num1> [, <num2> ... ] ;         # pin number(s)
    buff     = <num1> [, <num2> ... ] ;         # pin number(s)
    reset    = <num> ;                          # pin number
    sck      = <num> ;                          # pin number
    mosi     = <num> ;                          # pin number
    miso     = <num> ;                          # pin number
    errled   = <num> ;                          # pin number
    rdyled   = <num> ;                          # pin number
    pgmled   = <num> ;                          # pin number
    vfyled   = <num> ;                          # pin number
    usbvid   = <hexnum>;                        # USB VID (Vendor ID)
    usbpid   = <hexnum> [, <hexnum> ...];       # USB PID (Product ID)
    usbdev   = <interface>;                     # USB interface or other device info 
    usbvendor = <vendorname>;                   # USB Vendor Name
    usbproduct = <productname>;                 # USB Product Name
    usbsn    = <serialno>;                      # USB Serial Number
  ;
@end smallexample

@noindent
If a parent is specified, all settings of it (except its ids) are used for the new 
programmer. These values can be changed by new setting them for the new programmer.

@noindent
To invert a bit in the pin definitions, use @code{= ~ <num>}.

@noindent
Not all programmer types can handle a list of USB PIDs.

@noindent
Following programmer types are currently implemented:

@multitable @columnfractions .25 .6
@include programmer_types.texi
@end multitable

@c
@c Node
@c
@node Part Definitions, Other Notes, Programmer Definitions, Configuration File
@section Part Definitions

@smallexample
part
    id               = <id> ;                 # quoted string
    desc             = <description> ;        # quoted string
    has_jtag         = <yes/no> ;             # part has JTAG i/f
    has_debugwire    = <yes/no> ;             # part has debugWire i/f
    has_pdi          = <yes/no> ;             # part has PDI i/f
    has_tpi          = <yes/no> ;             # part has TPI i/f
    devicecode       = <num> ;                # numeric
    stk500_devcode   = <num> ;                # numeric
    avr910_devcode   = <num> ;                # numeric
    signature        = <num> <num> <num> ;    # signature bytes
    usbpid           = <num> ;                # DFU USB PID
    reset            = dedicated | io;
    retry_pulse      = reset | sck;
    pgm_enable       = <instruction format> ;
    chip_erase       = <instruction format> ;
    chip_erase_delay = <num> ;                # micro-seconds
    # STK500 parameters (parallel programming IO lines)
    pagel            = <num> ;                # pin name in hex, i.e., 0xD7
    bs2              = <num> ;                # pin name in hex, i.e., 0xA0
    serial           = <yes/no> ;             # can use serial downloading
    parallel         = <yes/no/pseudo>;       # can use par. programming
    # STK500v2 parameters, to be taken from Atmel's XML files
    timeout          = <num> ;
    stabdelay        = <num> ;
    cmdexedelay      = <num> ;
    synchloops       = <num> ;
    bytedelay        = <num> ;
    pollvalue        = <num> ;
    pollindex        = <num> ;
    predelay         = <num> ;
    postdelay        = <num> ;
    pollmethod       = <num> ;
    mode             = <num> ;
    delay            = <num> ;
    blocksize        = <num> ;
    readsize         = <num> ;
    hvspcmdexedelay  = <num> ;
    # STK500v2 HV programming parameters, from XML
    pp_controlstack  = <num>, <num>, ...;     # PP only
    hvsp_controlstack = <num>, <num>, ...;    # HVSP only
    hventerstabdelay = <num>;
    progmodedelay    = <num>;                 # PP only
    latchcycles      = <num>;
    togglevtg        = <num>;
    poweroffdelay    = <num>;
    resetdelayms     = <num>;
    resetdelayus     = <num>;
    hvleavestabdelay = <num>;
    resetdelay       = <num>;
    synchcycles      = <num>;                 # HVSP only
    chiperasepulsewidth = <num>;              # PP only
    chiperasepolltimeout = <num>;
    chiperasetime    = <num>;                 # HVSP only
    programfusepulsewidth = <num>;            # PP only
    programfusepolltimeout = <num>;
    programlockpulsewidth = <num>;            # PP only
    programlockpolltimeout = <num>;
    # JTAG ICE mkII parameters, also from XML files
    allowfullpagebitstream = <yes/no> ;
    enablepageprogramming = <yes/no> ;
    idr              = <num> ;                # IO addr of IDR (OCD) reg.
    rampz            = <num> ;                # IO addr of RAMPZ reg.
    spmcr            = <num> ;                # mem addr of SPMC[S]R reg.
    eecr             = <num> ;                # mem addr of EECR reg.
                                              # (only when != 0x3c)
    is_at90s1200     = <yes/no> ;             # AT90S1200 part
    is_avr32         = <yes/no> ;             # AVR32 part

    memory <memtype>
        paged           = <yes/no> ;          # yes / no
        size            = <num> ;             # bytes
        page_size       = <num> ;             # bytes
        num_pages       = <num> ;             # numeric
        min_write_delay = <num> ;             # micro-seconds
        max_write_delay = <num> ;             # micro-seconds
        readback_p1     = <num> ;             # byte value
        readback_p2     = <num> ;             # byte value
        pwroff_after_write = <yes/no> ;       # yes / no
        read            = <instruction format> ;
        write           = <instruction format> ;
        read_lo         = <instruction format> ;
        read_hi         = <instruction format> ;
        write_lo        = <instruction format> ;
        write_hi        = <instruction format> ;
        loadpage_lo     = <instruction format> ;
        loadpage_hi     = <instruction format> ;
        writepage       = <instruction format> ;
      ;
  ;
@end smallexample

@menu
* Parent Part::
* Instruction Format::
@end menu

@c
@c Node
@c
@node Parent Part, Instruction Format, Part Definitions, Part Definitions
@subsection Parent Part

@noindent
Parts can also inherit parameters from previously defined parts
using the following syntax. In this case specified integer and 
string values override parameter values from the parent part. New 
memory definitions are added to the definitions inherited from the 
parent.

@smallexample
   part parent <id>                              # quoted string
       id               = <id> ;                 # quoted string
       <any set of other parameters from the list above>
     ;
@end smallexample

@c
@c Node
@c
@node Instruction Format,  , Parent Part, Part Definitions
@subsection Instruction Format

@noindent
Instruction formats are specified as a comma separated list of string
values containing information (bit specifiers) about each of the 32 bits
of the instruction.  Bit specifiers may be one of the following formats:

@table @code

@item 1
The bit is always set on input as well as output

@item 0
the bit is always clear on input as well as output

@item x
the bit is ignored on input and output

@item a
the bit is an address bit, the bit-number matches this bit specifier's
position within the current instruction byte

@item a@var{N}
the bit is the @var{N}th address bit, bit-number = N, i.e., @code{a12}
is address bit 12 on input, @code{a0} is address bit 0.

@item i
the bit is an input data bit

@item o
the bit is an output data bit

@end table

Each instruction must be composed of 32 bit specifiers.  The instruction
specification closely follows the instruction data provided in Atmel's
data sheets for their parts.  For example, the EEPROM read and write
instruction for an AT90S2313 AVR part could be encoded as:

@smallexample

read  = "1  0  1  0   0  0  0  0   x x x x  x x x x",
        "x a6 a5 a4  a3 a2 a1 a0   o o o o  o o o o";

write = "1  1  0  0   0  0  0  0   x x x x  x x x x",
        "x a6 a5 a4  a3 a2 a1 a0   i i i i  i i i i";

@end smallexample



@c
@c Node
@c
@node Other Notes,  , Part Definitions, Configuration File
@section Other Notes


@itemize @bullet
@item
The @code{devicecode} parameter is the device code used by the STK500
and is obtained from the software section (@code{avr061.zip}) of
Atmel's AVR061 application note available from
@url{http://www.atmel.com/dyn/resources/prod_documents/doc2525.pdf}.

@item
Not all memory types will implement all instructions.

@item
AVR Fuse bits and Lock bits are implemented as a type of memory.

@item
Example memory types are: @code{flash}, @code{eeprom}, @code{fuse},
@code{lfuse} (low fuse), @code{hfuse} (high fuse), @code{efuse}
(extended fuse), @code{signature}, @code{calibration}, @code{lock}.

@item
The memory type specified on the AVRDUDE command line must match one of
the memory types defined for the specified chip.

@item
The @code{pwroff_after_write} flag causes AVRDUDE to attempt to power
the device off and back on after an unsuccessful write to the affected
memory area if VCC programmer pins are defined.  If VCC pins are not
defined for the programmer, a message indicating that the device needs a
power-cycle is printed out.  This flag was added to work around a
problem with the at90s4433/2333's; see the at90s4433 errata at:

         @url{http://www.atmel.com/dyn/resources/prod_documents/doc1280.pdf}

@item
The boot loader from application note AVR109 (and thus also the AVR
Butterfly) does not support writing of fuse bits.  Writing lock bits
is supported, but is restricted to the boot lock bits (BLBxx).  These
are restrictions imposed by the underlying SPM instruction that is used
to program the device from inside the boot loader.  Note that programming
the boot lock bits can result in a ``shoot-into-your-foot'' scenario as
the only way to unprogram these bits is a chip erase, which will also
erase the boot loader code.

The boot loader implements the ``chip erase'' function by erasing the
flash pages of the application section.

Reading fuse and lock bits is fully supported.

Note that due to the inability to write the fuse bits, the safemode
functionality does not make sense for these boot loaders.

@end itemize

@c
@c Node
@c
@node Programmer Specific Information, Platform Dependent Information, Configuration File, Top
@chapter Programmer Specific Information

@menu
* Atmel STK600::
* Atmel DFU bootloader using FLIP version 1::
@end menu

@c
@c Node
@c
@node Atmel STK600, Atmel DFU bootloader using FLIP version 1, Programmer Specific Information, Programmer Specific Information
@section Atmel STK600

@c
@c Update the table below by running the tools/get-stk600-devices.xsl
@c XSLT transformation on targetboard.xml as shipped by the latest
@c release of AVR Studio.
@c
The following devices are supported by the respective STK600 routing
and socket card:

@multitable @columnfractions .25 .25 .5
@headitem Routing card @tab Socket card @tab Devices
@item @code{} @tab @code{STK600-ATTINY10} @tab ATtiny4 ATtiny5 ATtiny9 ATtiny10
@item @code{STK600-RC008T-2} @tab @code{STK600-DIP} @tab ATtiny11 ATtiny12 ATtiny13 ATtiny13A ATtiny25 ATtiny45 ATtiny85
@item @code{STK600-RC008T-7} @tab @code{STK600-DIP} @tab ATtiny15
@item @code{STK600-RC014T-42} @tab @code{STK600-SOIC} @tab ATtiny20
@item @code{STK600-RC020T-1} @tab @code{STK600-DIP} @tab ATtiny2313 ATtiny2313A ATtiny4313
@item @code{} @tab @code{STK600-TinyX3U} @tab ATtiny43U
@item @code{STK600-RC014T-12} @tab @code{STK600-DIP} @tab ATtiny24 ATtiny44 ATtiny84 ATtiny24A ATtiny44A
@item @code{STK600-RC020T-8} @tab @code{STK600-DIP} @tab ATtiny26 ATtiny261 ATtiny261A ATtiny461 ATtiny861 ATtiny861A
@item @code{STK600-RC020T-43} @tab @code{STK600-SOIC} @tab ATtiny261 ATtiny261A ATtiny461 ATtiny461A ATtiny861 ATtiny861A
@item @code{STK600-RC020T-23} @tab @code{STK600-SOIC} @tab ATtiny87 ATtiny167
@item @code{STK600-RC028T-3} @tab @code{STK600-DIP} @tab ATtiny28
@item @code{STK600-RC028M-6} @tab @code{STK600-DIP} @tab ATtiny48 ATtiny88 ATmega8 ATmega8A ATmega48 ATmega88 ATmega168 ATmega48P ATmega48PA ATmega88P ATmega88PA ATmega168P ATmega168PA ATmega328P
@item @code{} @tab @code{QT600-ATTINY88-QT8} @tab ATtiny88
@item @code{STK600-RC040M-4} @tab @code{STK600-DIP} @tab ATmega8515 ATmega162
@item @code{STK600-RC044M-30} @tab @code{STK600-TQFP44} @tab ATmega8515 ATmega162
@item @code{STK600-RC040M-5} @tab @code{STK600-DIP} @tab ATmega8535 ATmega16 ATmega16A ATmega32 ATmega32A ATmega164P ATmega164PA ATmega324P ATmega324PA ATmega644 ATmega644P ATmega644PA ATmega1284P
@item @code{STK600-RC044M-31} @tab @code{STK600-TQFP44} @tab ATmega8535 ATmega16 ATmega16A ATmega32 ATmega32A ATmega164P ATmega164PA ATmega324P ATmega324PA ATmega644 ATmega644P ATmega644PA ATmega1284P
@item @code{} @tab @code{QT600-ATMEGA324-QM64} @tab ATmega324PA
@item @code{STK600-RC032M-29} @tab @code{STK600-TQFP32} @tab ATmega8 ATmega8A ATmega48 ATmega88 ATmega168 ATmega48P ATmega48PA ATmega88P ATmega88PA ATmega168P ATmega168PA ATmega328P
@item @code{STK600-RC064M-9} @tab @code{STK600-TQFP64} @tab ATmega64 ATmega64A ATmega128 ATmega128A ATmega1281 ATmega2561 AT90CAN32 AT90CAN64 AT90CAN128
@item @code{STK600-RC064M-10} @tab @code{STK600-TQFP64} @tab ATmega165 ATmega165P ATmega169 ATmega169P ATmega169PA ATmega325 ATmega325P ATmega329 ATmega329P ATmega645 ATmega649 ATmega649P
@item @code{STK600-RC100M-11} @tab @code{STK600-TQFP100} @tab ATmega640 ATmega1280 ATmega2560
@item @code{} @tab @code{STK600-ATMEGA2560} @tab ATmega2560
@item @code{STK600-RC100M-18} @tab @code{STK600-TQFP100} @tab ATmega3250 ATmega3250P ATmega3290 ATmega3290P ATmega6450 ATmega6490
@item @code{STK600-RC032U-20} @tab @code{STK600-TQFP32} @tab AT90USB82 AT90USB162 ATmega8U2 ATmega16U2 ATmega32U2 
@item @code{STK600-RC044U-25} @tab @code{STK600-TQFP44} @tab ATmega16U4 ATmega32U4
@item @code{STK600-RC064U-17} @tab @code{STK600-TQFP64} @tab ATmega32U6 AT90USB646 AT90USB1286 AT90USB647 AT90USB1287
@item @code{STK600-RCPWM-22} @tab @code{STK600-TQFP32} @tab ATmega32C1 ATmega64C1 ATmega16M1 ATmega32M1 ATmega64M1
@item @code{STK600-RCPWM-19} @tab @code{STK600-SOIC} @tab AT90PWM2 AT90PWM3 AT90PWM2B AT90PWM3B AT90PWM216 AT90PWM316
@item @code{STK600-RCPWM-26} @tab @code{STK600-SOIC} @tab AT90PWM81
@item @code{STK600-RC044M-24} @tab @code{STK600-TSSOP44} @tab ATmega16HVB ATmega32HVB
@item @code{} @tab @code{STK600-HVE2} @tab ATmega64HVE
@item @code{} @tab @code{STK600-ATMEGA128RFA1} @tab ATmega128RFA1
@item @code{STK600-RC100X-13} @tab @code{STK600-TQFP100} @tab ATxmega64A1 ATxmega128A1 ATxmega128A1_revD ATxmega128A1U
@item @code{} @tab @code{STK600-ATXMEGA1281A1} @tab ATxmega128A1
@item @code{} @tab @code{QT600-ATXMEGA128A1-QT16} @tab ATxmega128A1
@item @code{STK600-RC064X-14} @tab @code{STK600-TQFP64} @tab ATxmega64A3 ATxmega128A3 ATxmega256A3 ATxmega64D3 ATxmega128D3 ATxmega192D3 ATxmega256D3
@item @code{STK600-RC064X-14} @tab @code{STK600-MLF64} @tab ATxmega256A3B
@item @code{STK600-RC044X-15} @tab @code{STK600-TQFP44} @tab ATxmega32A4 ATxmega16A4 ATxmega16D4 ATxmega32D4
@item @code{} @tab @code{STK600-ATXMEGAT0} @tab ATxmega32T0
@item @code{} @tab @code{STK600-uC3-144} @tab AT32UC3A0512 AT32UC3A0256 AT32UC3A0128
@item @code{STK600-RCUC3A144-33} @tab @code{STK600-TQFP144} @tab AT32UC3A0512 AT32UC3A0256 AT32UC3A0128
@item @code{STK600-RCuC3A100-28} @tab @code{STK600-TQFP100} @tab AT32UC3A1512 AT32UC3A1256 AT32UC3A1128
@item @code{STK600-RCuC3B0-21} @tab @code{STK600-TQFP64-2} @tab AT32UC3B0256 AT32UC3B0512RevC AT32UC3B0512 AT32UC3B0128 AT32UC3B064 AT32UC3D1128
@item @code{STK600-RCuC3B48-27} @tab @code{STK600-TQFP48} @tab AT32UC3B1256 AT32UC3B164
@item @code{STK600-RCUC3A144-32} @tab @code{STK600-TQFP144} @tab AT32UC3A3512 AT32UC3A3256 AT32UC3A3128 AT32UC3A364 AT32UC3A3256S AT32UC3A3128S AT32UC3A364S
@item @code{STK600-RCUC3C0-36} @tab @code{STK600-TQFP144} @tab AT32UC3C0512 AT32UC3C0256 AT32UC3C0128 AT32UC3C064
@item @code{STK600-RCUC3C1-38} @tab @code{STK600-TQFP100} @tab AT32UC3C1512 AT32UC3C1256 AT32UC3C1128 AT32UC3C164
@item @code{STK600-RCUC3C2-40} @tab @code{STK600-TQFP64-2} @tab AT32UC3C2512 AT32UC3C2256 AT32UC3C2128 AT32UC3C264
@item @code{STK600-RCUC3C0-37} @tab @code{STK600-TQFP144} @tab AT32UC3C0512 AT32UC3C0256 AT32UC3C0128 AT32UC3C064
@item @code{STK600-RCUC3C1-39} @tab @code{STK600-TQFP100} @tab AT32UC3C1512 AT32UC3C1256 AT32UC3C1128 AT32UC3C164
@item @code{STK600-RCUC3C2-41} @tab @code{STK600-TQFP64-2} @tab AT32UC3C2512 AT32UC3C2256 AT32UC3C2128 AT32UC3C264
@item @code{STK600-RCUC3L0-34} @tab @code{STK600-TQFP48} @tab AT32UC3L064 AT32UC3L032 AT32UC3L016
@item @code{} @tab @code{QT600-AT32UC3L-QM64} @tab AT32UC3L064
@end multitable

Ensure the correct socket and routing card are mounted @emph{before}
powering on the STK600.  While the STK600 firmware ensures the socket
and routing card mounted match each other (using a table stored
internally in nonvolatile memory), it cannot handle the case where a
wrong routing card is used, e. g. the routing card
@code{STK600-RC040M-5} (which is meant for 40-pin DIP AVRs that have
an ADC, with the power supply pins in the center of the package) was
used but an ATmega8515 inserted (which uses the ``industry standard''
pinout with Vcc and GND at opposite corners).

Note that for devices that use the routing card @code{STK600-RC008T-2},
in order to use ISP mode, the jumper for @code{AREF0} must be removed
as it would otherwise block one of the ISP signals.  High-voltage
serial programming can be used even with that jumper installed.

The ISP system of the STK600 contains a detection against shortcuts
and other wiring errors.  AVRDUDE initiates a connection check before
trying to enter ISP programming mode, and display the result if the
target is not found ready to be ISP programmed.

High-voltage programming requires the target voltage to be set to at
least 4.5 V in order to work.  This can be done using
@emph{Terminal Mode}, see @ref{Terminal Mode Operation}.

@c
@c Node
@c
@node Atmel DFU bootloader using FLIP version 1, , Atmel STK600, Programmer Specific Information
@section Atmel DFU bootloader using FLIP version 1

Bootloaders using the FLIP protocol version 1 experience some very
specific behaviour.

These bootloaders have no option to access memory areas other than
Flash and EEPROM.

When the bootloader is started, it enters a @emph{security mode} where
the only acceptable access is to query the device configuration
parameters (which are used for the signature on AVR devices).  The
only way to leave this mode is a @emph{chip erase}.  As a chip erase
is normally implied by the @option{-U} option when reprogramming the
flash, this peculiarity might not be very obvious immediately.

Sometimes, a bootloader with security mode already disabled seems to
no longer respond with sensible configuration data, but only 0xFF for
all queries.  As these queries are used to obtain the equivalent of a
signature, AVRDUDE can only continue in that situation by forcing the
signature check to be overridden with the @option{-F} option.

A @emph{chip erase} might leave the EEPROM unerased, at least on some
versions of the bootloader.

@c
@c Node
@c
@node Platform Dependent Information, Troubleshooting, Programmer Specific Information, Top
@appendix Platform Dependent Information

@menu
* Unix::
* Windows::
@end menu

@c
@c Node
@c
@node Unix, Windows, Platform Dependent Information, Platform Dependent Information
@section Unix

@menu
* Unix Installation::           
* Unix Configuration Files::    
* Unix Port Names::             
* Unix Documentation::          
@end menu

@c
@c Node
@c
@node Unix Installation, Unix Configuration Files, Unix, Unix
@subsection Unix Installation

@noindent
To build and install from the source tarball on Unix like systems:

@example
$ gunzip -c avrdude-@value{VERSION}.tar.gz | tar xf -
$ cd avrdude-@value{VERSION}
$ ./configure
$ make
$ su root -c 'make install'
@end example

The default location of the install is into @code{/usr/local} so you
will need to be sure that @code{/usr/local/bin} is in your @code{PATH}
environment variable.

If you do not have root access to your system, you can do the
following instead:

@example
$ gunzip -c avrdude-@value{VERSION}.tar.gz | tar xf -
$ cd avrdude-@value{VERSION}
$ ./configure --prefix=$HOME/local
$ make
$ make install
@end example

@menu
* FreeBSD Installation::        
* Linux Installation::          
@end menu

@c
@c Node
@c
@node FreeBSD Installation, Linux Installation, Unix Installation, Unix Installation
@subsubsection FreeBSD Installation

@noindent
AVRDUDE is installed via the FreeBSD Ports Tree as follows:

@example
% su - root
# cd /usr/ports/devel/avrdude
# make install
@end example

If you wish to install from a pre-built package instead of the source,
you can use the following instead:

@example
% su - root
# pkg_add -r avrdude
@end example

Of course, you must be connected to the Internet for these methods to
work, since that is where the source as well as the pre-built package is
obtained.

@c
@c Node
@c
@node Linux Installation,  , FreeBSD Installation, Unix Installation
@subsubsection Linux Installation

@noindent
On rpm based Linux systems (such as RedHat, SUSE, Mandrake, etc.), you
can build and install the rpm binaries directly from the tarball:

@example
$ su - root
# rpmbuild -tb avrdude-@value{VERSION}.tar.gz
# rpm -Uvh /usr/src/redhat/RPMS/i386/avrdude-@value{VERSION}-1.i386.rpm
@end example

Note that the path to the resulting rpm package, differs from system
to system. The above example is specific to RedHat.

@c
@c Node
@c
@node Unix Configuration Files, Unix Port Names, Unix Installation, Unix
@subsection Unix Configuration Files

@noindent
When AVRDUDE is build using the default @option{--prefix} configure
option, the default configuration file for a Unix system is located at
@code{/usr/local/etc/avrdude.conf}.  This can be overridden by using the
@option{-C} command line option.  Additionally, the user's home directory
is searched for a file named @code{.avrduderc}, and if found, is used to
augment the system default configuration file.

@menu
* FreeBSD Configuration Files::  
* Linux Configuration Files::   
@end menu

@c
@c Node
@c
@node FreeBSD Configuration Files, Linux Configuration Files, Unix Configuration Files, Unix Configuration Files
@subsubsection FreeBSD Configuration Files

@noindent
When AVRDUDE is installed using the FreeBSD ports system, the system
configuration file is always @code{/usr/local/etc/avrdude.conf}.

@c
@c Node
@c
@node Linux Configuration Files,  , FreeBSD Configuration Files, Unix Configuration Files
@subsubsection Linux Configuration Files

@noindent
When AVRDUDE is installed using from an rpm package, the system
configuration file will be always be @code{/etc/avrdude.conf}.

@c
@c Node
@c
@node Unix Port Names, Unix Documentation, Unix Configuration Files, Unix
@subsection Unix Port Names

@noindent
The parallel and serial port device file names are system specific.
The following table lists the default names for a given system.

@multitable @columnfractions .30 .30 .30
@item @strong{System}
  @tab @strong{Default Parallel Port}
  @tab @strong{Default Serial Port}
@item FreeBSD
  @tab @code{/dev/ppi0}
  @tab @code{/dev/cuad0}
@item Linux
  @tab @code{/dev/parport0}
  @tab @code{/dev/ttyS0}
@item Solaris
  @tab @code{/dev/printers/0}
  @tab @code{/dev/term/a}
@end multitable

On FreeBSD systems, AVRDUDE uses the ppi(4) interface for
accessing the parallel port and the sio(4) driver for serial port
access.

On Linux systems, AVRDUDE uses the ppdev interface for
accessing the parallel port and the tty driver for serial port
access.

On Solaris systems, AVRDUDE uses the ecpp(7D) driver for
accessing the parallel port and the asy(7D) driver for serial port
access.

@c
@c Node
@c
@node Unix Documentation,  , Unix Port Names, Unix
@subsection Unix Documentation

@noindent
AVRDUDE installs a manual page as well as info, HTML and PDF
documentation.  The manual page is installed in
@code{/usr/local/man/man1} area, while the HTML and PDF documentation
is installed in @code{/usr/local/share/doc/avrdude} directory.  The
info manual is installed in @code{/usr/local/info/avrdude.info}.

Note that these locations can be altered by various configure options
such as @option{--prefix}.

@c
@c Node
@c
@node Windows,  , Unix, Platform Dependent Information
@section Windows

@menu
* Windows Installation::        
* Windows Configuration Files::  
* Windows Port Names::          
* Using the parallel port::     
* Documentation::               
* Credits.::                    
@end menu

@c
@c Node
@c
@node Windows Installation, Windows Configuration Files, Windows, Windows
@subsection Installation

@noindent
A Windows executable of avrdude is included in WinAVR which can be found at
@url{http://sourceforge.net/projects/winavr}. WinAVR is a suite of executable, 
open source software development tools for the AVR for the Windows platform.

There are two options to build avrdude from source under Windows.
The first one is to use Cygwin (@url{http://www.cygwin.com/}).

To build and install from the source tarball for Windows (using Cygwin):

@example
$ set PREFIX=<your install directory path>
$ export PREFIX
$ gunzip -c avrdude-@value{VERSION}.tar.gz | tar xf -
$ cd avrdude-@value{VERSION}
$ ./configure LDFLAGS="-static" --prefix=$PREFIX --datadir=$PREFIX 
--sysconfdir=$PREFIX/bin --enable-versioned-doc=no
$ make
$ make install
@end example

Note that recent versions of Cygwin (starting with 1.7) removed the
MinGW support from the compiler that is needed in order to build a
native Win32 API binary that does not require to install the Cygwin
library @code{cygwin1.dll} at run-time.  Either try using an older
compiler version that still supports MinGW builds, or use MinGW
(@url{http://www.mingw.org/}) directly.

@c
@c XXX Please add more detailed instructions here.
@c


@c
@c Node
@c
@node Windows Configuration Files, Windows Port Names, Windows Installation, Windows
@subsection Configuration Files

@menu
* Configuration file names::    
* How AVRDUDE finds the configuration files.::  
@end menu

@c
@c Node
@c
@node Configuration file names, How AVRDUDE finds the configuration files., Windows Configuration Files, Windows Configuration Files
@subsubsection Configuration file names

@noindent
AVRDUDE on Windows looks for a system configuration file name of
@code{avrdude.conf} and looks for a user override configuration file of
@code{avrdude.rc}.

@c
@c Node
@c
@node How AVRDUDE finds the configuration files.,  , Configuration file names, Windows Configuration Files
@subsubsection How AVRDUDE finds the configuration files.
 
@noindent
AVRDUDE on Windows has a different way of searching for the system and
user configuration files. Below is the search method for locating the
configuration files:

@enumerate

@item
The directory from which the application loaded.

@item
The current directory.

@item
The Windows system directory. On Windows NT, the name of this directory
is @code{SYSTEM32}.

@item
Windows NT: The 16-bit Windows system directory. The name of this
directory is @code{SYSTEM}.

@item
The Windows directory.

@item
The directories that are listed in the PATH environment variable.

@end enumerate


@c
@c Node
@c
@node Windows Port Names, Using the parallel port, Windows Configuration Files, Windows
@subsection Port Names

@menu
* Serial Ports::                
* Parallel Ports::              
@end menu

@c
@c Node
@c
@node Serial Ports, Parallel Ports, Windows Port Names, Windows Port Names
@subsubsection Serial Ports
 
@noindent
When you select a serial port (i.e. when using an STK500) use the
Windows serial port device names such as: com1, com2, etc.
 
@c
@c Node
@c
@node Parallel Ports,  , Serial Ports, Windows Port Names
@subsubsection Parallel Ports

@noindent
AVRDUDE will accept 3 Windows parallel port names: lpt1, lpt2, or
lpt3.  Each of these names corresponds to a fixed parallel port base
address:

@table @code
@item lpt1
0x378

@item lpt2
0x278

@item lpt3
0x3BC

@end table

On your desktop PC, lpt1 will be the most common choice. If you are
using a laptop, you might have to use lpt3 instead of lpt1. Select the
name of the port the corresponds to the base address of the parallel
port that you want.

If the parallel port can be accessed through a different
address, this address can be specified directly, using the common C
language notation (i. e., hexadecimal values are prefixed by @code{0x}).

@c
@c Node
@c 
@node Using the parallel port, Documentation, Windows Port Names, Windows
@subsection Using the parallel port

@menu
* Windows NT/2K/XP::            
* Windows 95/98::               
@end menu

@c
@c Node
@c
@node Windows NT/2K/XP, Windows 95/98, Using the parallel port, Using the parallel port
@subsubsection Windows NT/2K/XP

@noindent
On Windows NT, 2000, and XP user applications cannot directly access the
parallel port. However, kernel mode drivers can access the parallel port.
giveio.sys is a driver that can allow user applications to set the state
of the parallel port pins.

Before using AVRDUDE, the giveio.sys driver must be loaded. The
accompanying command-line program, loaddrv.exe, can do just that.

To make things even easier there are 3 batch files that are also
included:

@enumerate
@item install_giveio.bat
Install and start the giveio driver.

@item status_giveio.bat
Check on the status of the giveio driver.

@item remove_giveio.bat
Stop and remove the giveio driver from memory.
@end enumerate

These 3 batch files calls the loaddrv program with various options to
install, start, stop, and remove the driver.

When you first execute install_giveio.bat, loaddrv.exe and giveio.sys
must be in the current directory. When install_giveio.bat is executed it
will copy giveio.sys from your current directory to your Windows
directory. It will then load the driver from the Windows directory. This
means that after the first time install_giveio is executed, you should 
be able to subsequently execute the batch file from any directory and have
it successfully start the driver.

Note that you must have administrator privilege to load the giveio driver.

@c
@c Node
@c
@node Windows 95/98,  , Windows NT/2K/XP, Using the parallel port
@subsubsection Windows 95/98

@noindent
On Windows 95 and 98 the giveio.sys driver is not needed.


@c
@c Node
@c
@node Documentation, Credits., Using the parallel port, Windows
@subsection Documentation

@noindent
AVRDUDE installs a manual page as well as info, HTML and PDF
documentation.  The manual page is installed in
@code{/usr/local/man/man1} area, while the HTML and PDF documentation
is installed in @code{/usr/local/share/doc/avrdude} directory.  The
info manual is installed in @code{/usr/local/info/avrdude.info}.

Note that these locations can be altered by various configure options
such as @option{--prefix} and @option{--datadir}.


@c
@c Node
@c
@node Credits.,  , Documentation, Windows
@subsection Credits.

@noindent
Thanks to:

@itemize @bullet
@item
Dale Roberts for the giveio driver.

@item
Paula Tomlinson for the loaddrv sources.

@item
Chris Liechti <cliechti@@gmx.net> for modifying loaddrv to be command
line driven and for writing the batch files.

@end itemize

@c
@c Node
@c
@node Troubleshooting, ,Platform Dependent Information ,Top
@appendix Troubleshooting

@noindent
In general, please report any bugs encountered via
@*
@url{http://savannah.nongnu.org/bugs/?group=avrdude}.


@itemize @bullet

@item
Problem: I'm using a serial programmer under Windows and get the following
error:

@code{avrdude: serial_open(): can't set attributes for device "com1"},

Solution: This problem seems to appear with certain versions of Cygwin. Specifying
@code{"/dev/com1"} instead of @code{"com1"} should help.


@item
Problem: I'm using Linux and my AVR910 programmer is really slow.

Solution (short): @code{setserial @var{port} low_latency}

Solution (long):
There are two problems here. First, the system may wait some time before it
passes data from the serial port to the program. Under Linux the following
command works around this (you may need root privileges for this).

@code{setserial @var{port} low_latency}

Secondly, the serial interface chip may delay the interrupt for some time.
This behaviour can be changed by setting the FIFO-threshold to one. Under Linux this
can only be done by changing the kernel source in @code{drivers/char/serial.c}.
Search the file for @code{UART_FCR_TRIGGER_8} and replace it with @code{UART_FCR_TRIGGER_1}. Note that overall performance might suffer if there
is high throughput on serial lines. Also note that you are modifying the kernel at
your own risk.


@item
Problem: I'm not using Linux and my AVR910 programmer is really slow.

Solutions: The reasons for this are the same as above.
If you know how to work around this on your OS, please let us know.

@item
Problem: Updating the flash ROM from terminal mode does not work with the
JTAG ICEs.

Solution: None at this time.  Currently, the JTAG ICE code cannot
write to the flash ROM one byte at a time.

@item
Problem: Page-mode programming the EEPROM (using the -U option) does
not erase EEPROM cells before writing, and thus cannot overwrite any
previous value != 0xff.

Solution: None.  This is an inherent feature of the way JTAG EEPROM
programming works, and is documented that way in the Atmel AVR
datasheets.
In order to successfully program the EEPROM that way, a prior chip
erase (with the EESAVE fuse unprogrammed) is required.
This also applies to the STK500 and STK600 in high-voltage programming mode.

@item
Problem: How do I turn off the @var{DWEN} fuse?

Solution: If the @var{DWEN} (debugWire enable) fuse is activated,
the @var{/RESET} pin is not functional anymore, so normal ISP
communication cannot be established.
There are two options to deactivate that fuse again: high-voltage
programming, or getting the JTAG ICE mkII talk debugWire, and
prepare the target AVR to accept normal ISP communication again.

The first option requires a programmer that is capable of high-voltage
programming (either serial or parallel, depending on the AVR device),
for example the STK500.  In high-voltage programming mode, the
@var{/RESET} pin is activated initially using a 12 V pulse (thus the
name @emph{high voltage}), so the target AVR can subsequently be
reprogrammed, and the @var{DWEN} fuse can be cleared.  Typically, this
operation cannot be performed while the AVR is located in the target
circuit though.

The second option requires a JTAG ICE mkII that can talk the debugWire
protocol.  The ICE needs to be connected to the target using the
JTAG-to-ISP adapter, so the JTAG ICE mkII can be used as a debugWire
initiator as well as an ISP programmer.  AVRDUDE will then be activated
using the @var{jtag2isp} programmer type.  The initial ISP
communication attempt will fail, but AVRDUDE then tries to initiate a
debugWire reset.  When successful, this will leave the target AVR in a
state where it can accept standard ISP communication.  The ICE is then
signed off (which will make it signing off from the USB as well), so
AVRDUDE has to be called again afterwards.  This time, standard ISP
communication can work, so the @var{DWEN} fuse can be cleared.

The pin mapping for the JTAG-to-ISP adapter is:

@multitable @columnfractions .2 .2
@item @strong{JTAG pin} @tab @strong{ISP pin}
@item 1 @tab 3
@item 2 @tab 6
@item 3 @tab 1
@item 4 @tab 2
@item 6 @tab 5
@item 9 @tab 4
@end multitable

@item
Problem: Multiple USBasp or USBtinyISP programmers connected simultaneously are not
found.

Solution: The USBtinyISP code supports distinguishing multiple
programmers based on their bus:device connection tuple that describes
their place in the USB hierarchy on a specific host.  This tuple can
be added to the @var{-P usb} option, similar to adding a serial number
on other USB-based programmers.

The actual naming convention for the bus and device names is
operating-system dependent; AVRDUDE will print out what it found
on the bus when running it with (at least) one @var{-v} option.
By specifying a string that cannot match any existing device
(for example, @var{-P usb:xxx}), the scan will list all possible
candidate devices found on the bus.

Examples:
@example
avrdude -c usbtiny -p atmega8 -P usb:003:025 (Linux)
avrdude -c usbtiny -p atmega8 -P usb:/dev/usb:/dev/ugen1.3 (FreeBSD 8+)
avrdude -c usbtiny -p atmega8 \
  -P usb:bus-0:\\.\libusb0-0001--0x1781-0x0c9f (Windows)
@end example

@item
Problem: I cannot do @dots{} when the target is in debugWire mode.

Solution: debugWire mode imposes several limitations.

The debugWire protocol is Atmel's proprietary one-wire (plus ground)
protocol to allow an in-circuit emulation of the smaller AVR devices,
using the @var{/RESET} line.
DebugWire mode is initiated by activating the @var{DWEN}
fuse, and then power-cycling the target.
While this mode is mainly intended for debugging/emulation, it
also offers limited programming capabilities.
Effectively, the only memory areas that can be read or programmed
in this mode are flash ROM and EEPROM.
It is also possible to read out the signature.
All other memory areas cannot be accessed.
There is no
@emph{chip erase}
functionality in debugWire mode; instead, while reprogramming the
flash ROM, each flash ROM page is erased right before updating it.
This is done transparently by the JTAG ICE mkII (or AVR Dragon).
The only way back from debugWire mode is to initiate a special
sequence of commands to the JTAG ICE mkII (or AVR Dragon), so the
debugWire mode will be temporarily disabled, and the target can
be accessed using normal ISP programming.
This sequence is automatically initiated by using the JTAG ICE mkII
or AVR Dragon in ISP mode, when they detect that ISP mode cannot be
entered.

@item
Problem: I want to use my JTAG ICE mkII to program an
Xmega device through PDI.  The documentation tells me to use the
@emph{XMEGA PDI adapter for JTAGICE mkII} that is supposed to ship
with the kit, yet I don't have it.

Solution: Use the following pin mapping:

@multitable @columnfractions .2 .2 .2 .2
@item @strong{JTAGICE} @tab @strong{Target} @tab @strong{Squid cab-} @tab @strong{PDI}
@item @strong{mkII probe} @tab @strong{pins} @tab @strong{le colors} @tab @strong{header}
@item 1 (TCK)   @tab         @tab Black  @tab
@item 2 (GND)   @tab GND     @tab White  @tab 6
@item 3 (TDO)   @tab         @tab Grey   @tab
@item 4 (VTref) @tab VTref   @tab Purple @tab 2
@item 5 (TMS)   @tab         @tab Blue   @tab
@item 6 (nSRST) @tab PDI_CLK @tab Green  @tab 5
@item 7 (N.C.)  @tab         @tab Yellow @tab
@item 8 (nTRST) @tab         @tab Orange @tab
@item 9 (TDI)   @tab PDI_DATA @tab Red   @tab 1
@item 10 (GND)  @tab         @tab Brown  @tab
@end multitable

@item
Problem: I want to use my AVR Dragon to program an
Xmega device through PDI.  

Solution: Use the 6 pin ISP header on the Dragon and the following pin mapping:

@multitable @columnfractions .2 .2
@item @strong{Dragon} @tab @strong{Target}
@item @strong{ISP Header} @tab @strong{pins}
@item 1 (MISO)  @tab PDI_DATA
@item 2 (VCC)   @tab VCC
@item 3 (SCK)   @tab 
@item 4 (MOSI)  @tab 
@item 5 (RESET) @tab PDI_CLK / RST
@item 6 (GND)   @tab GND
@end multitable

@item
Problem: I want to use my AVRISP mkII to program an
ATtiny4/5/9/10 device through TPI.  How to connect the pins?

Solution: Use the following pin mapping:

@multitable @columnfractions .2 .2 .2
@item @strong{AVRISP} @tab @strong{Target} @tab @strong{ATtiny}
@item @strong{connector} @tab @strong{pins} @tab @strong{pin #}
@item 1 (MISO)  @tab TPIDATA  @tab 1
@item 2 (VTref) @tab Vcc      @tab 5
@item 3 (SCK)   @tab TPICLK   @tab 3
@item 4 (MOSI)  @tab          @tab
@item 5 (RESET) @tab /RESET   @tab 6
@item 6 (GND)   @tab GND      @tab 2
@end multitable

@item
Problem: I want to program an ATtiny4/5/9/10 device using a serial/parallel 
bitbang programmer.  How to connect the pins?

Solution: Since TPI has only 1 pin for bi-directional data transfer, both 
@var{MISO} and @var{MOSI} pins should be connected to the @var{TPIDATA} pin 
on the ATtiny device.
However, a 1K resistor should be placed between the @var{MOSI} and @var{TPIDATA}.
The @var{MISO} pin connects to @var{TPIDATA} directly.
The @var{SCK} pin is connected to @var{TPICLK}.

In addition, the @var{Vcc}, @var{/RESET} and @var{GND} pins should 
be connected to their respective ports on the ATtiny device.

@item
Problem: How can I use a FTDI FT232R USB-to-Serial device for bitbang programming?

Solution: When connecting the FT232 directly to the pins of the target Atmel device, 
the polarity of the pins defined in the @code{programmer} definition should be 
inverted by prefixing a tilde. For example, the @var{dasa} programmer would 
look like this when connected via a FT232R device (notice the tildes in 
front of pins 7, 4, 3 and 8):

@example
programmer
  id    = "dasa_ftdi";
  desc  = "serial port banging, reset=rts sck=dtr mosi=txd miso=cts";
  type  = serbb;
  reset = ~7;
  sck   = ~4;
  mosi  = ~3;
  miso  = ~8;
;
@end example

Note that this uses the FT232 device as a normal serial port, not using the 
FTDI drivers in the special bitbang mode.

@item
Problem: My ATtiny4/5/9/10 reads out fine, but any attempt to program
it (through TPI) fails.  Instead, the memory retains the old contents.

Solution: Mind the limited programming supply voltage range of these
devices.

In-circuit programming through TPI is only guaranteed by the datasheet
at Vcc = 5 V.

@item
Problem: My ATxmega@dots{}A1/A2/A3 cannot be programmed through PDI with
my AVR Dragon.  Programming through a JTAG ICE mkII works though, as does
programming through JTAG.

Solution: None by this time (2010 Q1).

It is said that the AVR Dragon can only program devices from the A4
Xmega sub-family.

@item
Problem: when programming with an AVRISPmkII or STK600, AVRDUDE hangs
when programming files of a certain size (e.g. 246 bytes).  Other
(larger or smaller) sizes work though.

Solution: This is a bug caused by an incorrect handling of zero-length
packets (ZLPs) in some versions of the libusb 0.1 API wrapper that ships
with libusb 1.x in certain Linux distributions.  All Linux systems with
kernel versions < 2.6.31 and libusb >= 1.0.0 < 1.0.3 are reported to be
affected by this.

See also: @url{http://www.libusb.org/ticket/6}

@item
Problem: after flashing a firmware that reduces the target's clock
speed (e.g. through the @code{CLKPR} register), further ISP connection
attempts fail.

Solution: Even though ISP starts with pulling @var{/RESET} low, the
target continues to run at the internal clock speed as defined by the
firmware running before.  Therefore, the ISP clock speed must be
reduced appropriately (to less than 1/4 of the internal clock speed)
using the -B option before the ISP initialization sequence will
succeed.

As that slows down the entire subsequent ISP session, it might make
sense to just issue a @emph{chip erase} using the slow ISP clock
(option @code{-e}), and then start a new session at higher speed.
Option @code{-D} might be used there, to prevent another unneeded
erase cycle.

@end itemize



@bye

