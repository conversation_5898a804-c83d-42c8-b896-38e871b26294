2015-12-15  <PERSON><PERSON> <<EMAIL>>

	* avrdude.1 (-C): Do not suggest users might change the
	default config file.  It will be overwritten by updates.

2015-12-09  <PERSON><PERSON> <<EMAIL>>

	bug #46610: Floating point exception (core dumped) arch linux rpi2
	bug #46483: version 6.2. ser_open(): can't set attributes for device
	* ser_posix.c: Back out change from patch #8380

2015-11-16  <PERSON><PERSON> <<EMAIL>>

	* configure.ac: Bump for post-release 6.2.

2015-11-16  <PERSON><PERSON> <<EMAIL>>

	* configure.ac: Released version 6.2.

2015-10-31  <PERSON><PERSON> <<EMAIL>>

	Submitted by <PERSON><PERSON>:
	bug #45727: Wrong atmega8u2 flash parameters
	* avrdude.conf.in (ATmega8U2): correct page and block size

2015-10-31  <PERSON><PERSON> <<EMAIL>>

	Submitted by <PERSON><PERSON><PERSON><PERSON>:
	bug #46020: Add TIAO TUMPA to the conf file.
	* avrdude.conf.in (tumpa): New entry.

2015-10-31  Joerg Wunsch <<EMAIL>>

	Submitted by Pasquale Cocchini:
	bug #46021: Please add read in the memory lock section of ATtiny85
	* avrdude.conf.in (ATtiny25/45/85): add read pattern for lock bits

2015-10-31  Joerg Wunsch <<EMAIL>>

	* Makefile.am (libavrdude_a_SOURCES): reflect recent changes
	(pgm.h is gone, config.h is new).

2015-04-09  Joerg Wunsch <<EMAIL>>

	bug #44717: avrdude creates empty flash dump
	* update.c (do_op): When about to write an empty flash dump file,
	warn about this to avoid surprises.
	* avrdude.1: Document the truncation of trailing 0xFF bytes for
	flash memory areas.
	* doc/avrdude.texi: (Dito.)

2015-04-09  Joerg Wunsch <<EMAIL>>

	Annual ChangeLog rotation.
