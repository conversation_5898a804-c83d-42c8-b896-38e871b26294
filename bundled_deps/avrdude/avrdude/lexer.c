
#line 2 "lexer.c"

#define  YY_INT_ALIGNED short int

/* A lexical scanner generated by flex */

#define FLEX_SCANNER
#define YY_FLEX_MAJOR_VERSION 2
#define YY_FLEX_MINOR_VERSION 6
#define YY_FLEX_SUBMINOR_VERSION 4
#if YY_FLEX_SUBMINOR_VERSION > 0
#define FLEX_BETA
#endif

/* First, we deal with  platform-specific or compiler-specific issues. */

/* begin standard C headers. */
#include <stdio.h>
#include <string.h>
#include <errno.h>
#include <stdlib.h>

/* end standard C headers. */

/* flex integer type definitions */

#ifndef FLEXINT_H
#define FLEXINT_H

/* C99 systems have <inttypes.h>. Non-C99 systems may or may not. */

#if (defined (__STDC_VERSION__) && __STDC_VERSION__ >= 199901L) || defined(_MSC_VER)

/* C99 says to define __STDC_LIMIT_MACROS before including stdint.h,
 * if you want the limit (max/min) macros for int types. 
 */
#ifndef __STDC_LIMIT_MACROS
#define __STDC_LIMIT_MACROS 1
#endif

#include <inttypes.h>
typedef int8_t flex_int8_t;
typedef uint8_t flex_uint8_t;
typedef int16_t flex_int16_t;
typedef uint16_t flex_uint16_t;
typedef int32_t flex_int32_t;
typedef uint32_t flex_uint32_t;
#else
typedef signed char flex_int8_t;
typedef short int flex_int16_t;
typedef int flex_int32_t;
typedef unsigned char flex_uint8_t; 
typedef unsigned short int flex_uint16_t;
typedef unsigned int flex_uint32_t;

/* Limits of integral types. */
#ifndef INT8_MIN
#define INT8_MIN               (-128)
#endif
#ifndef INT16_MIN
#define INT16_MIN              (-32767-1)
#endif
#ifndef INT32_MIN
#define INT32_MIN              (-2147483647-1)
#endif
#ifndef INT8_MAX
#define INT8_MAX               (127)
#endif
#ifndef INT16_MAX
#define INT16_MAX              (32767)
#endif
#ifndef INT32_MAX
#define INT32_MAX              (2147483647)
#endif
#ifndef UINT8_MAX
#define UINT8_MAX              (255U)
#endif
#ifndef UINT16_MAX
#define UINT16_MAX             (65535U)
#endif
#ifndef UINT32_MAX
#define UINT32_MAX             (4294967295U)
#endif

#ifndef SIZE_MAX
#define SIZE_MAX               (~(size_t)0)
#endif

#endif /* ! C99 */

#endif /* ! FLEXINT_H */

/* begin standard C++ headers. */

/* TODO: this is always defined, so inline it */
#define yyconst const

#if defined(__GNUC__) && __GNUC__ >= 3
#define yynoreturn __attribute__((__noreturn__))
#else
#define yynoreturn
#endif

/* Returned upon end-of-file. */
#define YY_NULL 0

/* Promotes a possibly negative, possibly signed char to an
 *   integer in range [0..255] for use as an array index.
 */
#define YY_SC_TO_UI(c) ((YY_CHAR) (c))

/* Enter a start condition.  This macro really ought to take a parameter,
 * but we do it the disgusting crufty way forced on us by the ()-less
 * definition of BEGIN.
 */
#define BEGIN (yy_start) = 1 + 2 *
/* Translate the current start state into a value that can be later handed
 * to BEGIN to return to the state.  The YYSTATE alias is for lex
 * compatibility.
 */
#define YY_START (((yy_start) - 1) / 2)
#define YYSTATE YY_START
/* Action number for EOF rule of a given start state. */
#define YY_STATE_EOF(state) (YY_END_OF_BUFFER + state + 1)
/* Special action meaning "start processing a new file". */
#define YY_NEW_FILE yyrestart( yyin  )
#define YY_END_OF_BUFFER_CHAR 0

/* Size of default input buffer. */
#ifndef YY_BUF_SIZE
#ifdef __ia64__
/* On IA-64, the buffer size is 16k, not 8k.
 * Moreover, YY_BUF_SIZE is 2*YY_READ_BUF_SIZE in the general case.
 * Ditto for the __ia64__ case accordingly.
 */
#define YY_BUF_SIZE 32768
#else
#define YY_BUF_SIZE 16384
#endif /* __ia64__ */
#endif

/* The state buf must be large enough to hold one state per character in the main buffer.
 */
#define YY_STATE_BUF_SIZE   ((YY_BUF_SIZE + 2) * sizeof(yy_state_type))

#ifndef YY_TYPEDEF_YY_BUFFER_STATE
#define YY_TYPEDEF_YY_BUFFER_STATE
typedef struct yy_buffer_state *YY_BUFFER_STATE;
#endif

#ifndef YY_TYPEDEF_YY_SIZE_T
#define YY_TYPEDEF_YY_SIZE_T
typedef size_t yy_size_t;
#endif

extern int yyleng;

extern FILE *yyin, *yyout;

#define EOB_ACT_CONTINUE_SCAN 0
#define EOB_ACT_END_OF_FILE 1
#define EOB_ACT_LAST_MATCH 2
    
    #define YY_LESS_LINENO(n)
    #define YY_LINENO_REWIND_TO(ptr)
    
/* Return all but the first "n" matched characters back to the input stream. */
#define yyless(n) \
	do \
		{ \
		/* Undo effects of setting up yytext. */ \
        int yyless_macro_arg = (n); \
        YY_LESS_LINENO(yyless_macro_arg);\
		*yy_cp = (yy_hold_char); \
		YY_RESTORE_YY_MORE_OFFSET \
		(yy_c_buf_p) = yy_cp = yy_bp + yyless_macro_arg - YY_MORE_ADJ; \
		YY_DO_BEFORE_ACTION; /* set up yytext again */ \
		} \
	while ( 0 )
#define unput(c) yyunput( c, (yytext_ptr)  )

#ifndef YY_STRUCT_YY_BUFFER_STATE
#define YY_STRUCT_YY_BUFFER_STATE
struct yy_buffer_state
	{
	FILE *yy_input_file;

	char *yy_ch_buf;		/* input buffer */
	char *yy_buf_pos;		/* current position in input buffer */

	/* Size of input buffer in bytes, not including room for EOB
	 * characters.
	 */
	int yy_buf_size;

	/* Number of characters read into yy_ch_buf, not including EOB
	 * characters.
	 */
	int yy_n_chars;

	/* Whether we "own" the buffer - i.e., we know we created it,
	 * and can realloc() it to grow it, and should free() it to
	 * delete it.
	 */
	int yy_is_our_buffer;

	/* Whether this is an "interactive" input source; if so, and
	 * if we're using stdio for input, then we want to use getc()
	 * instead of fread(), to make sure we stop fetching input after
	 * each newline.
	 */
	int yy_is_interactive;

	/* Whether we're considered to be at the beginning of a line.
	 * If so, '^' rules will be active on the next match, otherwise
	 * not.
	 */
	int yy_at_bol;

    int yy_bs_lineno; /**< The line count. */
    int yy_bs_column; /**< The column count. */

	/* Whether to try to fill the input buffer when we reach the
	 * end of it.
	 */
	int yy_fill_buffer;

	int yy_buffer_status;

#define YY_BUFFER_NEW 0
#define YY_BUFFER_NORMAL 1
	/* When an EOF's been seen but there's still some text to process
	 * then we mark the buffer as YY_EOF_PENDING, to indicate that we
	 * shouldn't try reading from the input source any more.  We might
	 * still have a bunch of tokens to match, though, because of
	 * possible backing-up.
	 *
	 * When we actually see the EOF, we change the status to "new"
	 * (via yyrestart()), so that the user can continue scanning by
	 * just pointing yyin at a new input file.
	 */
#define YY_BUFFER_EOF_PENDING 2

	};
#endif /* !YY_STRUCT_YY_BUFFER_STATE */

/* Stack of input buffers. */
static size_t yy_buffer_stack_top = 0; /**< index of top of stack. */
static size_t yy_buffer_stack_max = 0; /**< capacity of stack. */
static YY_BUFFER_STATE * yy_buffer_stack = NULL; /**< Stack as an array. */

/* We provide macros for accessing buffer states in case in the
 * future we want to put the buffer states in a more general
 * "scanner state".
 *
 * Returns the top of the stack, or NULL.
 */
#define YY_CURRENT_BUFFER ( (yy_buffer_stack) \
                          ? (yy_buffer_stack)[(yy_buffer_stack_top)] \
                          : NULL)
/* Same as previous macro, but useful when we know that the buffer stack is not
 * NULL or when we need an lvalue. For internal use only.
 */
#define YY_CURRENT_BUFFER_LVALUE (yy_buffer_stack)[(yy_buffer_stack_top)]

/* yy_hold_char holds the character lost when yytext is formed. */
static char yy_hold_char;
static int yy_n_chars;		/* number of characters read into yy_ch_buf */
int yyleng;

/* Points to current character in buffer. */
static char *yy_c_buf_p = NULL;
static int yy_init = 0;		/* whether we need to initialize */
static int yy_start = 0;	/* start state number */

/* Flag which is used to allow yywrap()'s to do buffer switches
 * instead of setting up a fresh yyin.  A bit of a hack ...
 */
static int yy_did_buffer_switch_on_eof;

void yyrestart ( FILE *input_file  );
void yy_switch_to_buffer ( YY_BUFFER_STATE new_buffer  );
YY_BUFFER_STATE yy_create_buffer ( FILE *file, int size  );
void yy_delete_buffer ( YY_BUFFER_STATE b  );
void yy_flush_buffer ( YY_BUFFER_STATE b  );
void yypush_buffer_state ( YY_BUFFER_STATE new_buffer  );
void yypop_buffer_state ( void );

static void yyensure_buffer_stack ( void );
static void yy_load_buffer_state ( void );
static void yy_init_buffer ( YY_BUFFER_STATE b, FILE *file  );
#define YY_FLUSH_BUFFER yy_flush_buffer( YY_CURRENT_BUFFER )

YY_BUFFER_STATE yy_scan_buffer ( char *base, yy_size_t size  );
YY_BUFFER_STATE yy_scan_string ( const char *yy_str  );
YY_BUFFER_STATE yy_scan_bytes ( const char *bytes, int len  );

void *yyalloc ( yy_size_t  );
void *yyrealloc ( void *, yy_size_t  );
void yyfree ( void *  );

#define yy_new_buffer yy_create_buffer
#define yy_set_interactive(is_interactive) \
	{ \
	if ( ! YY_CURRENT_BUFFER ){ \
        yyensure_buffer_stack (); \
		YY_CURRENT_BUFFER_LVALUE =    \
            yy_create_buffer( yyin, YY_BUF_SIZE ); \
	} \
	YY_CURRENT_BUFFER_LVALUE->yy_is_interactive = is_interactive; \
	}
#define yy_set_bol(at_bol) \
	{ \
	if ( ! YY_CURRENT_BUFFER ){\
        yyensure_buffer_stack (); \
		YY_CURRENT_BUFFER_LVALUE =    \
            yy_create_buffer( yyin, YY_BUF_SIZE ); \
	} \
	YY_CURRENT_BUFFER_LVALUE->yy_at_bol = at_bol; \
	}
#define YY_AT_BOL() (YY_CURRENT_BUFFER_LVALUE->yy_at_bol)

/* Begin user sect3 */
typedef flex_uint8_t YY_CHAR;

FILE *yyin = NULL, *yyout = NULL;

typedef int yy_state_type;

extern int yylineno;
int yylineno = 1;

extern char *yytext;
#ifdef yytext_ptr
#undef yytext_ptr
#endif
#define yytext_ptr yytext

static yy_state_type yy_get_previous_state ( void );
static yy_state_type yy_try_NUL_trans ( yy_state_type current_state  );
static int yy_get_next_buffer ( void );
static void yynoreturn yy_fatal_error ( const char* msg  );

/* Done after the current pattern has been matched and before the
 * corresponding action - sets up yytext.
 */
#define YY_DO_BEFORE_ACTION \
	(yytext_ptr) = yy_bp; \
	yyleng = (int) (yy_cp - yy_bp); \
	(yy_hold_char) = *yy_cp; \
	*yy_cp = '\0'; \
	(yy_c_buf_p) = yy_cp;
#define YY_NUM_RULES 156
#define YY_END_OF_BUFFER 157
/* This struct is not used in this scanner,
   but its presence is necessary. */
struct yy_trans_info
	{
	flex_int32_t yy_verify;
	flex_int32_t yy_nxt;
	};
static const flex_int16_t yy_accept[877] =
    {   0,
        0,    0,    0,    0,    0,    0,    0,    0,  157,  155,
      153,  152,    7,    9,  150,  151,  146,  155,  155,    4,
        4,  148,  147,  155,  155,  155,  155,  155,  155,  155,
      155,  155,  155,  155,  155,  155,  155,  155,  155,  155,
      155,  155,  155,  149,   20,   21,   13,  156,  156,   10,
       11,  153,    0,    3,    1,    6,   12,    5,    4,    0,
        0,    0,    0,    0,    0,    0,    0,  154,    0,    0,
        0,    0,    0,    0,    0,    0,    0,    0,   62,   64,
        0,    0,    0,    0,    0,    0,    0,    0,   78,    0,
        0,    0,    0,    0,    0,    0,    0,    0,    0,    0,

        0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
        0,    0,    0,    0,    0,    0,    0,   20,   19,   17,
       18,   14,   16,   15,    3,    2,    5,    8,    0,    0,
        0,    0,    0,   28,    0,    0,    0,    0,    0,    0,
        0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
        0,    0,    0,   63,    0,    0,    0,    0,    0,    0,
        0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
        0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
        0,    0,    0,    0,  120,    0,    0,    0,    0,    0,
        0,    0,    0,    0,    0,  132,  139,    0,    0,  145,

        2,    0,    0,    0,    0,    0,   29,    0,    0,    0,
        0,    0,    0,    0,   45,    0,   47,    0,    0,    0,
        0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
        0,    0,   75,   76,   77,    0,    0,    0,    0,    0,
        0,    0,   89,    0,    0,    0,    0,    0,    0,    0,
        0,    0,    0,    0,    0,  109,    0,    0,    0,    0,
      123,    0,    0,    0,    0,    0,    0,  131,    0,    0,
        0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
        0,    0,    0,    0,    0,    0,    0,   44,    0,    0,
        0,    0,   52,    0,    0,    0,    0,    0,    0,    0,

        0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
        0,    0,    0,    0,    0,    0,   85,   86,    0,    0,
        0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
        0,    0,    0,  107,    0,    0,    0,    0,  115,    0,
        0,    0,  124,    0,    0,    0,    0,    0,    0,    0,
        0,  136,    0,    0,    0,  141,    0,    0,    0,   25,
        0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
       48,    0,   51,    0,    0,    0,    0,    0,    0,    0,
        0,    0,    0,    0,    0,    0,    0,    0,    0,   73,
        0,    0,    0,    0,   82,   83,    0,    0,   88,    0,

       91,    0,    0,    0,    0,    0,    0,    0,    0,    0,
      105,    0,  108,    0,    0,    0,    0,    0,    0,  121,
        0,    0,    0,    0,    0,    0,    0,  133,  134,    0,
        0,  138,  140,    0,    0,    0,    0,    0,    0,    0,
        0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
        0,    0,    0,   56,   57,    0,    0,    0,    0,    0,
        0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
        0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
        0,    0,    0,  110,  111,    0,    0,    0,    0,    0,
        0,    0,    0,    0,  129,    0,    0,    0,    0,    0,

        0,    0,    0,    0,   26,    0,    0,    0,    0,    0,
        0,    0,    0,    0,    0,    0,    0,    0,   55,    0,
        0,    0,    0,    0,   66,    0,    0,    0,    0,   72,
        0,    0,    0,   81,    0,   87,    0,    0,    0,    0,
        0,    0,    0,   98,    0,    0,    0,    0,    0,    0,
      114,    0,    0,    0,    0,    0,    0,    0,    0,    0,
        0,  142,  143,    0,    0,    0,   24,   27,   30,    0,
        0,    0,    0,   38,    0,    0,    0,    0,    0,    0,
        0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
        0,    0,   79,   80,   84,    0,   92,    0,   94,   95,

        0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
      122,  125,    0,    0,    0,  130,    0,  137,  144,    0,
        0,   31,    0,    0,    0,    0,    0,    0,    0,    0,
        0,   46,    0,    0,    0,    0,    0,    0,    0,    0,
        0,    0,    0,    0,    0,    0,    0,   90,   93,    0,
        0,    0,    0,    0,  104,    0,    0,  116,    0,    0,
        0,  128,  135,    0,    0,    0,    0,    0,    0,   36,
        0,    0,    0,    0,    0,    0,    0,    0,   53,    0,
        0,    0,    0,    0,    0,   67,    0,   69,   70,    0,
        0,    0,    0,    0,    0,    0,    0,  112,  113,    0,

        0,  119,    0,  127,    0,    0,    0,    0,    0,    0,
        0,    0,    0,    0,    0,    0,   49,    0,    0,    0,
        0,    0,    0,   65,    0,    0,    0,    0,    0,    0,
        0,    0,    0,  117,  118,    0,    0,    0,    0,    0,
        0,   35,    0,    0,    0,    0,    0,    0,    0,   54,
        0,    0,    0,    0,   68,    0,    0,   96,    0,   99,
        0,    0,    0,    0,    0,    0,    0,   23,    0,    0,
        0,    0,    0,    0,    0,    0,   43,    0,    0,    0,
        0,    0,    0,    0,    0,    0,    0,    0,    0,    0,
      126,    0,    0,    0,    0,   37,    0,    0,    0,    0,

        0,    0,    0,    0,   61,   71,   74,   97,    0,    0,
        0,    0,    0,    0,   32,    0,    0,   39,   40,    0,
       42,    0,   58,   59,    0,    0,    0,    0,    0,    0,
        0,    0,    0,    0,    0,   60,    0,    0,    0,    0,
        0,    0,    0,    0,   41,    0,    0,    0,    0,    0,
      106,    0,    0,   34,    0,    0,    0,    0,    0,    0,
       33,    0,    0,    0,    0,    0,    0,   50,    0,  101,
        0,  103,   22,  100,  102,    0
    } ;

static const YY_CHAR yy_ec[256] =
    {   0,
        1,    1,    1,    1,    1,    1,    1,    1,    2,    3,
        1,    1,    2,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    2,    1,    4,    5,    1,    1,    1,    1,    6,
        7,    8,    9,   10,    9,   11,   12,   13,   14,   15,
       16,   17,   18,   17,   17,   17,   19,   20,   21,    1,
       22,    1,    1,    1,   23,   23,   23,   23,   23,   23,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,   24,    1,    1,   25,    1,   26,   27,   28,   29,

       30,   31,   32,   33,   34,   35,   36,   37,   38,   39,
       40,   41,    1,   42,   43,   44,   45,   46,   47,   48,
       49,   50,    1,    1,    1,   51,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,

        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1
    } ;

static const YY_CHAR yy_meta[52] =
    {   0,
        1,    1,    2,    2,    1,    1,    1,    1,    3,    1,
        4,    1,    5,    5,    5,    5,    5,    5,    5,    1,
        1,    1,    6,    2,    1,    6,    6,    6,    6,    6,
        6,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    7,    1,    1,
        1
    } ;

static const flex_int16_t yy_base[885] =
    {   0,
        0,    0,   49,   51,    0,    0,  976,  975,  977,  980,
      974,  980,  980,   47,  980,  980,  980,   46,  967,   46,
       55,  980,  980,   30,   42,   50,  944,   39,  936,   46,
       53,   58,   71,   62,   43,   74,   83,   90,   46,  929,
       91,  929,  940,  980,    0,  980,  980,   96,  980,  980,
      980,  967,  117,  128,  121,  135,  980,  142,    0,    0,
      931,  925,   65,  926,  950,  933,  919,  980,  928,  932,
      921,  133,  137,  933,  916,  931,  913,  137,  913,  980,
      929,  909,  926,  903,  905,  911,   86,  134,  980,  910,
      909,  917,  914,  139,  906,  145,  918,  143,  912,  899,

      902,  890,  142,  902,  895,  134,  898,  161,  896,  896,
      901,  891,  904,  902,  880,  894,  884,    0,  980,  980,
      980,  980,  980,  980,  185,  192,  199,    0,  886,  906,
      888,  894,  894,  980,  890,  890,  878,  888,  878,  882,
      889,  888,  885,  878,  869,  868,  882,  871,  864,  881,
      866,  874,  862,  980,  876,  873,  871,  874,  873,  857,
      871,  855,  864,  859,  867,  866,  848,  846,  858,  193,
       52,  850,  842,  855,  856,  854,  850,  836,  840,  838,
      841,  848,  846,  833,  980,  840,  834,  842,  843,  843,
      851,  840,  837,  834,  835,  192,  980,  827,  819,  980,

      211,  815,  847,  164,  818,  823,  980,  829,  165,  809,
      826,  827,  809,  804,  980,  824,  980,  811,  813,  819,
      815,  205,  803,  820,  108,  147,  812,  195,  797,  816,
      800,  794,  980,  980,  980,  204,  813,  809,  808,  214,
      800,  797,  980,  805,  804,  210,  804,  790,  791,  800,
      212,  800,  797,  777,  796,  228,  781,  775,  797,  796,
      980,  779,  791,  806,  785,  777,  779,  980,  785,  218,
      775,  227,  783,  782,  780,  797,  766,  779,  781,  763,
      775,  774,  761,  772,  773,  774,  762,  980,  768,  759,
      766,  766,  769,  763,  748,  762,  749,  759,  742,  759,

      748,  766,  742,  755,  752,  755,  738,  753,  729,  735,
      750,  749,  748,  727,  728,  728,  980,  980,  733,  725,
      729,  738,  727,  735,  738,  733,  722,  722,  723,  719,
      732,  717,  725,  980,  726,  225,  728,  719,  723,  726,
      713,  705,  980,  718,  734,  204,  701,  715,  698,  714,
      702,  980,  702,  711,  710,  222,  693,  712,  702,  980,
      691,  700,  696,  690,  705,  701,  685,  684,  683,  698,
      700,  683,  980,  689,  695,  695,  686,  685,  676,  687,
      676,  686,  701,  697,  663,  663,  678,  675,  665,  980,
      673,  667,  673,  661,  980,  980,  669,  672,  980,  675,

      980,  671,  655,  661,  660,  665,  651,  668,  664,  654,
      980,  666,  980,  656,  649,  660,  637,  656,  644,  980,
      639,  646,  657,  632,  640,  635,  632,  980,  980,  648,
      647,  980,  980,  231,  649,  637,  644,  622,  641,  620,
      643,  642,  624,  636,  631,  634,  638,  622,  627,  634,
      620,  613,  625,  980,  980,  613,  612,  615,  623,  609,
      636,  622,  605,  618,  603,  616,  601,  608,  613,  612,
      591,  603,  612,  608,  604,  591,  609,  603,  591,  583,
      601,  228,  604,  980,  980,  593,  598,  590,  581,  583,
      598,  594,  594,  581,  980,  576,  574,  578,  583,  576,

      583,  577,  583,  582,  980,  581,  561,  566,  578,  570,
      566,  576,  240,  575,  564,  570,  558,  568,  980,  555,
      554,  553,  548,  581,  980,  557,  568,  567,  561,  980,
      560,  546,  545,  980,  557,  980,  549,  537,  544,  553,
      533,  552,  540,  980,  550,  533,  537,  546,  544,  549,
      980,  547,  535,  541,  521,  539,  531,  526,  534,  537,
      522,  980,  980,  533,  521,  515,  980,  980,  980,  530,
      128,  533,  519,  980,  523,  243,  244,  526,  512,  524,
      509,  505,  525,  524,  507,  518,  532,  516,  519,  239,
      519,  518,  980,  980,  980,  512,  980,  512,  980,  980,

      510,  502,  508,  494,  508,  493,  490,  492,  483,  488,
      980,  980,  484,  499,  485,  980,  483,  980,  980,  500,
      497,  499,  233,  489,  473,  496,  476,  477,  478,  486,
      474,  980,  471,  473,  471,  478,  484,  483,  469,  479,
      494,  463,  476,  470,  463,  473,  472,  980,  980,  463,
      456,  461,  467,  460,  980,  465,  265,  237,  464,  465,
      449,  980,  980,  459,  450,  460,  451,  450,  448,  980,
      441,  456,  457,  436,  435,  103,  133,  154,  980,  180,
      213,  248,  247,  256,  274,  980,  259,  980,  980,  259,
      260,  265,  248,  267,  253,  254,  254,  980,  980,  254,

      255,  980,  259,  980,  270,  272,  272,  266,  261,  275,
      257,  270,  271,  267,  272,  285,  980,  272,  283,  284,
      285,  273,  280,  980,  276,  282,  283,  272,  296,  274,
      284,  285,  301,  980,  980,  298,  301,  301,  295,  289,
      304,  980,  294,  296,  300,  312,  299,  303,  309,  980,
      305,  306,  300,  319,  980,  320,  321,  980,  320,  980,
      312,  313,  314,  315,  306,  324,  321,  980,  330,  323,
      311,  329,  332,  331,  324,  334,  980,  322,  339,  340,
      341,  319,  320,  321,  335,  335,  330,  337,  332,  334,
      980,  333,  329,  341,  346,  980,  345,  345,  345,  354,

      359,  337,  338,  360,  980,  980,  980,  980,  345,  360,
      347,  362,  359,  351,  980,  365,  367,  980,  980,  367,
      980,  360,  980,  980,  363,  366,  354,  368,  356,  360,
      361,  366,  363,  366,  371,  980,  372,  377,  374,  379,
      384,  373,  371,  384,  980,  384,  389,  391,  391,  393,
      980,  393,  380,  980,  386,  386,  383,  388,  385,  404,
      980,  399,  387,  400,  389,  402,  398,  980,  393,  980,
      394,  980,  980,  980,  980,  980,  438,  445,  452,  457,
      459,  466,  473,  476
    } ;

static const flex_int16_t yy_def[885] =
    {   0,
      876,    1,  877,  877,  878,  878,  879,  879,  876,  876,
      876,  876,  876,  880,  876,  876,  876,  876,  876,  881,
      881,  876,  876,  876,  876,  876,  876,  876,  876,  876,
      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,
      876,  876,  876,  876,  882,  876,  876,  883,  876,  876,
      876,  876,  880,  876,  880,  876,  876,  876,   21,  884,
      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,
      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,
      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,
      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,

      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,
      876,  876,  876,  876,  876,  876,  876,  882,  876,  876,
      876,  876,  876,  876,  876,  876,  876,  884,  876,  876,
      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,
      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,
      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,
      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,
      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,
      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,
      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,

      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,
      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,
      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,
      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,
      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,
      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,
      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,
      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,
      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,
      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,

      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,
      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,
      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,
      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,
      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,
      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,
      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,
      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,
      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,
      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,

      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,
      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,
      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,
      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,
      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,
      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,
      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,
      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,
      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,
      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,

      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,
      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,
      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,
      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,
      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,
      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,
      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,
      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,
      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,
      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,

      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,
      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,
      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,
      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,
      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,
      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,
      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,
      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,
      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,
      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,

      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,
      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,
      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,
      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,
      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,
      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,
      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,
      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,
      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,
      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,

      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,
      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,
      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,
      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,
      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,
      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,
      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,
      876,  876,  876,  876,  876,    0,  876,  876,  876,  876,
      876,  876,  876,  876
    } ;

static const flex_int16_t yy_nxt[1032] =
    {   0,
       10,   11,   12,   13,   14,   15,   16,   10,   10,   17,
       18,   19,   20,   21,   21,   21,   21,   21,   21,   10,
       22,   23,   10,   10,   10,   24,   25,   26,   27,   28,
       29,   10,   30,   31,   10,   10,   32,   33,   34,   35,
       36,   37,   38,   39,   40,   41,   42,   10,   43,   10,
       44,   46,   47,   46,   47,   53,   58,   54,   56,   56,
       56,   56,   56,   56,   56,   58,   61,   63,   73,   68,
       92,   77,   48,   93,   48,   62,  244,   74,   64,  110,
       75,   79,   69,   82,   65,  111,   66,   70,  245,   71,
       67,   78,   80,   60,  112,   81,   84,   83,   85,   94,

       86,   89,  876,  131,   87,   95,   90,   91,  101,  132,
       88,  102,  103,   96,   97,   98,   99,  104,  114,  105,
      100,  115,  120,  106,  161,   53,  121,   54,  162,  876,
      107,  126,  300,  108,  122,  301,  716,  123,  109,  124,
      125,  125,  125,  125,  125,  125,  125,   56,   56,   56,
       56,   56,   56,   56,  127,  127,  127,  127,  127,  127,
      127,  140,  163,  141,  145,  187,  151,  182,  623,  142,
      169,  624,  176,  152,  717,  143,  164,  146,  144,  153,
      170,  172,  177,  188,  183,  184,  190,  173,  277,  282,
      302,  174,  303,  278,  283,  718,  191,  125,  125,  125,

      125,  125,  125,  125,  201,  201,  201,  201,  201,  201,
      201,  127,  127,  127,  127,  127,  127,  127,  241,  305,
      269,  719,  242,  201,  201,  201,  201,  201,  201,  201,
      311,  424,  270,  294,  271,  306,  243,  272,  316,  295,
      425,  720,  317,  323,  312,  296,  434,  324,  297,  330,
      318,  350,  336,  331,  337,  325,  353,  414,  546,  351,
      354,  415,  435,  499,  547,  548,  575,  500,  628,  630,
      338,  644,  667,  631,  700,  645,  721,  668,  698,  699,
      576,  701,  577,  722,  629,  723,  724,  725,  726,  727,
      728,  729,  730,  731,  732,  733,  734,  735,  736,  737,

      738,  739,  740,  741,  742,  743,  744,  745,  746,  747,
      748,  749,  750,  751,  752,  753,  754,  755,  756,  757,
      758,  759,  760,  761,  763,  765,  766,  767,  762,  764,
      768,  769,  770,  771,  772,  773,  774,  775,  776,  777,
      778,  779,  780,  781,  782,  783,  784,  785,  786,  787,
      788,  789,  790,  791,  792,  793,  794,  795,  796,  797,
      798,  799,  800,  801,  802,  803,  804,  805,  806,  807,
      808,  809,  810,  811,  812,  813,  814,  815,  816,  817,
      818,  819,  820,  821,  822,  823,  824,  825,  826,  827,
      828,  829,  830,  831,  832,  833,  834,  835,  836,  837,

      838,  839,  840,  841,  842,  843,  844,  845,  846,  847,
      848,  849,  850,  851,  852,  853,  854,  855,  856,  857,
      858,  859,  860,  861,  862,  863,  864,  865,  866,  867,
      868,  869,  870,  871,  872,  873,  874,  875,   45,   45,
       45,   45,   45,   45,   45,   49,   49,   49,   49,   49,
       49,   49,   50,   50,   50,   50,   50,   50,   50,   55,
       55,   55,   59,   59,  715,   59,  118,  714,  118,  118,
      118,  118,  118,  119,  119,  119,  119,  119,  119,  119,
      128,  128,  713,  712,  711,  710,  709,  708,  707,  706,
      705,  704,  703,  702,  697,  696,  695,  694,  693,  692,

      691,  690,  689,  688,  687,  686,  685,  684,  683,  682,
      681,  680,  679,  678,  677,  676,  675,  674,  673,  672,
      671,  670,  669,  666,  665,  664,  663,  662,  661,  660,
      659,  658,  657,  656,  655,  654,  653,  652,  651,  650,
      649,  648,  647,  646,  643,  642,  641,  640,  639,  638,
      637,  636,  635,  634,  633,  632,  627,  626,  625,  622,
      621,  620,  619,  618,  617,  616,  615,  614,  613,  612,
      611,  610,  609,  608,  607,  606,  605,  604,  603,  602,
      601,  600,  599,  598,  597,  596,  595,  594,  593,  592,
      591,  590,  589,  588,  587,  586,  585,  584,  583,  582,

      581,  580,  579,  578,  574,  573,  572,  571,  570,  569,
      568,  567,  566,  565,  564,  563,  562,  561,  560,  559,
      558,  557,  556,  555,  554,  553,  552,  551,  550,  549,
      545,  544,  543,  542,  541,  540,  539,  538,  537,  536,
      535,  534,  533,  532,  531,  530,  529,  528,  527,  526,
      525,  524,  523,  522,  521,  520,  519,  518,  517,  516,
      515,  514,  513,  512,  511,  510,  509,  508,  507,  506,
      505,  504,  503,  502,  501,  498,  497,  496,  495,  494,
      493,  492,  491,  490,  489,  488,  487,  486,  485,  484,
      483,  482,  481,  480,  479,  478,  477,  476,  475,  474,

      473,  472,  471,  470,  469,  468,  467,  466,  465,  464,
      463,  462,  461,  460,  459,  458,  457,  456,  455,  454,
      453,  452,  451,  450,  449,  448,  447,  446,  445,  444,
      443,  442,  441,  440,  439,  438,  437,  436,  433,  432,
      431,  430,  429,  428,  427,  426,  423,  422,  421,  420,
      419,  418,  417,  416,  413,  412,  411,  410,  409,  408,
      407,  406,  405,  404,  403,  402,  401,  400,  399,  398,
      397,  396,  395,  394,  393,  392,  391,  390,  389,  388,
      387,  386,  385,  384,  383,  382,  381,  380,  379,  378,
      377,  376,  375,  374,  373,  372,  371,  370,  369,  368,

      367,  366,  365,  364,  363,  362,  361,  360,  359,  358,
      357,  356,  355,  352,  349,  348,  347,  346,  345,  344,
      343,  342,  341,  340,  339,  335,  334,  333,  332,  329,
      328,  327,  326,  322,  321,  320,  319,  315,  314,  313,
      310,  309,  308,  307,  304,  299,  298,  293,  292,  291,
      290,  289,  288,  287,  286,  285,  284,  281,  280,  279,
      276,  275,  274,  273,  268,  267,  266,  265,  264,  263,
      262,  261,  260,  259,  258,  257,  256,  255,  254,  253,
      252,  251,  250,  249,  248,  247,  246,  240,  239,  238,
      237,  236,  235,  234,  233,  232,  231,  230,  229,  228,

      227,  226,  225,  224,  223,  222,  221,  220,  219,  218,
      217,  216,  215,  214,  213,  212,  211,  210,  209,  208,
      207,  206,  205,  204,  203,  202,  200,  199,  198,  197,
      196,  195,  194,  193,  192,  189,  186,  185,  181,  180,
      179,  178,  175,  171,  168,  167,  166,  165,  160,  159,
      158,  157,  156,  155,  154,  150,  149,  148,  147,  139,
      138,  137,  136,  135,  134,  133,  130,  129,   52,  117,
      116,  113,   76,   72,   57,   52,  876,   51,   51,    9,
      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,
      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,

      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,
      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,
      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,
      876
    } ;

static const flex_int16_t yy_chk[1032] =
    {   0,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    1,    1,    1,    1,    1,    1,    1,    1,    1,
        1,    3,    3,    4,    4,   14,   20,   14,   18,   18,
       18,   18,   18,   18,   18,   21,   24,   25,   28,   26,
       35,   30,    3,   35,    4,   24,  171,   28,   25,   39,
       28,   31,   26,   32,   25,   39,   25,   26,  171,   26,
       25,   30,   31,   20,   39,   31,   33,   32,   33,   36,

       33,   34,   21,   63,   33,   36,   34,   34,   37,   63,
       33,   37,   37,   36,   36,   36,   36,   38,   41,   38,
       36,   41,   48,   38,   87,   53,   48,   53,   87,   55,
       38,   55,  225,   38,   48,  225,  676,   48,   38,   48,
       54,   54,   54,   54,   54,   54,   54,   56,   56,   56,
       56,   56,   56,   56,   58,   58,   58,   58,   58,   58,
       58,   72,   88,   72,   73,  106,   78,  103,  571,   72,
       94,  571,   98,   78,  677,   72,   88,   73,   72,   78,
       94,   96,   98,  106,  103,  103,  108,   96,  204,  209,
      226,   96,  226,  204,  209,  678,  108,  125,  125,  125,

      125,  125,  125,  125,  126,  126,  126,  126,  126,  126,
      126,  127,  127,  127,  127,  127,  127,  127,  170,  228,
      196,  680,  170,  201,  201,  201,  201,  201,  201,  201,
      236,  346,  196,  222,  196,  228,  170,  196,  240,  222,
      346,  681,  240,  246,  236,  222,  356,  246,  222,  251,
      240,  270,  256,  251,  256,  246,  272,  336,  482,  270,
      272,  336,  356,  434,  482,  482,  513,  434,  576,  577,
      256,  590,  623,  577,  658,  590,  682,  623,  657,  657,
      513,  658,  513,  683,  576,  684,  685,  687,  690,  691,
      692,  693,  694,  695,  696,  697,  700,  701,  703,  705,

      706,  707,  708,  709,  710,  711,  712,  713,  714,  715,
      716,  718,  719,  720,  721,  722,  723,  725,  726,  727,
      728,  729,  730,  731,  732,  733,  736,  737,  731,  732,
      738,  739,  740,  741,  743,  744,  745,  746,  747,  748,
      749,  751,  752,  753,  754,  756,  757,  759,  761,  762,
      763,  764,  765,  766,  767,  769,  770,  771,  772,  773,
      774,  775,  776,  778,  779,  780,  781,  782,  783,  784,
      785,  786,  787,  788,  789,  790,  792,  793,  794,  795,
      797,  798,  799,  800,  801,  802,  803,  804,  809,  810,
      811,  812,  813,  814,  816,  817,  820,  822,  825,  826,

      827,  828,  829,  830,  831,  832,  833,  834,  835,  837,
      838,  839,  840,  841,  842,  843,  844,  846,  847,  848,
      849,  850,  852,  853,  855,  856,  857,  858,  859,  860,
      862,  863,  864,  865,  866,  867,  869,  871,  877,  877,
      877,  877,  877,  877,  877,  878,  878,  878,  878,  878,
      878,  878,  879,  879,  879,  879,  879,  879,  879,  880,
      880,  880,  881,  881,  675,  881,  882,  674,  882,  882,
      882,  882,  882,  883,  883,  883,  883,  883,  883,  883,
      884,  884,  673,  672,  671,  669,  668,  667,  666,  665,
      664,  661,  660,  659,  656,  654,  653,  652,  651,  650,

      647,  646,  645,  644,  643,  642,  641,  640,  639,  638,
      637,  636,  635,  634,  633,  631,  630,  629,  628,  627,
      626,  625,  624,  622,  621,  620,  617,  615,  614,  613,
      610,  609,  608,  607,  606,  605,  604,  603,  602,  601,
      598,  596,  592,  591,  589,  588,  587,  586,  585,  584,
      583,  582,  581,  580,  579,  578,  575,  573,  572,  570,
      566,  565,  564,  561,  560,  559,  558,  557,  556,  555,
      554,  553,  552,  550,  549,  548,  547,  546,  545,  543,
      542,  541,  540,  539,  538,  537,  535,  533,  532,  531,
      529,  528,  527,  526,  524,  523,  522,  521,  520,  518,

      517,  516,  515,  514,  512,  511,  510,  509,  508,  507,
      506,  504,  503,  502,  501,  500,  499,  498,  497,  496,
      494,  493,  492,  491,  490,  489,  488,  487,  486,  483,
      481,  480,  479,  478,  477,  476,  475,  474,  473,  472,
      471,  470,  469,  468,  467,  466,  465,  464,  463,  462,
      461,  460,  459,  458,  457,  456,  453,  452,  451,  450,
      449,  448,  447,  446,  445,  444,  443,  442,  441,  440,
      439,  438,  437,  436,  435,  431,  430,  427,  426,  425,
      424,  423,  422,  421,  419,  418,  417,  416,  415,  414,
      412,  410,  409,  408,  407,  406,  405,  404,  403,  402,

      400,  398,  397,  394,  393,  392,  391,  389,  388,  387,
      386,  385,  384,  383,  382,  381,  380,  379,  378,  377,
      376,  375,  374,  372,  371,  370,  369,  368,  367,  366,
      365,  364,  363,  362,  361,  359,  358,  357,  355,  354,
      353,  351,  350,  349,  348,  347,  345,  344,  342,  341,
      340,  339,  338,  337,  335,  333,  332,  331,  330,  329,
      328,  327,  326,  325,  324,  323,  322,  321,  320,  319,
      316,  315,  314,  313,  312,  311,  310,  309,  308,  307,
      306,  305,  304,  303,  302,  301,  300,  299,  298,  297,
      296,  295,  294,  293,  292,  291,  290,  289,  287,  286,

      285,  284,  283,  282,  281,  280,  279,  278,  277,  276,
      275,  274,  273,  271,  269,  267,  266,  265,  264,  263,
      262,  260,  259,  258,  257,  255,  254,  253,  252,  250,
      249,  248,  247,  245,  244,  242,  241,  239,  238,  237,
      232,  231,  230,  229,  227,  224,  223,  221,  220,  219,
      218,  216,  214,  213,  212,  211,  210,  208,  206,  205,
      203,  202,  199,  198,  195,  194,  193,  192,  191,  190,
      189,  188,  187,  186,  184,  183,  182,  181,  180,  179,
      178,  177,  176,  175,  174,  173,  172,  169,  168,  167,
      166,  165,  164,  163,  162,  161,  160,  159,  158,  157,

      156,  155,  153,  152,  151,  150,  149,  148,  147,  146,
      145,  144,  143,  142,  141,  140,  139,  138,  137,  136,
      135,  133,  132,  131,  130,  129,  117,  116,  115,  114,
      113,  112,  111,  110,  109,  107,  105,  104,  102,  101,
      100,   99,   97,   95,   93,   92,   91,   90,   86,   85,
       84,   83,   82,   81,   79,   77,   76,   75,   74,   71,
       70,   69,   67,   66,   65,   64,   62,   61,   52,   43,
       42,   40,   29,   27,   19,   11,    9,    8,    7,  876,
      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,
      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,

      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,
      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,
      876,  876,  876,  876,  876,  876,  876,  876,  876,  876,
      876
    } ;

static yy_state_type yy_last_accepting_state;
static char *yy_last_accepting_cpos;

extern int yy_flex_debug;
int yy_flex_debug = 0;

/* The intent behind this definition is that it'll catch
 * any uses of REJECT which flex missed.
 */
#define REJECT reject_used_but_not_detected
#define yymore() yymore_used_but_not_detected
#define YY_MORE_ADJ 0
#define YY_RESTORE_YY_MORE_OFFSET
char *yytext;
#line 1 "lexer.l"
/*
 * avrdude - A Downloader/Uploader for AVR device programmers
 * Copyright (C) 2000-2004  Brian S. Dean <<EMAIL>>
 * Copyright (C) 2006 Joerg Wunsch <<EMAIL>>
 *
 * This program is free software; you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation; either version 2 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program. If not, see <http://www.gnu.org/licenses/>.
 */
/* $Id$ */
#line 23 "lexer.l"
/* need this for the call to atof() below */
#include <math.h>
#include <string.h>
#include <errno.h>
#include <limits.h>
#include <sys/types.h>
#include <sys/stat.h>

#include "avrdude.h"
#include "libavrdude.h"
#include "config.h"

#include "config_gram.h"

#ifndef YYERRCODE
#define YYERRCODE 256
#endif

#line 996 "lexer.c"

/* Bump resources for classic lex. */
#line 999 "lexer.c"

#define INITIAL 0
#define strng 1
#define incl 2
#define comment 3

#ifndef YY_NO_UNISTD_H
/* Special case for "unistd.h", since it is non-ANSI. We include it way
 * down here because we want the user's section 1 to have been scanned first.
 * The user has a chance to override it with an option.
 */
#include <unistd.h>
#endif

#ifndef YY_EXTRA_TYPE
#define YY_EXTRA_TYPE void *
#endif

static int yy_init_globals ( void );

/* Accessor methods to globals.
   These are made visible to non-reentrant scanners for convenience. */

int yylex_destroy ( void );

int yyget_debug ( void );

void yyset_debug ( int debug_flag  );

YY_EXTRA_TYPE yyget_extra ( void );

void yyset_extra ( YY_EXTRA_TYPE user_defined  );

FILE *yyget_in ( void );

void yyset_in  ( FILE * _in_str  );

FILE *yyget_out ( void );

void yyset_out  ( FILE * _out_str  );

			int yyget_leng ( void );

char *yyget_text ( void );

int yyget_lineno ( void );

void yyset_lineno ( int _line_number  );

/* Macros after this point can all be overridden by user definitions in
 * section 1.
 */

#ifndef YY_SKIP_YYWRAP
#ifdef __cplusplus
extern "C" int yywrap ( void );
#else
extern int yywrap ( void );
#endif
#endif

#ifndef YY_NO_UNPUT
    
#endif

#ifndef yytext_ptr
static void yy_flex_strncpy ( char *, const char *, int );
#endif

#ifdef YY_NEED_STRLEN
static int yy_flex_strlen ( const char * );
#endif

#ifndef YY_NO_INPUT
#ifdef __cplusplus
static int yyinput ( void );
#else
static int input ( void );
#endif

#endif

/* Amount of stuff to slurp up with each read. */
#ifndef YY_READ_BUF_SIZE
#ifdef __ia64__
/* On IA-64, the buffer size is 16k, not 8k */
#define YY_READ_BUF_SIZE 16384
#else
#define YY_READ_BUF_SIZE 8192
#endif /* __ia64__ */
#endif

/* Copy whatever the last rule matched to the standard output. */
#ifndef ECHO
/* This used to be an fputs(), but since the string might contain NUL's,
 * we now use fwrite().
 */
#define ECHO do { if (fwrite( yytext, (size_t) yyleng, 1, yyout )) {} } while (0)
#endif

/* Gets input and stuffs it into "buf".  number of characters read, or YY_NULL,
 * is returned in "result".
 */
#ifndef YY_INPUT
#define YY_INPUT(buf,result,max_size) \
	if ( YY_CURRENT_BUFFER_LVALUE->yy_is_interactive ) \
		{ \
		int c = '*'; \
		int n; \
		for ( n = 0; n < max_size && \
			     (c = getc( yyin )) != EOF && c != '\n'; ++n ) \
			buf[n] = (char) c; \
		if ( c == '\n' ) \
			buf[n++] = (char) c; \
		if ( c == EOF && ferror( yyin ) ) \
			YY_FATAL_ERROR( "input in flex scanner failed" ); \
		result = n; \
		} \
	else \
		{ \
		errno=0; \
		while ( (result = (int) fread(buf, 1, (yy_size_t) max_size, yyin)) == 0 && ferror(yyin)) \
			{ \
			if( errno != EINTR) \
				{ \
				YY_FATAL_ERROR( "input in flex scanner failed" ); \
				break; \
				} \
			errno=0; \
			clearerr(yyin); \
			} \
		}\
\

#endif

/* No semi-colon after return; correct usage is to write "yyterminate();" -
 * we don't want an extra ';' after the "return" because that will cause
 * some compilers to complain about unreachable statements.
 */
#ifndef yyterminate
#define yyterminate() return YY_NULL
#endif

/* Number of entries by which start-condition stack grows. */
#ifndef YY_START_STACK_INCR
#define YY_START_STACK_INCR 25
#endif

/* Report a fatal error. */
#ifndef YY_FATAL_ERROR
#define YY_FATAL_ERROR(msg) yy_fatal_error( msg )
#endif

/* end tables serialization structures and prototypes */

/* Default declaration of generated scanner - a define so the user can
 * easily add parameters.
 */
#ifndef YY_DECL
#define YY_DECL_IS_OURS 1

extern int yylex (void);

#define YY_DECL int yylex (void)
#endif /* !YY_DECL */

/* Code executed at the beginning of each rule, after yytext and yyleng
 * have been set up.
 */
#ifndef YY_USER_ACTION
#define YY_USER_ACTION
#endif

/* Code executed at the end of each rule. */
#ifndef YY_BREAK
#define YY_BREAK /*LINTED*/break;
#endif

#define YY_RULE_SETUP \
	YY_USER_ACTION

/** The main scanner function which does all the work.
 */
YY_DECL
{
	yy_state_type yy_current_state;
	char *yy_cp, *yy_bp;
	int yy_act;
    
	if ( !(yy_init) )
		{
		(yy_init) = 1;

#ifdef YY_USER_INIT
		YY_USER_INIT;
#endif

		if ( ! (yy_start) )
			(yy_start) = 1;	/* first start state */

		if ( ! yyin )
			yyin = stdin;

		if ( ! yyout )
			yyout = stdout;

		if ( ! YY_CURRENT_BUFFER ) {
			yyensure_buffer_stack ();
			YY_CURRENT_BUFFER_LVALUE =
				yy_create_buffer( yyin, YY_BUF_SIZE );
		}

		yy_load_buffer_state(  );
		}

	{
#line 57 "lexer.l"


#line 1220 "lexer.c"

	while ( /*CONSTCOND*/1 )		/* loops until end-of-file is reached */
		{
		yy_cp = (yy_c_buf_p);

		/* Support of yytext. */
		*yy_cp = (yy_hold_char);

		/* yy_bp points to the position in yy_ch_buf of the start of
		 * the current run.
		 */
		yy_bp = yy_cp;

		yy_current_state = (yy_start);
yy_match:
		do
			{
			YY_CHAR yy_c = yy_ec[YY_SC_TO_UI(*yy_cp)] ;
			if ( yy_accept[yy_current_state] )
				{
				(yy_last_accepting_state) = yy_current_state;
				(yy_last_accepting_cpos) = yy_cp;
				}
			while ( yy_chk[yy_base[yy_current_state] + yy_c] != yy_current_state )
				{
				yy_current_state = (int) yy_def[yy_current_state];
				if ( yy_current_state >= 877 )
					yy_c = yy_meta[yy_c];
				}
			yy_current_state = yy_nxt[yy_base[yy_current_state] + yy_c];
			++yy_cp;
			}
		while ( yy_base[yy_current_state] != 980 );

yy_find_action:
		yy_act = yy_accept[yy_current_state];
		if ( yy_act == 0 )
			{ /* have to back up */
			yy_cp = (yy_last_accepting_cpos);
			yy_current_state = (yy_last_accepting_state);
			yy_act = yy_accept[yy_current_state];
			}

		YY_DO_BEFORE_ACTION;

do_action:	/* This label is used only to access EOF actions. */

		switch ( yy_act )
	{ /* beginning of action switch */
			case 0: /* must back up */
			/* undo the effects of YY_DO_BEFORE_ACTION */
			*yy_cp = (yy_hold_char);
			yy_cp = (yy_last_accepting_cpos);
			yy_current_state = (yy_last_accepting_state);
			goto yy_find_action;

case 1:
YY_RULE_SETUP
#line 59 "lexer.l"
{ yylval = number(yytext); return TKN_NUMBER; }
	YY_BREAK
case 2:
YY_RULE_SETUP
#line 60 "lexer.l"
{ yylval = number_real(yytext); return TKN_NUMBER_REAL; }
	YY_BREAK
case 3:
YY_RULE_SETUP
#line 61 "lexer.l"
{ yylval = number_real(yytext); return TKN_NUMBER_REAL; }
	YY_BREAK
case 4:
YY_RULE_SETUP
#line 62 "lexer.l"
{ yylval = number(yytext); return TKN_NUMBER; }
	YY_BREAK
case 5:
YY_RULE_SETUP
#line 63 "lexer.l"
{ yylval = number_real(yytext); return TKN_NUMBER_REAL; }
	YY_BREAK
case 6:
YY_RULE_SETUP
#line 64 "lexer.l"
{ yylval = number_real(yytext); return TKN_NUMBER_REAL; }
	YY_BREAK
case 7:
YY_RULE_SETUP
#line 66 "lexer.l"
{ string_buf_ptr = string_buf; BEGIN(strng); }
	YY_BREAK
case 8:
YY_RULE_SETUP
#line 68 "lexer.l"
{ yylval = hexnumber(yytext); return TKN_NUMBER; }
	YY_BREAK
case 9:
YY_RULE_SETUP
#line 72 "lexer.l"
{ /* The following eats '#' style comments to end of line */
       BEGIN(comment); }
	YY_BREAK
case 10:
YY_RULE_SETUP
#line 74 "lexer.l"
{ /* eat comments */ }
	YY_BREAK
case 11:
/* rule 11 can match eol */
YY_RULE_SETUP
#line 75 "lexer.l"
{ lineno++; BEGIN(INITIAL); }
	YY_BREAK
case 12:
YY_RULE_SETUP
#line 78 "lexer.l"
{  /* The following eats multiline C style comments */
        int c;
        int comment_start;
        
        comment_start = lineno;
        while (1) {
          while (((c = input()) != '*') && (c != EOF)) {
            /* eat up text of comment, but keep counting lines */
            if (c == '\n')
              lineno++;
          }
          
          if (c == '*') {
            while ((c = input()) == '*')
              ;
            if (c == '/')
              break;    /* found the end */
          }
          
          if (c == EOF) {
            yyerror("EOF in comment (started on line %d)", comment_start);
            return YYERRCODE;
          }
        }
     }
	YY_BREAK
case 13:
YY_RULE_SETUP
#line 105 "lexer.l"
{ *string_buf_ptr = 0; string_buf_ptr = string_buf;
             yylval = string(string_buf_ptr); BEGIN(INITIAL); return TKN_STRING; }
	YY_BREAK
case 14:
YY_RULE_SETUP
#line 107 "lexer.l"
*string_buf_ptr++ = '\n';
	YY_BREAK
case 15:
YY_RULE_SETUP
#line 108 "lexer.l"
*string_buf_ptr++ = '\t';
	YY_BREAK
case 16:
YY_RULE_SETUP
#line 109 "lexer.l"
*string_buf_ptr++ = '\r';
	YY_BREAK
case 17:
YY_RULE_SETUP
#line 110 "lexer.l"
*string_buf_ptr++ = '\b';
	YY_BREAK
case 18:
YY_RULE_SETUP
#line 111 "lexer.l"
*string_buf_ptr++ = '\f';
	YY_BREAK
case 19:
/* rule 19 can match eol */
YY_RULE_SETUP
#line 112 "lexer.l"
*(string_buf_ptr++) = yytext[1];
	YY_BREAK
case 20:
YY_RULE_SETUP
#line 113 "lexer.l"
{ char *yptr = yytext; while (*yptr) 
                                         *(string_buf_ptr++) = *(yptr++); }
	YY_BREAK
case 21:
/* rule 21 can match eol */
YY_RULE_SETUP
#line 116 "lexer.l"
{ yyerror("unterminated character constant");
            return YYERRCODE; }
	YY_BREAK
case 22:
YY_RULE_SETUP
#line 119 "lexer.l"
{ yylval=NULL; return K_ALLOWFULLPAGEBITSTREAM; }
	YY_BREAK
case 23:
YY_RULE_SETUP
#line 120 "lexer.l"
{ yylval=NULL; return K_AVR910_DEVCODE; }
	YY_BREAK
case 24:
YY_RULE_SETUP
#line 121 "lexer.l"
{ yylval=NULL; return K_PAGE_SIZE; }
	YY_BREAK
case 25:
YY_RULE_SETUP
#line 122 "lexer.l"
{ yylval=NULL; return K_PAGED; }
	YY_BREAK
case 26:
YY_RULE_SETUP
#line 123 "lexer.l"
{ yylval=NULL; return K_BAUDRATE; }
	YY_BREAK
case 27:
YY_RULE_SETUP
#line 124 "lexer.l"
{ yylval=NULL; return K_BLOCKSIZE; }
	YY_BREAK
case 28:
YY_RULE_SETUP
#line 125 "lexer.l"
{ yylval=NULL; return K_BS2; }
	YY_BREAK
case 29:
YY_RULE_SETUP
#line 126 "lexer.l"
{ yylval=NULL; return K_BUFF; }
	YY_BREAK
case 30:
YY_RULE_SETUP
#line 127 "lexer.l"
{ yylval=NULL; return K_BYTEDELAY; }
	YY_BREAK
case 31:
YY_RULE_SETUP
#line 128 "lexer.l"
{ yylval=new_token(K_CHIP_ERASE); return K_CHIP_ERASE; }
	YY_BREAK
case 32:
YY_RULE_SETUP
#line 129 "lexer.l"
{ yylval=NULL; return K_CHIP_ERASE_DELAY; }
	YY_BREAK
case 33:
YY_RULE_SETUP
#line 130 "lexer.l"
{ yylval=NULL; return K_CHIPERASEPOLLTIMEOUT; }
	YY_BREAK
case 34:
YY_RULE_SETUP
#line 131 "lexer.l"
{ yylval=NULL; return K_CHIPERASEPULSEWIDTH; }
	YY_BREAK
case 35:
YY_RULE_SETUP
#line 132 "lexer.l"
{ yylval=NULL; return K_CHIPERASETIME; }
	YY_BREAK
case 36:
YY_RULE_SETUP
#line 133 "lexer.l"
{ yylval=NULL; return K_CMDEXEDELAY; }
	YY_BREAK
case 37:
YY_RULE_SETUP
#line 134 "lexer.l"
{ yylval=NULL; return K_CONNTYPE; }
	YY_BREAK
case 38:
YY_RULE_SETUP
#line 135 "lexer.l"
{ yylval=new_token(K_DEDICATED); return K_DEDICATED; }
	YY_BREAK
case 39:
YY_RULE_SETUP
#line 136 "lexer.l"
{ yylval=NULL; return K_DEFAULT_BITCLOCK; }
	YY_BREAK
case 40:
YY_RULE_SETUP
#line 137 "lexer.l"
{ yylval=NULL; return K_DEFAULT_PARALLEL; }
	YY_BREAK
case 41:
YY_RULE_SETUP
#line 138 "lexer.l"
{ yylval=NULL; return K_DEFAULT_PROGRAMMER; }
	YY_BREAK
case 42:
YY_RULE_SETUP
#line 139 "lexer.l"
{ yylval=NULL; return K_DEFAULT_SAFEMODE; }
	YY_BREAK
case 43:
YY_RULE_SETUP
#line 140 "lexer.l"
{ yylval=NULL; return K_DEFAULT_SERIAL; }
	YY_BREAK
case 44:
YY_RULE_SETUP
#line 141 "lexer.l"
{ yylval=NULL; return K_DELAY; }
	YY_BREAK
case 45:
YY_RULE_SETUP
#line 142 "lexer.l"
{ yylval=NULL; return K_DESC; }
	YY_BREAK
case 46:
YY_RULE_SETUP
#line 143 "lexer.l"
{ yylval=NULL; return K_DEVICECODE; }
	YY_BREAK
case 47:
YY_RULE_SETUP
#line 144 "lexer.l"
{ yylval=NULL; return K_EECR; }
	YY_BREAK
case 48:
YY_RULE_SETUP
#line 145 "lexer.l"
{ yylval=NULL; return K_EEPROM; }
	YY_BREAK
case 49:
YY_RULE_SETUP
#line 146 "lexer.l"
{ yylval=NULL; return K_EEPROM_INSTR; }
	YY_BREAK
case 50:
YY_RULE_SETUP
#line 147 "lexer.l"
{ yylval=NULL; return K_ENABLEPAGEPROGRAMMING; }
	YY_BREAK
case 51:
YY_RULE_SETUP
#line 148 "lexer.l"
{ yylval=NULL; return K_ERRLED; }
	YY_BREAK
case 52:
YY_RULE_SETUP
#line 149 "lexer.l"
{ yylval=NULL; return K_FLASH; }
	YY_BREAK
case 53:
YY_RULE_SETUP
#line 150 "lexer.l"
{ yylval=NULL; return K_FLASH_INSTR; }
	YY_BREAK
case 54:
YY_RULE_SETUP
#line 151 "lexer.l"
{ yylval=NULL; return K_HAS_DW; }
	YY_BREAK
case 55:
YY_RULE_SETUP
#line 152 "lexer.l"
{ yylval=NULL; return K_HAS_JTAG; }
	YY_BREAK
case 56:
YY_RULE_SETUP
#line 153 "lexer.l"
{ yylval=NULL; return K_HAS_PDI; }
	YY_BREAK
case 57:
YY_RULE_SETUP
#line 154 "lexer.l"
{ yylval=NULL; return K_HAS_TPI; }
	YY_BREAK
case 58:
YY_RULE_SETUP
#line 155 "lexer.l"
{ yylval=NULL; return K_HVENTERSTABDELAY; }
	YY_BREAK
case 59:
YY_RULE_SETUP
#line 156 "lexer.l"
{ yylval=NULL; return K_HVLEAVESTABDELAY; }
	YY_BREAK
case 60:
YY_RULE_SETUP
#line 157 "lexer.l"
{ yylval=NULL; return K_HVSP_CONTROLSTACK; }
	YY_BREAK
case 61:
YY_RULE_SETUP
#line 158 "lexer.l"
{ yylval=NULL; return K_HVSPCMDEXEDELAY; }
	YY_BREAK
case 62:
YY_RULE_SETUP
#line 159 "lexer.l"
{ yylval=NULL; return K_ID; }
	YY_BREAK
case 63:
YY_RULE_SETUP
#line 160 "lexer.l"
{ yylval=NULL; return K_IDR; }
	YY_BREAK
case 64:
YY_RULE_SETUP
#line 161 "lexer.l"
{ yylval=new_token(K_IO); return K_IO; }
	YY_BREAK
case 65:
YY_RULE_SETUP
#line 162 "lexer.l"
{ yylval=NULL; return K_IS_AT90S1200; }
	YY_BREAK
case 66:
YY_RULE_SETUP
#line 163 "lexer.l"
{ yylval=NULL; return K_IS_AVR32; }
	YY_BREAK
case 67:
YY_RULE_SETUP
#line 164 "lexer.l"
{ yylval=NULL; return K_LATCHCYCLES; }
	YY_BREAK
case 68:
YY_RULE_SETUP
#line 165 "lexer.l"
{ yylval=new_token(K_LOAD_EXT_ADDR); return K_LOAD_EXT_ADDR; }
	YY_BREAK
case 69:
YY_RULE_SETUP
#line 166 "lexer.l"
{ yylval=new_token(K_LOADPAGE_HI); return K_LOADPAGE_HI; }
	YY_BREAK
case 70:
YY_RULE_SETUP
#line 167 "lexer.l"
{ yylval=new_token(K_LOADPAGE_LO); return K_LOADPAGE_LO; }
	YY_BREAK
case 71:
YY_RULE_SETUP
#line 168 "lexer.l"
{ yylval=NULL; return K_MAX_WRITE_DELAY; }
	YY_BREAK
case 72:
YY_RULE_SETUP
#line 169 "lexer.l"
{ yylval=NULL; return K_MCU_BASE; }
	YY_BREAK
case 73:
YY_RULE_SETUP
#line 170 "lexer.l"
{ yylval=NULL; return K_MEMORY; }
	YY_BREAK
case 74:
YY_RULE_SETUP
#line 171 "lexer.l"
{ yylval=NULL; return K_MIN_WRITE_DELAY; }
	YY_BREAK
case 75:
YY_RULE_SETUP
#line 172 "lexer.l"
{ yylval=NULL; return K_MISO; }
	YY_BREAK
case 76:
YY_RULE_SETUP
#line 173 "lexer.l"
{ yylval=NULL; return K_MODE; }
	YY_BREAK
case 77:
YY_RULE_SETUP
#line 174 "lexer.l"
{ yylval=NULL; return K_MOSI; }
	YY_BREAK
case 78:
YY_RULE_SETUP
#line 175 "lexer.l"
{ yylval=new_token(K_NO); return K_NO; }
	YY_BREAK
case 79:
YY_RULE_SETUP
#line 176 "lexer.l"
{ yylval=NULL; return K_NUM_PAGES; }
	YY_BREAK
case 80:
YY_RULE_SETUP
#line 177 "lexer.l"
{ yylval=NULL; return K_NUM_PAGES; }
	YY_BREAK
case 81:
YY_RULE_SETUP
#line 178 "lexer.l"
{ yylval=NULL; return K_NVM_BASE; }
	YY_BREAK
case 82:
YY_RULE_SETUP
#line 179 "lexer.l"
{ yylval=NULL; return K_OCDREV; }
	YY_BREAK
case 83:
YY_RULE_SETUP
#line 180 "lexer.l"
{ yylval=NULL; return K_OFFSET; }
	YY_BREAK
case 84:
YY_RULE_SETUP
#line 181 "lexer.l"
{ yylval=NULL; return K_PAGE_SIZE; }
	YY_BREAK
case 85:
YY_RULE_SETUP
#line 182 "lexer.l"
{ yylval=NULL; return K_PAGED; }
	YY_BREAK
case 86:
YY_RULE_SETUP
#line 183 "lexer.l"
{ yylval=NULL; return K_PAGEL; }
	YY_BREAK
case 87:
YY_RULE_SETUP
#line 184 "lexer.l"
{ yylval=NULL; return K_PARALLEL; }
	YY_BREAK
case 88:
YY_RULE_SETUP
#line 185 "lexer.l"
{ yylval=NULL; return K_PARENT; }
	YY_BREAK
case 89:
YY_RULE_SETUP
#line 186 "lexer.l"
{ yylval=NULL; return K_PART; }
	YY_BREAK
case 90:
YY_RULE_SETUP
#line 187 "lexer.l"
{ yylval=new_token(K_PGM_ENABLE); return K_PGM_ENABLE; }
	YY_BREAK
case 91:
YY_RULE_SETUP
#line 188 "lexer.l"
{ yylval=NULL; return K_PGMLED; }
	YY_BREAK
case 92:
YY_RULE_SETUP
#line 189 "lexer.l"
{ yylval=NULL; return K_POLLINDEX; }
	YY_BREAK
case 93:
YY_RULE_SETUP
#line 190 "lexer.l"
{ yylval=NULL; return K_POLLMETHOD; }
	YY_BREAK
case 94:
YY_RULE_SETUP
#line 191 "lexer.l"
{ yylval=NULL; return K_POLLVALUE; }
	YY_BREAK
case 95:
YY_RULE_SETUP
#line 192 "lexer.l"
{ yylval=NULL; return K_POSTDELAY; }
	YY_BREAK
case 96:
YY_RULE_SETUP
#line 193 "lexer.l"
{ yylval=NULL; return K_POWEROFFDELAY; }
	YY_BREAK
case 97:
YY_RULE_SETUP
#line 194 "lexer.l"
{ yylval=NULL; return K_PP_CONTROLSTACK; }
	YY_BREAK
case 98:
YY_RULE_SETUP
#line 195 "lexer.l"
{ yylval=NULL; return K_PREDELAY; }
	YY_BREAK
case 99:
YY_RULE_SETUP
#line 196 "lexer.l"
{ yylval=NULL; return K_PROGMODEDELAY; }
	YY_BREAK
case 100:
YY_RULE_SETUP
#line 197 "lexer.l"
{ yylval=NULL; return K_PROGRAMFUSEPOLLTIMEOUT; }
	YY_BREAK
case 101:
YY_RULE_SETUP
#line 198 "lexer.l"
{ yylval=NULL; return K_PROGRAMFUSEPULSEWIDTH; }
	YY_BREAK
case 102:
YY_RULE_SETUP
#line 199 "lexer.l"
{ yylval=NULL; return K_PROGRAMLOCKPOLLTIMEOUT; }
	YY_BREAK
case 103:
YY_RULE_SETUP
#line 200 "lexer.l"
{ yylval=NULL; return K_PROGRAMLOCKPULSEWIDTH; }
	YY_BREAK
case 104:
YY_RULE_SETUP
#line 201 "lexer.l"
{ yylval=NULL; return K_PROGRAMMER; }
	YY_BREAK
case 105:
YY_RULE_SETUP
#line 202 "lexer.l"
{ yylval=new_token(K_PSEUDO); return K_PSEUDO; }
	YY_BREAK
case 106:
YY_RULE_SETUP
#line 203 "lexer.l"
{ yylval=NULL; return K_PWROFF_AFTER_WRITE; }
	YY_BREAK
case 107:
YY_RULE_SETUP
#line 204 "lexer.l"
{ yylval=NULL; return K_RAMPZ; }
	YY_BREAK
case 108:
YY_RULE_SETUP
#line 205 "lexer.l"
{ yylval=NULL; return K_RDYLED; }
	YY_BREAK
case 109:
YY_RULE_SETUP
#line 206 "lexer.l"
{ yylval=new_token(K_READ); return K_READ; }
	YY_BREAK
case 110:
YY_RULE_SETUP
#line 207 "lexer.l"
{ yylval=new_token(K_READ_HI); return K_READ_HI; }
	YY_BREAK
case 111:
YY_RULE_SETUP
#line 208 "lexer.l"
{ yylval=new_token(K_READ_LO); return K_READ_LO; }
	YY_BREAK
case 112:
YY_RULE_SETUP
#line 209 "lexer.l"
{ yylval=NULL; return K_READBACK_P1; }
	YY_BREAK
case 113:
YY_RULE_SETUP
#line 210 "lexer.l"
{ yylval=NULL; return K_READBACK_P2; }
	YY_BREAK
case 114:
YY_RULE_SETUP
#line 211 "lexer.l"
{ yylval=NULL; return K_READSIZE; }
	YY_BREAK
case 115:
YY_RULE_SETUP
#line 212 "lexer.l"
{ yylval=new_token(K_RESET); return K_RESET; }
	YY_BREAK
case 116:
YY_RULE_SETUP
#line 213 "lexer.l"
{ yylval=NULL; return K_RESETDELAY; }
	YY_BREAK
case 117:
YY_RULE_SETUP
#line 214 "lexer.l"
{ yylval=NULL; return K_RESETDELAYMS; }
	YY_BREAK
case 118:
YY_RULE_SETUP
#line 215 "lexer.l"
{ yylval=NULL; return K_RESETDELAYUS; }
	YY_BREAK
case 119:
YY_RULE_SETUP
#line 216 "lexer.l"
{ yylval=NULL; return K_RETRY_PULSE; }
	YY_BREAK
case 120:
YY_RULE_SETUP
#line 217 "lexer.l"
{ yylval=new_token(K_SCK); return K_SCK; }
	YY_BREAK
case 121:
YY_RULE_SETUP
#line 218 "lexer.l"
{ yylval=NULL; return K_SERIAL; }
	YY_BREAK
case 122:
YY_RULE_SETUP
#line 219 "lexer.l"
{ yylval=NULL; return K_SIGNATURE; }
	YY_BREAK
case 123:
YY_RULE_SETUP
#line 220 "lexer.l"
{ yylval=NULL; return K_SIZE; }
	YY_BREAK
case 124:
YY_RULE_SETUP
#line 221 "lexer.l"
{ yylval=NULL; return K_SPMCR; }
	YY_BREAK
case 125:
YY_RULE_SETUP
#line 222 "lexer.l"
{ yylval=NULL; return K_STABDELAY; }
	YY_BREAK
case 126:
YY_RULE_SETUP
#line 223 "lexer.l"
{ yylval=NULL; return K_STK500_DEVCODE; }
	YY_BREAK
case 127:
YY_RULE_SETUP
#line 224 "lexer.l"
{ yylval=NULL; return K_SYNCHCYCLES; }
	YY_BREAK
case 128:
YY_RULE_SETUP
#line 225 "lexer.l"
{ yylval=NULL; return K_SYNCHLOOPS; }
	YY_BREAK
case 129:
YY_RULE_SETUP
#line 226 "lexer.l"
{ yylval=NULL; return K_TIMEOUT; }
	YY_BREAK
case 130:
YY_RULE_SETUP
#line 227 "lexer.l"
{ yylval=NULL; return K_TOGGLEVTG; }
	YY_BREAK
case 131:
YY_RULE_SETUP
#line 228 "lexer.l"
{ yylval=NULL; return K_TYPE; }
	YY_BREAK
case 132:
YY_RULE_SETUP
#line 229 "lexer.l"
{ yylval=NULL; return K_USB; }
	YY_BREAK
case 133:
YY_RULE_SETUP
#line 230 "lexer.l"
{ yylval=NULL; return K_USBDEV; }
	YY_BREAK
case 134:
YY_RULE_SETUP
#line 231 "lexer.l"
{ yylval=NULL; return K_USBPID; }
	YY_BREAK
case 135:
YY_RULE_SETUP
#line 232 "lexer.l"
{ yylval=NULL; return K_USBPRODUCT; }
	YY_BREAK
case 136:
YY_RULE_SETUP
#line 233 "lexer.l"
{ yylval=NULL; return K_USBSN; }
	YY_BREAK
case 137:
YY_RULE_SETUP
#line 234 "lexer.l"
{ yylval=NULL; return K_USBVENDOR; }
	YY_BREAK
case 138:
YY_RULE_SETUP
#line 235 "lexer.l"
{ yylval=NULL; return K_USBVID; }
	YY_BREAK
case 139:
YY_RULE_SETUP
#line 236 "lexer.l"
{ yylval=NULL; return K_VCC; }
	YY_BREAK
case 140:
YY_RULE_SETUP
#line 237 "lexer.l"
{ yylval=NULL; return K_VFYLED; }
	YY_BREAK
case 141:
YY_RULE_SETUP
#line 238 "lexer.l"
{ yylval=new_token(K_WRITE); return K_WRITE; }
	YY_BREAK
case 142:
YY_RULE_SETUP
#line 239 "lexer.l"
{ yylval=new_token(K_WRITE_HI); return K_WRITE_HI; }
	YY_BREAK
case 143:
YY_RULE_SETUP
#line 240 "lexer.l"
{ yylval=new_token(K_WRITE_LO); return K_WRITE_LO; }
	YY_BREAK
case 144:
YY_RULE_SETUP
#line 241 "lexer.l"
{ yylval=new_token(K_WRITEPAGE); return K_WRITEPAGE; }
	YY_BREAK
case 145:
YY_RULE_SETUP
#line 242 "lexer.l"
{ yylval=new_token(K_YES); return K_YES; }
	YY_BREAK
case 146:
YY_RULE_SETUP
#line 244 "lexer.l"
{ yylval = NULL; pyytext(); return TKN_COMMA; }
	YY_BREAK
case 147:
YY_RULE_SETUP
#line 245 "lexer.l"
{ yylval = NULL; pyytext(); return TKN_EQUAL; }
	YY_BREAK
case 148:
YY_RULE_SETUP
#line 246 "lexer.l"
{ yylval = NULL; pyytext(); return TKN_SEMI; }
	YY_BREAK
case 149:
YY_RULE_SETUP
#line 247 "lexer.l"
{ yylval = NULL; pyytext(); return TKN_TILDE; }
	YY_BREAK
case 150:
YY_RULE_SETUP
#line 248 "lexer.l"
{ yylval = NULL; pyytext(); return TKN_LEFT_PAREN; }
	YY_BREAK
case 151:
YY_RULE_SETUP
#line 249 "lexer.l"
{ yylval = NULL; pyytext(); return TKN_RIGHT_PAREN; }
	YY_BREAK
case 152:
/* rule 152 can match eol */
YY_RULE_SETUP
#line 251 "lexer.l"
{ lineno++; }
	YY_BREAK
case 153:
YY_RULE_SETUP
#line 252 "lexer.l"
{ /* ignore whitespace */ }
	YY_BREAK
case 154:
YY_RULE_SETUP
#line 254 "lexer.l"
{ yyerror("possible old-style config file entry\n"
             "  Update your config file (see " CONFIG_DIR 
               "/avrdude.conf.sample for a sample)");
     return YYERRCODE; }
	YY_BREAK
case 155:
YY_RULE_SETUP
#line 259 "lexer.l"
{ return YYERRCODE; }
	YY_BREAK
case 156:
YY_RULE_SETUP
#line 261 "lexer.l"
ECHO;
	YY_BREAK
#line 2092 "lexer.c"
case YY_STATE_EOF(INITIAL):
case YY_STATE_EOF(strng):
case YY_STATE_EOF(incl):
case YY_STATE_EOF(comment):
	yyterminate();

	case YY_END_OF_BUFFER:
		{
		/* Amount of text matched not including the EOB char. */
		int yy_amount_of_matched_text = (int) (yy_cp - (yytext_ptr)) - 1;

		/* Undo the effects of YY_DO_BEFORE_ACTION. */
		*yy_cp = (yy_hold_char);
		YY_RESTORE_YY_MORE_OFFSET

		if ( YY_CURRENT_BUFFER_LVALUE->yy_buffer_status == YY_BUFFER_NEW )
			{
			/* We're scanning a new file or input source.  It's
			 * possible that this happened because the user
			 * just pointed yyin at a new source and called
			 * yylex().  If so, then we have to assure
			 * consistency between YY_CURRENT_BUFFER and our
			 * globals.  Here is the right place to do so, because
			 * this is the first action (other than possibly a
			 * back-up) that will match for the new input source.
			 */
			(yy_n_chars) = YY_CURRENT_BUFFER_LVALUE->yy_n_chars;
			YY_CURRENT_BUFFER_LVALUE->yy_input_file = yyin;
			YY_CURRENT_BUFFER_LVALUE->yy_buffer_status = YY_BUFFER_NORMAL;
			}

		/* Note that here we test for yy_c_buf_p "<=" to the position
		 * of the first EOB in the buffer, since yy_c_buf_p will
		 * already have been incremented past the NUL character
		 * (since all states make transitions on EOB to the
		 * end-of-buffer state).  Contrast this with the test
		 * in input().
		 */
		if ( (yy_c_buf_p) <= &YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[(yy_n_chars)] )
			{ /* This was really a NUL. */
			yy_state_type yy_next_state;

			(yy_c_buf_p) = (yytext_ptr) + yy_amount_of_matched_text;

			yy_current_state = yy_get_previous_state(  );

			/* Okay, we're now positioned to make the NUL
			 * transition.  We couldn't have
			 * yy_get_previous_state() go ahead and do it
			 * for us because it doesn't know how to deal
			 * with the possibility of jamming (and we don't
			 * want to build jamming into it because then it
			 * will run more slowly).
			 */

			yy_next_state = yy_try_NUL_trans( yy_current_state );

			yy_bp = (yytext_ptr) + YY_MORE_ADJ;

			if ( yy_next_state )
				{
				/* Consume the NUL. */
				yy_cp = ++(yy_c_buf_p);
				yy_current_state = yy_next_state;
				goto yy_match;
				}

			else
				{
				yy_cp = (yy_c_buf_p);
				goto yy_find_action;
				}
			}

		else switch ( yy_get_next_buffer(  ) )
			{
			case EOB_ACT_END_OF_FILE:
				{
				(yy_did_buffer_switch_on_eof) = 0;

				if ( yywrap(  ) )
					{
					/* Note: because we've taken care in
					 * yy_get_next_buffer() to have set up
					 * yytext, we can now set up
					 * yy_c_buf_p so that if some total
					 * hoser (like flex itself) wants to
					 * call the scanner after we return the
					 * YY_NULL, it'll still work - another
					 * YY_NULL will get returned.
					 */
					(yy_c_buf_p) = (yytext_ptr) + YY_MORE_ADJ;

					yy_act = YY_STATE_EOF(YY_START);
					goto do_action;
					}

				else
					{
					if ( ! (yy_did_buffer_switch_on_eof) )
						YY_NEW_FILE;
					}
				break;
				}

			case EOB_ACT_CONTINUE_SCAN:
				(yy_c_buf_p) =
					(yytext_ptr) + yy_amount_of_matched_text;

				yy_current_state = yy_get_previous_state(  );

				yy_cp = (yy_c_buf_p);
				yy_bp = (yytext_ptr) + YY_MORE_ADJ;
				goto yy_match;

			case EOB_ACT_LAST_MATCH:
				(yy_c_buf_p) =
				&YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[(yy_n_chars)];

				yy_current_state = yy_get_previous_state(  );

				yy_cp = (yy_c_buf_p);
				yy_bp = (yytext_ptr) + YY_MORE_ADJ;
				goto yy_find_action;
			}
		break;
		}

	default:
		YY_FATAL_ERROR(
			"fatal flex scanner internal error--no action found" );
	} /* end of action switch */
		} /* end of scanning one token */
	} /* end of user's declarations */
} /* end of yylex */

/* yy_get_next_buffer - try to read in a new buffer
 *
 * Returns a code representing an action:
 *	EOB_ACT_LAST_MATCH -
 *	EOB_ACT_CONTINUE_SCAN - continue scanning from current position
 *	EOB_ACT_END_OF_FILE - end of file
 */
static int yy_get_next_buffer (void)
{
    	char *dest = YY_CURRENT_BUFFER_LVALUE->yy_ch_buf;
	char *source = (yytext_ptr);
	int number_to_move, i;
	int ret_val;

	if ( (yy_c_buf_p) > &YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[(yy_n_chars) + 1] )
		YY_FATAL_ERROR(
		"fatal flex scanner internal error--end of buffer missed" );

	if ( YY_CURRENT_BUFFER_LVALUE->yy_fill_buffer == 0 )
		{ /* Don't try to fill the buffer, so this is an EOF. */
		if ( (yy_c_buf_p) - (yytext_ptr) - YY_MORE_ADJ == 1 )
			{
			/* We matched a single character, the EOB, so
			 * treat this as a final EOF.
			 */
			return EOB_ACT_END_OF_FILE;
			}

		else
			{
			/* We matched some text prior to the EOB, first
			 * process it.
			 */
			return EOB_ACT_LAST_MATCH;
			}
		}

	/* Try to read more data. */

	/* First move last chars to start of buffer. */
	number_to_move = (int) ((yy_c_buf_p) - (yytext_ptr) - 1);

	for ( i = 0; i < number_to_move; ++i )
		*(dest++) = *(source++);

	if ( YY_CURRENT_BUFFER_LVALUE->yy_buffer_status == YY_BUFFER_EOF_PENDING )
		/* don't do the read, it's not guaranteed to return an EOF,
		 * just force an EOF
		 */
		YY_CURRENT_BUFFER_LVALUE->yy_n_chars = (yy_n_chars) = 0;

	else
		{
			int num_to_read =
			YY_CURRENT_BUFFER_LVALUE->yy_buf_size - number_to_move - 1;

		while ( num_to_read <= 0 )
			{ /* Not enough room in the buffer - grow it. */

			/* just a shorter name for the current buffer */
			YY_BUFFER_STATE b = YY_CURRENT_BUFFER_LVALUE;

			int yy_c_buf_p_offset =
				(int) ((yy_c_buf_p) - b->yy_ch_buf);

			if ( b->yy_is_our_buffer )
				{
				int new_size = b->yy_buf_size * 2;

				if ( new_size <= 0 )
					b->yy_buf_size += b->yy_buf_size / 8;
				else
					b->yy_buf_size *= 2;

				b->yy_ch_buf = (char *)
					/* Include room in for 2 EOB chars. */
					yyrealloc( (void *) b->yy_ch_buf,
							 (yy_size_t) (b->yy_buf_size + 2)  );
				}
			else
				/* Can't grow it, we don't own it. */
				b->yy_ch_buf = NULL;

			if ( ! b->yy_ch_buf )
				YY_FATAL_ERROR(
				"fatal error - scanner input buffer overflow" );

			(yy_c_buf_p) = &b->yy_ch_buf[yy_c_buf_p_offset];

			num_to_read = YY_CURRENT_BUFFER_LVALUE->yy_buf_size -
						number_to_move - 1;

			}

		if ( num_to_read > YY_READ_BUF_SIZE )
			num_to_read = YY_READ_BUF_SIZE;

		/* Read in more data. */
		YY_INPUT( (&YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[number_to_move]),
			(yy_n_chars), num_to_read );

		YY_CURRENT_BUFFER_LVALUE->yy_n_chars = (yy_n_chars);
		}

	if ( (yy_n_chars) == 0 )
		{
		if ( number_to_move == YY_MORE_ADJ )
			{
			ret_val = EOB_ACT_END_OF_FILE;
			yyrestart( yyin  );
			}

		else
			{
			ret_val = EOB_ACT_LAST_MATCH;
			YY_CURRENT_BUFFER_LVALUE->yy_buffer_status =
				YY_BUFFER_EOF_PENDING;
			}
		}

	else
		ret_val = EOB_ACT_CONTINUE_SCAN;

	if (((yy_n_chars) + number_to_move) > YY_CURRENT_BUFFER_LVALUE->yy_buf_size) {
		/* Extend the array by 50%, plus the number we really need. */
		int new_size = (yy_n_chars) + number_to_move + ((yy_n_chars) >> 1);
		YY_CURRENT_BUFFER_LVALUE->yy_ch_buf = (char *) yyrealloc(
			(void *) YY_CURRENT_BUFFER_LVALUE->yy_ch_buf, (yy_size_t) new_size  );
		if ( ! YY_CURRENT_BUFFER_LVALUE->yy_ch_buf )
			YY_FATAL_ERROR( "out of dynamic memory in yy_get_next_buffer()" );
		/* "- 2" to take care of EOB's */
		YY_CURRENT_BUFFER_LVALUE->yy_buf_size = (int) (new_size - 2);
	}

	(yy_n_chars) += number_to_move;
	YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[(yy_n_chars)] = YY_END_OF_BUFFER_CHAR;
	YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[(yy_n_chars) + 1] = YY_END_OF_BUFFER_CHAR;

	(yytext_ptr) = &YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[0];

	return ret_val;
}

/* yy_get_previous_state - get the state just before the EOB char was reached */

    static yy_state_type yy_get_previous_state (void)
{
	yy_state_type yy_current_state;
	char *yy_cp;
    
	yy_current_state = (yy_start);

	for ( yy_cp = (yytext_ptr) + YY_MORE_ADJ; yy_cp < (yy_c_buf_p); ++yy_cp )
		{
		YY_CHAR yy_c = (*yy_cp ? yy_ec[YY_SC_TO_UI(*yy_cp)] : 1);
		if ( yy_accept[yy_current_state] )
			{
			(yy_last_accepting_state) = yy_current_state;
			(yy_last_accepting_cpos) = yy_cp;
			}
		while ( yy_chk[yy_base[yy_current_state] + yy_c] != yy_current_state )
			{
			yy_current_state = (int) yy_def[yy_current_state];
			if ( yy_current_state >= 877 )
				yy_c = yy_meta[yy_c];
			}
		yy_current_state = yy_nxt[yy_base[yy_current_state] + yy_c];
		}

	return yy_current_state;
}

/* yy_try_NUL_trans - try to make a transition on the NUL character
 *
 * synopsis
 *	next_state = yy_try_NUL_trans( current_state );
 */
    static yy_state_type yy_try_NUL_trans  (yy_state_type yy_current_state )
{
	int yy_is_jam;
    	char *yy_cp = (yy_c_buf_p);

	YY_CHAR yy_c = 1;
	if ( yy_accept[yy_current_state] )
		{
		(yy_last_accepting_state) = yy_current_state;
		(yy_last_accepting_cpos) = yy_cp;
		}
	while ( yy_chk[yy_base[yy_current_state] + yy_c] != yy_current_state )
		{
		yy_current_state = (int) yy_def[yy_current_state];
		if ( yy_current_state >= 877 )
			yy_c = yy_meta[yy_c];
		}
	yy_current_state = yy_nxt[yy_base[yy_current_state] + yy_c];
	yy_is_jam = (yy_current_state == 876);

		return yy_is_jam ? 0 : yy_current_state;
}

#ifndef YY_NO_UNPUT

#endif

#ifndef YY_NO_INPUT
#ifdef __cplusplus
    static int yyinput (void)
#else
    static int input  (void)
#endif

{
	int c;
    
	*(yy_c_buf_p) = (yy_hold_char);

	if ( *(yy_c_buf_p) == YY_END_OF_BUFFER_CHAR )
		{
		/* yy_c_buf_p now points to the character we want to return.
		 * If this occurs *before* the EOB characters, then it's a
		 * valid NUL; if not, then we've hit the end of the buffer.
		 */
		if ( (yy_c_buf_p) < &YY_CURRENT_BUFFER_LVALUE->yy_ch_buf[(yy_n_chars)] )
			/* This was really a NUL. */
			*(yy_c_buf_p) = '\0';

		else
			{ /* need more input */
			int offset = (int) ((yy_c_buf_p) - (yytext_ptr));
			++(yy_c_buf_p);

			switch ( yy_get_next_buffer(  ) )
				{
				case EOB_ACT_LAST_MATCH:
					/* This happens because yy_g_n_b()
					 * sees that we've accumulated a
					 * token and flags that we need to
					 * try matching the token before
					 * proceeding.  But for input(),
					 * there's no matching to consider.
					 * So convert the EOB_ACT_LAST_MATCH
					 * to EOB_ACT_END_OF_FILE.
					 */

					/* Reset buffer status. */
					yyrestart( yyin );

					/*FALLTHROUGH*/

				case EOB_ACT_END_OF_FILE:
					{
					if ( yywrap(  ) )
						return 0;

					if ( ! (yy_did_buffer_switch_on_eof) )
						YY_NEW_FILE;
#ifdef __cplusplus
					return yyinput();
#else
					return input();
#endif
					}

				case EOB_ACT_CONTINUE_SCAN:
					(yy_c_buf_p) = (yytext_ptr) + offset;
					break;
				}
			}
		}

	c = *(unsigned char *) (yy_c_buf_p);	/* cast for 8-bit char's */
	*(yy_c_buf_p) = '\0';	/* preserve yytext */
	(yy_hold_char) = *++(yy_c_buf_p);

	return c;
}
#endif	/* ifndef YY_NO_INPUT */

/** Immediately switch to a different input stream.
 * @param input_file A readable stream.
 * 
 * @note This function does not reset the start condition to @c INITIAL .
 */
    void yyrestart  (FILE * input_file )
{
    
	if ( ! YY_CURRENT_BUFFER ){
        yyensure_buffer_stack ();
		YY_CURRENT_BUFFER_LVALUE =
            yy_create_buffer( yyin, YY_BUF_SIZE );
	}

	yy_init_buffer( YY_CURRENT_BUFFER, input_file );
	yy_load_buffer_state(  );
}

/** Switch to a different input buffer.
 * @param new_buffer The new input buffer.
 * 
 */
    void yy_switch_to_buffer  (YY_BUFFER_STATE  new_buffer )
{
    
	/* TODO. We should be able to replace this entire function body
	 * with
	 *		yypop_buffer_state();
	 *		yypush_buffer_state(new_buffer);
     */
	yyensure_buffer_stack ();
	if ( YY_CURRENT_BUFFER == new_buffer )
		return;

	if ( YY_CURRENT_BUFFER )
		{
		/* Flush out information for old buffer. */
		*(yy_c_buf_p) = (yy_hold_char);
		YY_CURRENT_BUFFER_LVALUE->yy_buf_pos = (yy_c_buf_p);
		YY_CURRENT_BUFFER_LVALUE->yy_n_chars = (yy_n_chars);
		}

	YY_CURRENT_BUFFER_LVALUE = new_buffer;
	yy_load_buffer_state(  );

	/* We don't actually know whether we did this switch during
	 * EOF (yywrap()) processing, but the only time this flag
	 * is looked at is after yywrap() is called, so it's safe
	 * to go ahead and always set it.
	 */
	(yy_did_buffer_switch_on_eof) = 1;
}

static void yy_load_buffer_state  (void)
{
    	(yy_n_chars) = YY_CURRENT_BUFFER_LVALUE->yy_n_chars;
	(yytext_ptr) = (yy_c_buf_p) = YY_CURRENT_BUFFER_LVALUE->yy_buf_pos;
	yyin = YY_CURRENT_BUFFER_LVALUE->yy_input_file;
	(yy_hold_char) = *(yy_c_buf_p);
}

/** Allocate and initialize an input buffer state.
 * @param file A readable stream.
 * @param size The character buffer size in bytes. When in doubt, use @c YY_BUF_SIZE.
 * 
 * @return the allocated buffer state.
 */
    YY_BUFFER_STATE yy_create_buffer  (FILE * file, int  size )
{
	YY_BUFFER_STATE b;
    
	b = (YY_BUFFER_STATE) yyalloc( sizeof( struct yy_buffer_state )  );
	if ( ! b )
		YY_FATAL_ERROR( "out of dynamic memory in yy_create_buffer()" );

	b->yy_buf_size = size;

	/* yy_ch_buf has to be 2 characters longer than the size given because
	 * we need to put in 2 end-of-buffer characters.
	 */
	b->yy_ch_buf = (char *) yyalloc( (yy_size_t) (b->yy_buf_size + 2)  );
	if ( ! b->yy_ch_buf )
		YY_FATAL_ERROR( "out of dynamic memory in yy_create_buffer()" );

	b->yy_is_our_buffer = 1;

	yy_init_buffer( b, file );

	return b;
}

/** Destroy the buffer.
 * @param b a buffer created with yy_create_buffer()
 * 
 */
    void yy_delete_buffer (YY_BUFFER_STATE  b )
{
    
	if ( ! b )
		return;

	if ( b == YY_CURRENT_BUFFER ) /* Not sure if we should pop here. */
		YY_CURRENT_BUFFER_LVALUE = (YY_BUFFER_STATE) 0;

	if ( b->yy_is_our_buffer )
		yyfree( (void *) b->yy_ch_buf  );

	yyfree( (void *) b  );
}

/* Initializes or reinitializes a buffer.
 * This function is sometimes called more than once on the same buffer,
 * such as during a yyrestart() or at EOF.
 */
    static void yy_init_buffer  (YY_BUFFER_STATE  b, FILE * file )

{
	int oerrno = errno;
    
	yy_flush_buffer( b );

	b->yy_input_file = file;
	b->yy_fill_buffer = 1;

    /* If b is the current buffer, then yy_init_buffer was _probably_
     * called from yyrestart() or through yy_get_next_buffer.
     * In that case, we don't want to reset the lineno or column.
     */
    if (b != YY_CURRENT_BUFFER){
        b->yy_bs_lineno = 1;
        b->yy_bs_column = 0;
    }

        b->yy_is_interactive = file ? (isatty( fileno(file) ) > 0) : 0;
    
	errno = oerrno;
}

/** Discard all buffered characters. On the next scan, YY_INPUT will be called.
 * @param b the buffer state to be flushed, usually @c YY_CURRENT_BUFFER.
 * 
 */
    void yy_flush_buffer (YY_BUFFER_STATE  b )
{
    	if ( ! b )
		return;

	b->yy_n_chars = 0;

	/* We always need two end-of-buffer characters.  The first causes
	 * a transition to the end-of-buffer state.  The second causes
	 * a jam in that state.
	 */
	b->yy_ch_buf[0] = YY_END_OF_BUFFER_CHAR;
	b->yy_ch_buf[1] = YY_END_OF_BUFFER_CHAR;

	b->yy_buf_pos = &b->yy_ch_buf[0];

	b->yy_at_bol = 1;
	b->yy_buffer_status = YY_BUFFER_NEW;

	if ( b == YY_CURRENT_BUFFER )
		yy_load_buffer_state(  );
}

/** Pushes the new state onto the stack. The new state becomes
 *  the current state. This function will allocate the stack
 *  if necessary.
 *  @param new_buffer The new state.
 *  
 */
void yypush_buffer_state (YY_BUFFER_STATE new_buffer )
{
    	if (new_buffer == NULL)
		return;

	yyensure_buffer_stack();

	/* This block is copied from yy_switch_to_buffer. */
	if ( YY_CURRENT_BUFFER )
		{
		/* Flush out information for old buffer. */
		*(yy_c_buf_p) = (yy_hold_char);
		YY_CURRENT_BUFFER_LVALUE->yy_buf_pos = (yy_c_buf_p);
		YY_CURRENT_BUFFER_LVALUE->yy_n_chars = (yy_n_chars);
		}

	/* Only push if top exists. Otherwise, replace top. */
	if (YY_CURRENT_BUFFER)
		(yy_buffer_stack_top)++;
	YY_CURRENT_BUFFER_LVALUE = new_buffer;

	/* copied from yy_switch_to_buffer. */
	yy_load_buffer_state(  );
	(yy_did_buffer_switch_on_eof) = 1;
}

/** Removes and deletes the top of the stack, if present.
 *  The next element becomes the new top.
 *  
 */
void yypop_buffer_state (void)
{
    	if (!YY_CURRENT_BUFFER)
		return;

	yy_delete_buffer(YY_CURRENT_BUFFER );
	YY_CURRENT_BUFFER_LVALUE = NULL;
	if ((yy_buffer_stack_top) > 0)
		--(yy_buffer_stack_top);

	if (YY_CURRENT_BUFFER) {
		yy_load_buffer_state(  );
		(yy_did_buffer_switch_on_eof) = 1;
	}
}

/* Allocates the stack if it does not exist.
 *  Guarantees space for at least one push.
 */
static void yyensure_buffer_stack (void)
{
	yy_size_t num_to_alloc;
    
	if (!(yy_buffer_stack)) {

		/* First allocation is just for 2 elements, since we don't know if this
		 * scanner will even need a stack. We use 2 instead of 1 to avoid an
		 * immediate realloc on the next call.
         */
      num_to_alloc = 1; /* After all that talk, this was set to 1 anyways... */
		(yy_buffer_stack) = (struct yy_buffer_state**)yyalloc
								(num_to_alloc * sizeof(struct yy_buffer_state*)
								);
		if ( ! (yy_buffer_stack) )
			YY_FATAL_ERROR( "out of dynamic memory in yyensure_buffer_stack()" );

		memset((yy_buffer_stack), 0, num_to_alloc * sizeof(struct yy_buffer_state*));

		(yy_buffer_stack_max) = num_to_alloc;
		(yy_buffer_stack_top) = 0;
		return;
	}

	if ((yy_buffer_stack_top) >= ((yy_buffer_stack_max)) - 1){

		/* Increase the buffer to prepare for a possible push. */
		yy_size_t grow_size = 8 /* arbitrary grow size */;

		num_to_alloc = (yy_buffer_stack_max) + grow_size;
		(yy_buffer_stack) = (struct yy_buffer_state**)yyrealloc
								((yy_buffer_stack),
								num_to_alloc * sizeof(struct yy_buffer_state*)
								);
		if ( ! (yy_buffer_stack) )
			YY_FATAL_ERROR( "out of dynamic memory in yyensure_buffer_stack()" );

		/* zero only the new slots.*/
		memset((yy_buffer_stack) + (yy_buffer_stack_max), 0, grow_size * sizeof(struct yy_buffer_state*));
		(yy_buffer_stack_max) = num_to_alloc;
	}
}

/** Setup the input buffer state to scan directly from a user-specified character buffer.
 * @param base the character buffer
 * @param size the size in bytes of the character buffer
 * 
 * @return the newly allocated buffer state object.
 */
YY_BUFFER_STATE yy_scan_buffer  (char * base, yy_size_t  size )
{
	YY_BUFFER_STATE b;
    
	if ( size < 2 ||
	     base[size-2] != YY_END_OF_BUFFER_CHAR ||
	     base[size-1] != YY_END_OF_BUFFER_CHAR )
		/* They forgot to leave room for the EOB's. */
		return NULL;

	b = (YY_BUFFER_STATE) yyalloc( sizeof( struct yy_buffer_state )  );
	if ( ! b )
		YY_FATAL_ERROR( "out of dynamic memory in yy_scan_buffer()" );

	b->yy_buf_size = (int) (size - 2);	/* "- 2" to take care of EOB's */
	b->yy_buf_pos = b->yy_ch_buf = base;
	b->yy_is_our_buffer = 0;
	b->yy_input_file = NULL;
	b->yy_n_chars = b->yy_buf_size;
	b->yy_is_interactive = 0;
	b->yy_at_bol = 1;
	b->yy_fill_buffer = 0;
	b->yy_buffer_status = YY_BUFFER_NEW;

	yy_switch_to_buffer( b  );

	return b;
}

/** Setup the input buffer state to scan a string. The next call to yylex() will
 * scan from a @e copy of @a str.
 * @param yystr a NUL-terminated string to scan
 * 
 * @return the newly allocated buffer state object.
 * @note If you want to scan bytes that may contain NUL values, then use
 *       yy_scan_bytes() instead.
 */
YY_BUFFER_STATE yy_scan_string (const char * yystr )
{
    
	return yy_scan_bytes( yystr, (int) strlen(yystr) );
}

/** Setup the input buffer state to scan the given bytes. The next call to yylex() will
 * scan from a @e copy of @a bytes.
 * @param yybytes the byte buffer to scan
 * @param _yybytes_len the number of bytes in the buffer pointed to by @a bytes.
 * 
 * @return the newly allocated buffer state object.
 */
YY_BUFFER_STATE yy_scan_bytes  (const char * yybytes, int  _yybytes_len )
{
	YY_BUFFER_STATE b;
	char *buf;
	yy_size_t n;
	int i;
    
	/* Get memory for full buffer, including space for trailing EOB's. */
	n = (yy_size_t) (_yybytes_len + 2);
	buf = (char *) yyalloc( n  );
	if ( ! buf )
		// YY_FATAL_ERROR( "out of dynamic memory in yy_scan_bytes()" );
		avrdude_oom("out of dynamic memory in yy_scan_bytes()");

	for ( i = 0; i < _yybytes_len; ++i )
		buf[i] = yybytes[i];

	buf[_yybytes_len] = buf[_yybytes_len+1] = YY_END_OF_BUFFER_CHAR;

	b = yy_scan_buffer( buf, n );
	if ( ! b )
		YY_FATAL_ERROR( "bad buffer in yy_scan_bytes()" );

	/* It's okay to grow etc. this buffer, and we should throw it
	 * away when we're done.
	 */
	b->yy_is_our_buffer = 1;

	return b;
}

#ifndef YY_EXIT_FAILURE
#define YY_EXIT_FAILURE 2
#endif

static void yynoreturn yy_fatal_error (const char* msg )
{
	fprintf( stderr, "%s\n", msg );
	// exit( YY_EXIT_FAILURE );
	abort();
}

/* Redefine yyless() so it works in section 3 code. */

#undef yyless
#define yyless(n) \
	do \
		{ \
		/* Undo effects of setting up yytext. */ \
        int yyless_macro_arg = (n); \
        YY_LESS_LINENO(yyless_macro_arg);\
		yytext[yyleng] = (yy_hold_char); \
		(yy_c_buf_p) = yytext + yyless_macro_arg; \
		(yy_hold_char) = *(yy_c_buf_p); \
		*(yy_c_buf_p) = '\0'; \
		yyleng = yyless_macro_arg; \
		} \
	while ( 0 )

/* Accessor  methods (get/set functions) to struct members. */

/** Get the current line number.
 * 
 */
int yyget_lineno  (void)
{
    
    return yylineno;
}

/** Get the input stream.
 * 
 */
FILE *yyget_in  (void)
{
        return yyin;
}

/** Get the output stream.
 * 
 */
FILE *yyget_out  (void)
{
        return yyout;
}

/** Get the length of the current token.
 * 
 */
int yyget_leng  (void)
{
        return yyleng;
}

/** Get the current token.
 * 
 */

char *yyget_text  (void)
{
        return yytext;
}

/** Set the current line number.
 * @param _line_number line number
 * 
 */
void yyset_lineno (int  _line_number )
{
    
    yylineno = _line_number;
}

/** Set the input stream. This does not discard the current
 * input buffer.
 * @param _in_str A readable stream.
 * 
 * @see yy_switch_to_buffer
 */
void yyset_in (FILE *  _in_str )
{
        yyin = _in_str ;
}

void yyset_out (FILE *  _out_str )
{
        yyout = _out_str ;
}

int yyget_debug  (void)
{
        return yy_flex_debug;
}

void yyset_debug (int  _bdebug )
{
        yy_flex_debug = _bdebug ;
}

static int yy_init_globals (void)
{
        /* Initialization is the same as for the non-reentrant scanner.
     * This function is called from yylex_destroy(), so don't allocate here.
     */

    (yy_buffer_stack) = NULL;
    (yy_buffer_stack_top) = 0;
    (yy_buffer_stack_max) = 0;
    (yy_c_buf_p) = NULL;
    (yy_init) = 0;
    (yy_start) = 0;

/* Defined in main.c */
#ifdef YY_STDINIT
    yyin = stdin;
    yyout = stdout;
#else
    yyin = NULL;
    yyout = NULL;
#endif

    /* For future reference: Set errno on error, since we are called by
     * yylex_init()
     */
    return 0;
}

/* yylex_destroy is for both reentrant and non-reentrant scanners. */
int yylex_destroy  (void)
{
    
    /* Pop the buffer stack, destroying each element. */
	while(YY_CURRENT_BUFFER){
		yy_delete_buffer( YY_CURRENT_BUFFER  );
		YY_CURRENT_BUFFER_LVALUE = NULL;
		yypop_buffer_state();
	}

	/* Destroy the stack itself. */
	yyfree((yy_buffer_stack) );
	(yy_buffer_stack) = NULL;

    /* Reset the globals. This is important in a non-reentrant scanner so the next time
     * yylex() is called, initialization will occur. */
    yy_init_globals( );

    return 0;
}

/*
 * Internal utility routines.
 */

#ifndef yytext_ptr
static void yy_flex_strncpy (char* s1, const char * s2, int n )
{
		
	int i;
	for ( i = 0; i < n; ++i )
		s1[i] = s2[i];
}
#endif

#ifdef YY_NEED_STRLEN
static int yy_flex_strlen (const char * s )
{
	int n;
	for ( n = 0; s[n]; ++n )
		;

	return n;
}
#endif

void *yyalloc (yy_size_t  size )
{
			return malloc(size);
}

void *yyrealloc  (void * ptr, yy_size_t  size )
{
		
	/* The cast to (char *) in the following accommodates both
	 * implementations that use char* generic pointers, and those
	 * that use void* generic pointers.  It works with the latter
	 * because both ANSI C and C++ allow castless assignment from
	 * any pointer type to void*, and deal with argument conversions
	 * as though doing an assignment.
	 */
	return realloc(ptr, size);
}

void yyfree (void * ptr )
{
			free( (char *) ptr );	/* see yyrealloc() for (char *) cast */
}

#define YYTABLES_NAME "yytables"

#line 261 "lexer.l"



