2010-12-17  <PERSON><PERSON> <<EMAIL>>

	* avrdude.conf.in (ATmega128RFA1): Bump two timing values in order to
	  improve ISP programming stability, in particular with the STK600.

2010-12-14  <PERSON><PERSON> <<EMAIL>>

	* stk500v2.c (stk500v2_command): Detect warning status codes.

2010-10-22  <PERSON>ls <PERSON>ob <<EMAIL>>

	* serial.h: serial_open() calls will now return -1 on error (no call to exit())
	* buspirate.c: (Dito.)
	* jtagmkII.c: (Dito.)
	* butterfly.c: (Dito.)
	* jtagmkI.c: (Dito.)
	* arduino.c: (Dito.)
	* avr910.c: (Dito.)
	* stk500.c: (Dito.)
	* ser_avrdoper.c: (Dito.)
	* stk500v2.c: (Dito.)
	* ser_posix.c: (Dito.)
	* usb_libusb.c: (Dito.)

2010-07-27  <PERSON><PERSON> <<EMAIL>>

	bug #30566: MinGW + Ubuntu 9.04
	* stk500v2.c (stk500v2_open): use same condition to refer to the AVR
	Doper support as used in the definition in ser_avrdoper.c.
	(Thanks to Christian Starkjohann for the analysis of the problem.)

2010-07-19  Michal Ludvig <<EMAIL>>

	* buspirate.c: Added compatibility with BusPirate "NewUI" firmware 5.x
	  (contributed by Kari Knuuttila)

2010-07-12  Nils Springob <<EMAIL>>

	* avrdude.conf.in (atmega88p): New device.

2010-06-03  Joerg Wunsch <<EMAIL>>

	bug #29913: 246 Byte Bug - AVRdude crashes
	doc/avrdude.texi (Troubleshooting): Mention the libusb 0.1 API
	wrapper issue that is present in some Linux versions.

2010-03-19  Joerg Wunsch <<EMAIL>>

	bug #29263: Can't build avrdude on windows using latest cygwin 1.7.1
	* doc/avrdude.texi: Remove the recommendation for building
	Win32 binaries under Cygwin; mention MinGW as an alternative
	environment.

2010-03-08  Michal Ludvig <<EMAIL>>

	* ser_posix.c(ser_set_dtr_rts): Fixed DTR on/off to make
	  Arduino auto-reset work. (bug #29108, patch #7100)

2010-03-05  Joerg Wunsch <<EMAIL>>

	* buspirate.c: Replace printf() by fprintf(stderr)
	* safemode.c: (Dito.)
	* usbtiny.c: (Dito.)

2010-01-22  Joerg Wunsch <<EMAIL>>

	Cleanup Cygwin builds.
	* windows/Makefile.am (loaddrv_LDFLAGS): remove, the -mno-cygwin
	flag is supposed to be set in CFLAGS by ./configure
	* configure.ac: add a check for the presence of usleep(), add a
	check whether the linker accepts -static
	* avrdude.h: protect prototype for usleep by !defined(HAVE_USLEEP)
	* ppwin.c (usleep): protect by !defined(HAVE_USLEEP)
	* main.c: silence "array subscript of type char" compiler warnings
	by casting all arguments to tolower()/toupper() and isspace()/
	isdigit()/ispunct() to "int"
	* butterfly.c: (Dito.)
	* avr910.c: (Dito.)

2010-01-19  Joerg Wunsch <<EMAIL>>

	* configure.ac: Bump for post-5.10.

2010-01-19  Joerg Wunsch <<EMAIL>>

	* configure.ac: Released version 5.10.

2010-01-19  Joerg Wunsch <<EMAIL>>

	bug #28677: Cygwin's GCC no longer supports -mno-cygwin option
	* configure.ac: For Win32 environments, add a check whether the
	compiler understands the -mno-cygwin option.  If not, don't use
	it but suggest using a different compiler.

2010-01-18  David Hoerl <<EMAIL>>

	bug #28660: Problem with loading intel hex rom files that exceed
	0x10000 bytes
	* fileio.c: Fix two byte shifts.

2010-01-15  Joerg Wunsch <<EMAIL>>

	Submitted by Michael biebl:
	* configure.ac: Fix FreeBSD default serial port name.
	* doc/avrdude.texi: (Dito.)

2010-01-15  Joerg Wunsch <<EMAIL>>

	* jtagmkII.c: If entering JTAG mode fails with a bad JTAG ID
	message, retry with external reset applied (in case the target
	is in sleep mode or has asserted the JTD bit).

2010-01-15  Joerg Wunsch <<EMAIL>>

	Submitted by Aurelien Jarno:
	* configure.ac: Fix build for GNU/kFreeBSD.
	* ppi.c: (Dito.)
	* par.c: (Dito.)

2010-01-15  Joerg Wunsch <<EMAIL>>

	* configure.ac: Bump version for post-5.8.

2010-01-15  Joerg Wunsch <<EMAIL>>

	* configure.ac: Bump version for release 5.8.

2010-01-15  Joerg Wunsch <<EMAIL>>

	Submitted by Soren Jorvang:
	bug #28611: -i delay not being applied to all serial port
	bit banging state transitions
	* serbb_win32.c: Apply ispdelay everywhere.
	* serbb_posix.c: (Dito.)

2010-01-15  Joerg Wunsch <<EMAIL>>

	* stk500v2_private.h: Implement TPI mode for AVRISPmkII/STK600
	* config_gram.y: (Dito.)
	* avrpart.h: (Dito.)
	* stk500v2.c: (Dito.)
	* main.c: (Dito.)
	* lexer.l: (Dito.)
	* avrdude.conf.in: Add ATtiny4/5/9/10
	* avrdude.1: Document TPI and new device support.
	* doc/avrdude.texi: (Dito.)

2010-01-14  Joerg Wunsch <<EMAIL>>

	Submitted by clint fisher:
	patch #7038: Adding Atmega32U4 Device to avrdude.conf.in
	* avrdude.conf.in (atmega32u4): New device.
	* avrdude.1: Document the new device support.
	* doc/avrdude.texi: (Dito.)

2010-01-14  Joerg Wunsch <<EMAIL>>

	Submitted by Thomas Pircher:
	patch #6927: Documentation patches
	* doc/avrdude.texi: Fix various typos, and remove the last
	remnants of obsoleted options -i/-o/-m/-f.
	* avrdude.1: Merge typo fixes from avrdude.texi where
	applicable.

2010-01-14  Joerg Wunsch <<EMAIL>>

	* avrdude.1: Update documentation to match the reality (device
	support, memory areas).
	* doc/avrdude.texi: Update documentation to match the
	reality (device support, programmer support, memory areas).
	Merge buspirate-specific comments from avrdude.1.
	* jtagmkII.c: Add some firmware feature checks.

2010-01-13  Joerg Wunsch <<EMAIL>>

	* jtagmkII.c: Implement PDI mode support for the JTAG ICE mkII
	and the AVR Dragon.
	* jtagmkII.h: (Dito.)
	* config_gram.y: (Dito.)
	* jtagmkII_private.h: (Dito.)
	* avrdude.conf.in: (Dito.)
	* lexer.l: (Dito.)

2010-01-13  Joerg Wunsch <<EMAIL>>

	* stk500v2.c: Update STK600 routing and socket card data from XML
	file.

2010-01-13  Joerg Wunsch <<EMAIL>>

	* stk500v2.c: Cleanup the open/close handling to avoid accessing
	unallocated memory (in the atexit handler) in case of bailing out.
	* main.c: (Ditto.)

2010-01-13  Joerg Wunsch <<EMAIL>>

	* jtagmkII.c: Stylistic changes: move #defines out into
	jtagmkII_private.h, drop all #if 0 blocks, fold overly long lines,
	move the *_initpgm() functions to the end of the file; while being
	here, remove all trailing whitespace.
	* jtagmkII_private.h: move AVR32 #defines here.

2010-01-12  Joerg Wunsch <<EMAIL>>

	* bootstrap: autoconf 2.62 works well.

2010-01-12  Joerg Wunsch <<EMAIL>>

	Various fixes for Xmega devices.
	* avrdude.conf.in: Correctly declare EEPROM page sizes for
	all Xmega devices (0x20 instead of 0x100).
	* avr.c: If a memory region has a page size declared, try
	using the paged IO routines regardless of the target memory
	name.  Xmega EEPROM requires to be written in paged mode.
	Correctly use a long (rather than unsigned long) variable to
	evaluate the success status of the paged mode write attempt.
	* stk500v2.c: Don't apply TIF space offsets twice (bug #27995:
	AVRDUDE 5.8svn fails to program and read XMEGA); use
	stk500v2_loadaddr() prior to paged mode (EEPROM and flash) writes,
	otherwise programming of flash areas will fail; while being there,
	check the return value of stk500v2_loadaddr() everywhere; use the
	correct write/erase mode bits (same as AVR Studio does).

2010-01-12  Michal Ludvig  <<EMAIL>>

	* buspirate.c: Initialise firmware version to v0.0
	prior to parsing the buspirate banner.

2010-01-11  Joerg Wunsch <<EMAIL>>

	Clean-up the Xmega erase functions.
	* jtagmkII_private.h: Add CMND_XMEGA_ERASE as well as
	the various XMEGA_ERASE_* definitions (from updated
	appnote AVR067)
	* jtagmkII.c (jtagmkII_chip_erase): Correctly implement Xmega chip
	erase based on CMND_XMEGA_ERASE.  After erasing an Xmega part, do
	*not* reinitialize the world, as a subsequent programming
	operation will fail (for unknown reasons).  Actually, this was
	really only required for ancient AVRs, but doesn't hurt on mega
	and tiny devices.
	* jtagmkII.c (jtagmkII_pre_write): Remove, this turned out
	to be just a chip erase.
	* jtagmkII.c (jtagmkII_program_disable): Don't try reading
	"hfuse" for Xmega parts; they don't have it.
	* main.c (main): Re-enable auto-erase.  It's been done
	before (as "jtagmkII_pre_write") in jtagmkII_paged_write()
	anyway.  Xmega boot and application flash areas should be
	handled separately in the future, so auto_erase can only
	affect the area just being programmed.

2010-01-11  Joerg Wunsch <<EMAIL>>

	* main.c (main): disable safemode for Xmega parts.

2010-01-12  Michal Ludvig  <<EMAIL>>

	* buspirate.c: If the BusPirate doesn't respond
	to a standard a reset command assume it was in binmode
	and attempt to exit to text mode first.

2010-01-08  Joerg Wunsch <<EMAIL>>

	* bitbang.c: Fix Win32 build error: move freq up to the file
	level.
	* buspirate.c: Fix Win32 build warning: include <malloc.h> to
	to get a declaration for alloca().

2010-01-08  Thomas Fischl <<EMAIL>>

	bug #28520: Programming with USBasp with low clock speed fails
	* usbasp.c: Change blocksize depending on sck frequency to
	avoid usb transmition timeouts.

2010-01-08  Joerg Wunsch <<EMAIL>>

	bug #27505: serbb_posix does not cope with inverted pins
	* serbb_posix (serbb_highpulsepin): apply PIN_MASK when
	checking pin numbers.
	* serbb_win32 (serbb_highpulsepin): (Dito.)

2010-01-08  Joerg Wunsch <<EMAIL>>

	bug #28516: Linux/Dragon: Error message on exit
	* stk500v2.c: Fix the "bad response to GO command:
	RSP_ILLEGAL_EMULATOR_MODE" message.  jtagmkII_close()
	has been called with the wrong pgm->cookie.  Wrap it
	inside stk500v2_jtagmkII_close(), adjusting the cookie
	data appropriately.

2010-01-08  Joerg Wunsch <<EMAIL>>

	Submitted by Doug:
	patch #7010: Win32 enhanced bitbang_delay
	* bitbang.c (bitbang_calibrate_delay, bitbang_delay): On Win32,
	use the high-resolution performance counter rather than the
	uneducated delay loop guess if it is available on the target
	hardware.

2010-01-08  Joerg Wunsch <<EMAIL>>

	Submitted by Gerard:
	patch #6828: Using arbitrary BAUD rates
	* ser_posix.c (serial_baud_lookup): Allow non-standard baud
	rates.
	* ser_win32.c (serial_baud_lookup): (Dito.)

2010-01-07  Joerg Wunsch <<EMAIL>>

	Submitted by Eric Trein:
	bug #27596: AT90s2333 is not correctly supported in avrdude.conf
	* avrdude.conf.in (at90s2333): add various STK500v2 parameters.

2010-01-07  Joerg Wunsch <<EMAIL>>

	Submitted by Gyorgy Szekely:
	bug #28458: Buffer line is incorrectly released for PP programmers
	* par.c (par_close): use par_setmany() rather than par_setpin()
	for PPI_AVR_BUFF.

2010-01-07  Joerg Wunsch <<EMAIL>>

	Submitted by Lukasz Goralczyk:
	bug #27507: SIGSEGV when using avrdragon (avrdude 5.8)
	* stk500v2.c (stk500v2_dragon_isp_initpgm): Use
	stk500v2_jtagmkII_setup/stk500v2_jtagmkII_rather than their
	jtagII counterparts, to get the private data properly
	initialized.

2010-01-07  Joerg Wunsch <<EMAIL>>

	* buspirate.c: Cosmetics: remove UTF-8 dashes, adjust for 8-column
	hard tabs.

2010-01-07  Joerg Wunsch <<EMAIL>>

	* buspirate.c: add $ Id $ line.
	* buspirate.h: add $ Id $ line.

2010-01-07  Joerg Wunsch <<EMAIL>>

	Fix a few warnings that came up recently (some of them only triggered
	by recent GCC versions).
	* config_gram.y (parse_cmdbits): "brkt possibly used uninitialized"
	(GCC errs here)
	* jtagmkII.c (jtagmkII_reset32): "status possibly used uninitialized"
	(I think GCC errs, too)
	* buspirate.c: "pointers differ in signedness" (mismatch between
	string processing and the use of "unsigned char" throughought the
	AVRDUDE API)

2010-01-01  Joerg Wunsch <<EMAIL>>

	* jtagmkII.c (jtagmkII_smc_init32): replace sleep() by usleep() for
	win32 compatibility.
