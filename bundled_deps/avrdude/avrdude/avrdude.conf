# $Id$ -*- text -*-
#
# AVRDUDE Configuration File
#
# This file contains configuration data used by AVRDUDE which describes
# the programming hardware pinouts and also provides part definitions.
# AVRDUDE's "-C" command line option specifies the location of the
# configuration file.  The "-c" option names the programmer configuration
# which must match one of the entry's "id" parameter.  The "-p" option
# identifies which part AVRDUDE is going to be programming and must match
# one of the parts' "id" parameter.
#
# DO NOT MODIFY THIS FILE.  Modifications will be overwritten the next
# time a "make install" is run.  For user-specific additions, use the
# "-C +filename" commandline option.
#
# Possible entry formats are:
#
#   programmer
#       parent <id>                                 # optional parent
#       id       = <id1> [, <id2> [, <id3>] ...] ;  # <idN> are quoted strings
#       desc     = <description> ;                  # quoted string
#       type     = <type>;                          # programmer type, quoted string
#                          # supported programmer types can be listed by "-c ?type"
#       connection_type = parallel | serial | usb
#       baudrate = <num> ;                          # baudrate for avr910-programmer
#       vcc      = <num1> [, <num2> ... ] ;         # pin number(s)
#       buff     = <num1> [, <num2> ... ] ;         # pin number(s)
#       reset    = <num> ;                          # pin number
#       sck      = <num> ;                          # pin number
#       mosi     = <num> ;                          # pin number
#       miso     = <num> ;                          # pin number
#       errled   = <num> ;                          # pin number
#       rdyled   = <num> ;                          # pin number
#       pgmled   = <num> ;                          # pin number
#       vfyled   = <num> ;                          # pin number
#       usbvid   = <hexnum>;                        # USB VID (Vendor ID)
#       usbpid   = <hexnum> [, <hexnum> ...]        # USB PID (Product ID) (1)
#       usbdev   = <interface>;                     # USB interface or other device info 
#       usbvendor = <vendorname>;                   # USB Vendor Name
#       usbproduct = <productname>;                 # USB Product Name
#       usbsn    = <serialno>;                      # USB Serial Number
#
#        To invert a bit, use = ~ <num>, the spaces are important.
#        For a pin list all pins must be inverted.
#        A single pin can be specified as usual = ~ <num>, for lists
#        specify it as follows = ~ ( <num> [, <num2> ... ] ) .
#
#        (1) Not all programmer types can process a list of PIDs.
#     ;
#
#   part
#       id               = <id> ;                 # quoted string
#       desc             = <description> ;        # quoted string
#       has_jtag         = <yes/no> ;             # part has JTAG i/f
#       has_debugwire    = <yes/no> ;             # part has debugWire i/f
#       has_pdi          = <yes/no> ;             # part has PDI i/f
#       has_tpi          = <yes/no> ;             # part has TPI i/f
#       devicecode       = <num> ;            # deprecated, use stk500_devcode
#       stk500_devcode   = <num> ;                # numeric
#       avr910_devcode   = <num> ;                # numeric
#       signature        = <num> <num> <num> ;    # signature bytes
#       usbpid           = <num> ;                # DFU USB PID
#       chip_erase_delay = <num> ;                # micro-seconds
#       reset            = dedicated | io;
#       retry_pulse      = reset | sck;
#       pgm_enable       = <instruction format> ;
#       chip_erase       = <instruction format> ;
#       chip_erase_delay = <num> ;                # chip erase delay (us)
#       # STK500 parameters (parallel programming IO lines)
#       pagel            = <num> ;                # pin name in hex, i.e., 0xD7
#       bs2              = <num> ;                # pin name in hex, i.e., 0xA0
#       serial           = <yes/no> ;             # can use serial downloading
#       parallel         = <yes/no/pseudo>;       # can use par. programming
#       # STK500v2 parameters, to be taken from Atmel's XML files
#       timeout          = <num> ;
#       stabdelay        = <num> ;
#       cmdexedelay      = <num> ;
#       synchloops       = <num> ;
#       bytedelay        = <num> ;
#       pollvalue        = <num> ;
#       pollindex        = <num> ;
#       predelay         = <num> ;
#       postdelay        = <num> ;
#       pollmethod       = <num> ;
#       mode             = <num> ;
#       delay            = <num> ;
#       blocksize        = <num> ;
#       readsize         = <num> ;
#       hvspcmdexedelay  = <num> ;
#       # STK500v2 HV programming parameters, from XML
#       pp_controlstack  = <num>, <num>, ...;   # PP only
#       hvsp_controlstack = <num>, <num>, ...;  # HVSP only
#       hventerstabdelay = <num>;
#       progmodedelay    = <num>;               # PP only
#       latchcycles      = <num>;
#       togglevtg        = <num>;
#       poweroffdelay    = <num>;
#       resetdelayms     = <num>;
#       resetdelayus     = <num>;
#       hvleavestabdelay = <num>;
#       resetdelay       = <num>;
#       synchcycles      = <num>;               # HVSP only
#       chiperasepulsewidth = <num>;            # PP only
#       chiperasepolltimeout = <num>;
#       chiperasetime    = <num>;               # HVSP only
#       programfusepulsewidth = <num>;          # PP only
#       programfusepolltimeout = <num>;
#       programlockpulsewidth = <num>;          # PP only
#       programlockpolltimeout = <num>;
#       # JTAG ICE mkII parameters, also from XML files
#       allowfullpagebitstream = <yes/no> ;
#       enablepageprogramming = <yes/no> ;
#       idr              = <num> ;                # IO addr of IDR (OCD) reg.
#       rampz            = <num> ;                # IO addr of RAMPZ reg.
#       spmcr            = <num> ;                # mem addr of SPMC[S]R reg.
#       eecr             = <num> ;                # mem addr of EECR reg.
#                                                 # (only when != 0x3c)
#       is_at90s1200     = <yes/no> ;             # AT90S1200 part
#       is_avr32         = <yes/no> ;             # AVR32 part
#
#       memory <memtype>
#           paged           = <yes/no> ;          # yes / no
#           size            = <num> ;             # bytes
#           page_size       = <num> ;             # bytes
#           num_pages       = <num> ;             # numeric
#           min_write_delay = <num> ;             # micro-seconds
#           max_write_delay = <num> ;             # micro-seconds
#           readback_p1     = <num> ;             # byte value
#           readback_p2     = <num> ;             # byte value
#           pwroff_after_write = <yes/no> ;       # yes / no
#           read            = <instruction format> ;
#           write           = <instruction format> ;
#           read_lo         = <instruction format> ;
#           read_hi         = <instruction format> ;
#           write_lo        = <instruction format> ;
#           write_hi        = <instruction format> ;
#           loadpage_lo     = <instruction format> ;
#           loadpage_hi     = <instruction format> ;
#           writepage       = <instruction format> ;
#         ;
#     ;
#
# If any of the above parameters are not specified, the default value
# of 0 is used for numerics or the empty string ("") for string
# values.  If a required parameter is left empty, AVRDUDE will
# complain.
#
# Parts can also inherit parameters from previously defined parts
# using the following syntax. In this case specified integer and 
# string values override parameter values from the parent part. New 
# memory definitions are added to the definitions inherited from the 
# parent.
#
#   part parent <id>                              # quoted string
#       id               = <id> ;                 # quoted string
#       <any set of other parameters from the list above>
#     ;
#
# NOTES:
#   * 'devicecode' is the device code used by the STK500 (see codes 
#       listed below)
#   * Not all memory types will implement all instructions.
#   * AVR Fuse bits and Lock bits are implemented as a type of memory.
#   * Example memory types are:
#       "flash", "eeprom", "fuse", "lfuse" (low fuse), "hfuse" (high
#       fuse), "signature", "calibration", "lock"
#   * The memory type specified on the avrdude command line must match
#     one of the memory types defined for the specified chip.
#   * The pwroff_after_write flag causes avrdude to attempt to
#     power the device off and back on after an unsuccessful write to
#     the affected memory area if VCC programmer pins are defined.  If
#     VCC pins are not defined for the programmer, a message
#     indicating that the device needs a power-cycle is printed out.
#     This flag was added to work around a problem with the
#     at90s4433/2333's; see the at90s4433 errata at:
#
#         http://www.atmel.com/dyn/resources/prod_documents/doc1280.pdf
#
# INSTRUCTION FORMATS
#
#    Instruction formats are specified as a comma seperated list of
#    string values containing information (bit specifiers) about each
#    of the 32 bits of the instruction.  Bit specifiers may be one of
#    the following formats:
#
#       '1'  = the bit is always set on input as well as output
#
#       '0'  = the bit is always clear on input as well as output
#
#       'x'  = the bit is ignored on input and output
#
#       'a'  = the bit is an address bit, the bit-number matches this bit
#              specifier's position within the current instruction byte
#
#       'aN' = the bit is the Nth address bit, bit-number = N, i.e., a12
#              is address bit 12 on input, a0 is address bit 0.
#
#       'i'  = the bit is an input data bit
#
#       'o'  = the bit is an output data bit
#
#    Each instruction must be composed of 32 bit specifiers.  The
#    instruction specification closely follows the instruction data
#    provided in Atmel's data sheets for their parts.
#
# See below for some examples.
#
#
# The following are STK500 part device codes to use for the
# "devicecode" field of the part.  These came from Atmel's software
# section avr061.zip which accompanies the application note
# AVR061 available from:
#
#      http://www.atmel.com/dyn/resources/prod_documents/doc2525.pdf
#

#define ATTINY10    0x10  /* the _old_ one that never existed! */
#define ATTINY11    0x11
#define ATTINY12    0x12
#define ATTINY15    0x13
#define ATTINY13    0x14

#define ATTINY22    0x20
#define ATTINY26    0x21
#define ATTINY28    0x22
#define ATTINY2313  0x23

#define AT90S1200   0x33

#define AT90S2313   0x40
#define AT90S2323   0x41
#define AT90S2333   0x42
#define AT90S2343   0x43

#define AT90S4414   0x50
#define AT90S4433   0x51
#define AT90S4434   0x52
#define ATMEGA48    0x59

#define AT90S8515   0x60
#define AT90S8535   0x61
#define AT90C8534   0x62
#define ATMEGA8515  0x63
#define ATMEGA8535  0x64

#define ATMEGA8     0x70
#define ATMEGA88    0x73
#define ATMEGA168   0x86

#define ATMEGA161   0x80
#define ATMEGA163   0x81
#define ATMEGA16    0x82
#define ATMEGA162   0x83
#define ATMEGA169   0x84

#define ATMEGA323   0x90
#define ATMEGA32    0x91

#define ATMEGA64    0xA0

#define ATMEGA103   0xB1
#define ATMEGA128   0xB2
#define AT90CAN128  0xB3
#define AT90CAN64   0xB3
#define AT90CAN32   0xB3

#define AT86RF401   0xD0

#define AT89START   0xE0
#define AT89S51	    0xE0
#define AT89S52	    0xE1

# The following table lists the devices in the original AVR910
# appnote:
# |Device |Signature | Code |
# +-------+----------+------+
# |tiny12 | 1E 90 05 | 0x55 |
# |tiny15 | 1E 90 06 | 0x56 |
# |       |          |      |
# | S1200 | 1E 90 01 | 0x13 |
# |       |          |      |
# | S2313 | 1E 91 01 | 0x20 |
# | S2323 | 1E 91 02 | 0x48 |
# | S2333 | 1E 91 05 | 0x34 |
# | S2343 | 1E 91 03 | 0x4C |
# |       |          |      |
# | S4414 | 1E 92 01 | 0x28 |
# | S4433 | 1E 92 03 | 0x30 |
# | S4434 | 1E 92 02 | 0x6C |
# |       |          |      |
# | S8515 | 1E 93 01 | 0x38 |
# | S8535 | 1E 93 03 | 0x68 |
# |       |          |      |
# |mega32 | 1E 95 01 | 0x72 |
# |mega83 | 1E 93 05 | 0x65 |
# |mega103| 1E 97 01 | 0x41 |
# |mega161| 1E 94 01 | 0x60 |
# |mega163| 1E 94 02 | 0x64 |

# Appnote AVR109 also has a table of AVR910 device codes, which
# lists:
# dev         avr910   signature
# ATmega8     0x77     0x1E 0x93 0x07
# ATmega8515  0x3B     0x1E 0x93 0x06
# ATmega8535  0x6A     0x1E 0x93 0x08
# ATmega16    0x75     0x1E 0x94 0x03
# ATmega162   0x63     0x1E 0x94 0x04
# ATmega163   0x66     0x1E 0x94 0x02
# ATmega169   0x79     0x1E 0x94 0x05
# ATmega32    0x7F     0x1E 0x95 0x02
# ATmega323   0x73     0x1E 0x95 0x01
# ATmega64    0x46     0x1E 0x96 0x02
# ATmega128   0x44     0x1E 0x97 0x02
#
# These codes refer to "BOOT" device codes which are apparently
# different than standard device codes, for whatever reasons
# (often one above the standard code).

# There are several extended versions of AVR910 implementations around
# in the Internet.  These add the following codes (only devices that
# actually exist are listed):

# ATmega8515	0x3A
# ATmega128	0x43
# ATmega64	0x45
# ATtiny26	0x5E
# ATmega8535	0x69
# ATmega32	0x72
# ATmega16	0x74
# ATmega8	0x76
# ATmega169	0x78

#
# Overall avrdude defaults; suitable for ~/.avrduderc
#
default_parallel   = "/dev/parport0";
default_serial     = "/dev/ttyS0";
# default_bitclock = 2.5;

# Turn off safemode by default
#default_safemode  = no;


#
# PROGRAMMER DEFINITIONS
#

# http://wiring.org.co/
# Basically STK500v2 protocol, with some glue to trigger the
# bootloader.
programmer
  id    = "wiring";
  desc  = "Wiring";
  type  = "wiring";
  connection_type = serial;
;

programmer
  id    = "arduino";
  desc  = "Arduino";
  type  = "arduino";
  connection_type = serial;
;
# this will interface with the chips on these programmers:
#
# http://real.kiev.ua/old/avreal/en/adapters
# http://www.amontec.com/jtagkey.shtml, jtagkey-tiny.shtml
# http://www.olimex.com/dev/arm-usb-ocd.html, arm-usb-tiny.html
# http://www.ethernut.de/en/hardware/turtelizer/index.html
# http://elk.informatik.fh-augsburg.de/hhweb/doc/openocd/usbjtag/usbjtag.html
# http://dangerousprototypes.com/docs/FT2232_breakout_board
# http://www.ftdichip.com/Products/Modules/DLPModules.htm,DLP-2232*,DLP-USB1232H
# http://flashrom.org/FT2232SPI_Programmer
# 
# The drivers will look for a specific device and use the first one found.
# If you have mulitple devices, then look for unique information (like SN)
# And fill that in here.
#
# Note that the pin numbers for the main ISP signals (reset, sck,
# mosi, miso) are fixed and cannot be changed, since they must match
# the way the Multi-Protocol Synchronous Serial Engine (MPSSE) of
# these FTDI ICs has been designed.

programmer
  id         = "avrftdi";
  desc       = "FT2232D based generic programmer";
  type       = "avrftdi";
  connection_type = usb;
  usbvid     = 0x0403;
  usbpid     = 0x6010;
  usbvendor  = "";
  usbproduct = "";
  usbdev     = "A";
  usbsn      = "";
#ISP-signals - lower ADBUS-Nibble (default)
  reset  = 3;
  sck    = 0;
  mosi   = 1;
  miso   = 2;
#LED SIGNALs - higher ADBUS-Nibble
#  errled = 4;
#  rdyled = 5;
#  pgmled = 6;
#  vfyled = 7;
#Buffer Signal - ACBUS - Nibble
#  buff   = 8;
;
# This is an implementation of the above with a buffer IC (74AC244) and
# 4 LEDs directly attached, all active low.
programmer
  id         = "2232HIO";
  desc       = "FT2232H based generic programmer";
  type       = "avrftdi";
  connection_type = usb;
  usbvid     = 0x0403;
# Note: This PID is reserved for generic H devices and 
# should be programmed into the EEPROM
#  usbpid     = 0x8A48;
  usbpid     = 0x6010;
  usbdev     = "A";
  usbvendor  = "";
  usbproduct = "";
  usbsn      = "";
#ISP-signals 
  reset  = 3;
  sck    = 0;
  mosi   = 1;
  miso   = 2;
  buff   = ~4;
#LED SIGNALs 
  errled = ~ 11;
  rdyled = ~ 14;
  pgmled = ~ 13;
  vfyled = ~ 12;
;

#The FT4232H can be treated as FT2232H, but it has a different USB
#device ID of 0x6011.
programmer parent "avrftdi"
  id         = "4232h";
  desc       = "FT4232H based generic programmer";
  usbpid     = 0x6011;
;

programmer
  id         = "jtagkey";
  desc       = "Amontec JTAGKey, JTAGKey-Tiny and JTAGKey2";
  type       = "avrftdi";
  connection_type = usb;
  usbvid     = 0x0403;
# Note: This PID is used in all JTAGKey variants
  usbpid     = 0xCFF8;
  usbdev     = "A";
  usbvendor  = "";
  usbproduct = "";
  usbsn      = "";
#ISP-signals => 20 - Pin connector on JTAGKey
  reset  = 3; # TMS 7 violet
  sck    = 0; # TCK 9 white
  mosi   = 1; # TDI 5 green
  miso   = 2; # TDO 13 orange
  buff   = ~4;
# VTG           VREF 1 brown with red tip
# GND           GND 20 black
# The colors are on the 20 pin breakout cable
# from Amontec
;

# UM232H module from FTDI and Glyn.com.au.
# See helix.air.net.au for detailed usage information.
# J1: Connect pin 2 and 3 for USB power.
# J2: Connect pin 2 and 3 for USB power.
# J2: Pin 7 is SCK
#   : Pin 8 is MOSI
#   : Pin 9 is MISO
#   : Pin 11 is RST
#   : Pin 6 is ground
# Use the -b flag to set the SPI clock rate eg -b 3750000 is the fastest I could get
# a 16MHz Atmega1280 to program reliably.  The 232H is conveniently 5V tolerant.
programmer
  id         = "UM232H";
  desc       = "FT232H based module from FTDI and Glyn.com.au";
  type       = "avrftdi";
  usbvid     = 0x0403;
# Note: This PID is reserved for generic 232H devices and
# should be programmed into the EEPROM
  usbpid     = 0x6014;
  usbdev     = "A";
  usbvendor  = "";
  usbproduct = "";
  usbsn      = "";
#ISP-signals
  sck    = 0;
  mosi   = 1;
  miso   = 2;
  reset  = 3;
;

# C232HM module from FTDI and Glyn.com.au.
# : Orange is SCK
# : Yellow is MOSI
# : Green is MISO
# : Brown is RST
# : Black is ground
# Use the -b flag to set the SPI clock rate eg -b 3750000 is the fastest I could get
# a 16MHz Atmega1280 to program reliably.  The 232H is conveniently 5V tolerant.
programmer
  id         = "C232HM";
  desc       = "FT232H based module from FTDI and Glyn.com.au";
  type       = "avrftdi";
  usbvid     = 0x0403;
# Note: This PID is reserved for generic 232H devices and
# should be programmed into the EEPROM
  usbpid     = 0x6014;
  usbdev     = "A";
  usbvendor  = "";
  usbproduct = "";
  usbsn      = "";
#ISP-signals
  sck    = 0;
  mosi   = 1;
  miso   = 2;
  reset  = 3;
;


# On the adapter you can read "O-Link". On the PCB is printed "OpenJTAG v3.1"
# You can find it as "OpenJTAG ARM JTAG USB" in the internet. 
# (But there are also several projects called Open JTAG, eg. 
# http://www.openjtag.org, which are completely different.)
#   http://www.100ask.net/shop/english.html (website seems to be outdated)
#   http://item.taobao.com/item.htm?id=1559277013
#   http://www.micro4you.com/store/openjtag-arm-jtag-usb.html (schematics!)
# some other sources which call it O-Link
#   http://www.andahammer.com/olink/
#   http://www.developmentboard.net/31-o-link-debugger.html
#   http://armwerks.com/catalog/o-link-debugger-copy/
# or just have a look at ebay ...
# It is basically the same entry as jtagkey with different usb ids.
programmer parent "jtagkey"
  id         = "o-link";
  desc       = "O-Link, OpenJTAG from www.100ask.net";
  usbvid     = 0x1457;
  usbpid     = 0x5118;
  usbvendor  = "www.100ask.net";
  usbproduct = "USB<=>JTAG&RS232";
;

# http://wiki.openmoko.org/wiki/Debug_Board_v3
programmer
  id    = "openmoko";
  desc  = "Openmoko debug board (v3)";
  type  = "avrftdi";
  usbvid     = 0x1457;
  usbpid    = 0x5118;
  usbdev = "A";
  usbvendor  = "";
  usbproduct = "";
  usbsn      = "";
  reset  = 3; # TMS 7
  sck    = 0; # TCK 9
  mosi   = 1; # TDI 5
  miso   = 2; # TDO 13
;

# Only Rev. A boards.
# Schematic and user manual: http://www.cs.put.poznan.pl/wswitala/download/pdf/811EVBK.pdf
programmer
  id         = "lm3s811";
  desc       = "Luminary Micro LM3S811 Eval Board (Rev. A)";
  type       = "avrftdi";
  connection_type = usb;
  usbvid     = 0x0403;
  usbpid     = 0xbcd9;
  usbvendor  = "LMI";
  usbproduct = "LM3S811 Evaluation Board";
  usbdev     = "A";
  usbsn      = "";
#ISP-signals - lower ACBUS-Nibble (default)
  reset  = 3;
  sck    = 0;
  mosi   = 1;
  miso   = 2;
# Enable correct buffers
  buff   = 7;
;

# submitted as bug #46020
programmer
  id     = "tumpa";
  desc   = "TIAO USB Multi-Protocol Adapter";
  type   = "avrftdi";
  connection_type = usb;
  usbvid = 0x0403;
  usbpid = 0x8A98;
  usbdev = "A";
  usbvendor = "TIAO";
  usbproduct = "";
  usbsn  = "";
  sck    = 0; # TCK 9
  mosi   = 1; # TDI 5
  miso   = 2; # TDO 13
  reset  = 3; # TMS 7
;

programmer
  id    = "avrisp";
  desc  = "Atmel AVR ISP";
  type  = "stk500";
  connection_type = serial;
;

programmer
  id    = "avrispv2";
  desc  = "Atmel AVR ISP V2";
  type  =  "stk500v2";
  connection_type = serial;
;

programmer
  id    = "avrispmkII";
  desc  = "Atmel AVR ISP mkII";
  type  =  "stk500v2";
  connection_type = usb;
;

programmer parent "avrispmkII"
  id    = "avrisp2";
;

programmer
  id    = "buspirate";
  desc  = "The Bus Pirate";
  type  = "buspirate";
  connection_type = serial;
;

programmer
  id    = "buspirate_bb";
  desc  = "The Bus Pirate (bitbang interface, supports TPI)";
  type  = "buspirate_bb";
  connection_type = serial;
  # pins are bits in bitbang byte (numbers are 87654321)
  # 1|POWER|PULLUP|AUX|MOSI|CLK|MISO|CS
  reset  = 1;
  sck    = 3;
  mosi   = 4;
  miso   = 2;
  #vcc    = 7; This is internally set independent of this setting.
;

# This is supposed to be the "default" STK500 entry.
# Attempts to select the correct firmware version
# by probing for it.  Better use one of the entries
# below instead.
programmer
  id    = "stk500";
  desc  = "Atmel STK500";
  type  = "stk500generic";
  connection_type = serial;
;

programmer
  id    = "stk500v1";
  desc  = "Atmel STK500 Version 1.x firmware";
  type  = "stk500";
  connection_type = serial;
;

programmer
  id    = "mib510";
  desc  = "Crossbow MIB510 programming board";
  type  = "stk500";
  connection_type = serial;
;

programmer
  id    = "stk500v2";
  desc  = "Atmel STK500 Version 2.x firmware";
  type  = "stk500v2";
  connection_type = serial;
;

programmer
  id    = "stk500pp";
  desc  = "Atmel STK500 V2 in parallel programming mode";
  type  = "stk500pp";
  connection_type = serial;
;

programmer
  id    = "stk500hvsp";
  desc  = "Atmel STK500 V2 in high-voltage serial programming mode";
  type  = "stk500hvsp";
  connection_type = serial;
;

programmer
  id    = "stk600";
  desc  = "Atmel STK600";
  type  = "stk600";
  connection_type = usb;
;

programmer
  id    = "stk600pp";
  desc  = "Atmel STK600 in parallel programming mode";
  type  = "stk600pp";
  connection_type = usb;
;

programmer
  id    = "stk600hvsp";
  desc  = "Atmel STK600 in high-voltage serial programming mode";
  type  = "stk600hvsp";
  connection_type = usb;
;

programmer
  id    = "avr910";
  desc  = "Atmel Low Cost Serial Programmer";
  type  = "avr910";
  connection_type = serial;
;

programmer
  id    = "ft245r";
  desc  = "FT245R Synchronous BitBang";
  type  = "ftdi_syncbb";
  connection_type = usb;
  miso  = 1; # D1
  sck   = 0; # D0
  mosi  = 2; # D2
  reset = 4; # D4
;

programmer
  id    = "ft232r";
  desc  = "FT232R Synchronous BitBang";
  type  = "ftdi_syncbb";
  connection_type = usb;
  miso  = 1;  # RxD
  sck   = 0;  # TxD
  mosi  = 2;  # RTS
  reset = 4;  # DTR
;

# see http://www.bitwizard.nl/wiki/index.php/FTDI_ATmega
programmer
  id    = "bwmega";
  desc  = "BitWizard ftdi_atmega builtin programmer";
  type  = "ftdi_syncbb";
  connection_type = usb;
  miso  = 5;  # DSR
  sck   = 6;  # DCD
  mosi  = 3;  # CTS
  reset = 7;  # RI
;

# see http://www.geocities.jp/arduino_diecimila/bootloader/index_en.html
# Note: pins are numbered from 1!
programmer
  id    = "arduino-ft232r";
  desc  = "Arduino: FT232R connected to ISP";
  type  = "ftdi_syncbb";
  connection_type = usb;
  miso  = 3;  # CTS X3(1)
  sck   = 5;  # DSR X3(2)
  mosi  = 6;  # DCD X3(3)
  reset = 7;  # RI  X3(4)
;

# website mentioned above uses this id
programmer parent "arduino-ft232r"
  id    = "diecimila";
  desc  = "alias for arduino-ft232r";
;

# There is a ATmega328P kit PCB called "uncompatino".
# This board allows ISP via its on-board FT232R.
# This is designed like Arduino Duemilanove but has no standard ICPS header.
# Its 4 pairs of pins are shorted to enable ftdi_syncbb.
# http://akizukidenshi.com/catalog/g/gP-07487/
# http://akizukidenshi.com/download/ds/akizuki/k6096_manual_20130816.pdf
programmer
  id    = "uncompatino";
  desc  = "uncompatino with all pairs of pins shorted";
  type  = "ftdi_syncbb";
  connection_type = usb;
  miso  = 3; # cts
  sck   = 5; # dsr
  mosi  = 6; # dcd
  reset = 7; # ri
;

# FTDI USB to serial cable TTL-232R-5V with a custom adapter for ICSP
# http://www.ftdichip.com/Products/Cables/USBTTLSerial.htm
# http://www.ftdichip.com/Support/Documents/DataSheets/Cables/DS_TTL-232R_CABLES.pdf
# For ICSP pinout see for example http://www.atmel.com/images/doc2562.pdf
# (Figure 1. ISP6PIN header pinout and Table 1. Connections required for ISP ...)
# TTL-232R GND 1 Black  -> ICPS GND   (pin 6)
# TTL-232R CTS 2 Brown  -> ICPS MOSI  (pin 4)
# TTL-232R VCC 3 Red    -> ICPS VCC   (pin 2)
# TTL-232R TXD 4 Orange -> ICPS RESET (pin 5)
# TTL-232R RXD 5 Yellow -> ICPS SCK   (pin 3)
# TTL-232R RTS 6 Green  -> ICPS MISO  (pin 1)
# Except for VCC and GND, you can connect arbitual pairs as long as 
# the following table is adjusted.
programmer
  id    = "ttl232r";
  desc  = "FTDI TTL232R-5V with ICSP adapter";
  type  = "ftdi_syncbb";
  connection_type = usb;
  miso  = 2; # rts
  sck   = 1; # rxd
  mosi  = 3; # cts
  reset = 0; # txd
;

programmer
  id    = "usbasp";
  desc  = "USBasp, http://www.fischl.de/usbasp/";
  type  = "usbasp";
  connection_type = usb;
  usbvid     = 0x16C0; # VOTI
  usbpid     = 0x05DC; # Obdev's free shared PID
  usbvendor  = "www.fischl.de";
  usbproduct = "USBasp";

  # following variants are autodetected for id "usbasp"

  # original usbasp from fischl.de
  # see above "usbasp"

  # old usbasp from fischl.de
  #usbvid     = 0x03EB; # ATMEL
  #usbpid     = 0xC7B4; # (unoffical) USBasp
  #usbvendor  = "www.fischl.de";
  #usbproduct = "USBasp";

  # NIBObee (only if -P nibobee is given on command line)
  # see below "nibobee"
;

programmer
  id    = "nibobee";
  desc  = "NIBObee";
  type  = "usbasp";
  connection_type = usb;
  usbvid     = 0x16C0; # VOTI
  usbpid     = 0x092F; # NIBObee PID
  usbvendor  = "www.nicai-systems.com";
  usbproduct = "NIBObee";
;

programmer
  id    = "usbasp-clone";
  desc  = "Any usbasp clone with correct VID/PID";
  type  = "usbasp";
  connection_type = usb;
  usbvid    = 0x16C0; # VOTI
  usbpid    = 0x05DC; # Obdev's free shared PID
  #usbvendor  = "";
  #usbproduct = "";
;

programmer
  id    = "usbtiny";
  desc  = "USBtiny simple USB programmer, http://www.ladyada.net/make/usbtinyisp/";
  type  = "usbtiny";
  connection_type = usb;
  usbvid     = 0x1781;
  usbpid     = 0x0c9f;
;

# commercial version of USBtiny, using a separate VID/PID
programmer
  id    = "ehajo-isp";
  desc  = "avr-isp-programmer from eHaJo, http://www.eHaJo.de";
  type  = "usbtiny";
  connection_type = usb;
  usbvid     = 0x16D0;
  usbpid     = 0x0BA5;
;

programmer
  id    = "butterfly";
  desc  = "Atmel Butterfly Development Board";
  type  = "butterfly";
  connection_type = serial;
;

programmer
  id    = "avr109";
  desc  = "Atmel AppNote AVR109 Boot Loader";
  type  = "butterfly";
  connection_type = serial;
;

programmer
  id    = "avr911";
  desc  = "Atmel AppNote AVR911 AVROSP";
  type  = "butterfly";
  connection_type = serial;
;
 
# suggested in http://forum.mikrokopter.de/topic-post48317.html
programmer
  id    = "mkbutterfly";
  desc  = "Mikrokopter.de Butterfly";
  type  = "butterfly_mk";
  connection_type = serial;
;

programmer parent "mkbutterfly"
  id    = "butterfly_mk";
;

programmer
  id    = "jtagmkI";
  desc  = "Atmel JTAG ICE (mkI)";
  baudrate = 115200;    # default is 115200
  type  = "jtagmki";
  connection_type = serial;
;

# easier to type
programmer parent "jtagmkI"
  id    = "jtag1";
;

# easier to type
programmer parent "jtag1"
  id    = "jtag1slow";
  baudrate = 19200;
;

# The JTAG ICE mkII has both, serial and USB connectivity.  As it is
# mostly used through USB these days (AVR Studio 5 only supporting it
# that way), we make connection_type = usb the default.  Users are
# still free to use a serial port with the -P option.

programmer
  id    = "jtagmkII";
  desc  = "Atmel JTAG ICE mkII";
  baudrate = 19200;    # default is 19200
  type  = "jtagmkii";
  connection_type = usb;
;

# easier to type
programmer parent "jtagmkII"
  id    = "jtag2slow";
;

# JTAG ICE mkII @ 115200 Bd
programmer parent "jtag2slow"
  id    = "jtag2fast";
  baudrate = 115200;
;

# make the fast one the default, people will love that
programmer parent "jtag2fast"
  id    = "jtag2";
;

# JTAG ICE mkII in ISP mode
programmer
  id    = "jtag2isp";
  desc  = "Atmel JTAG ICE mkII in ISP mode";
  baudrate = 115200;
  type  = "jtagmkii_isp";
  connection_type = usb;
;

# JTAG ICE mkII in debugWire mode
programmer
  id    = "jtag2dw";
  desc  = "Atmel JTAG ICE mkII in debugWire mode";
  baudrate = 115200;
  type  = "jtagmkii_dw";
  connection_type = usb;
;

# JTAG ICE mkII in AVR32 mode
programmer
  id    = "jtagmkII_avr32";
  desc  = "Atmel JTAG ICE mkII im AVR32 mode";
  baudrate = 115200;
  type  = "jtagmkii_avr32";
  connection_type = usb;
;

# JTAG ICE mkII in AVR32 mode
programmer
  id    = "jtag2avr32";
  desc  = "Atmel JTAG ICE mkII im AVR32 mode";
  baudrate = 115200;
  type  = "jtagmkii_avr32";
  connection_type = usb;
;

# JTAG ICE mkII in PDI mode
programmer
  id    = "jtag2pdi";
  desc  = "Atmel JTAG ICE mkII PDI mode";
  baudrate = 115200;
  type  = "jtagmkii_pdi";
  connection_type = usb;
;

# AVR Dragon in JTAG mode
programmer
  id    = "dragon_jtag";
  desc  = "Atmel AVR Dragon in JTAG mode";
  baudrate = 115200;
  type  = "dragon_jtag";
  connection_type = usb;
;

# AVR Dragon in ISP mode
programmer
  id    = "dragon_isp";
  desc  = "Atmel AVR Dragon in ISP mode";
  baudrate = 115200;
  type  = "dragon_isp";
  connection_type = usb;
;

# AVR Dragon in PP mode
programmer
  id    = "dragon_pp";
  desc  = "Atmel AVR Dragon in PP mode";
  baudrate = 115200;
  type  = "dragon_pp";
  connection_type = usb;
;

# AVR Dragon in HVSP mode
programmer
  id    = "dragon_hvsp";
  desc  = "Atmel AVR Dragon in HVSP mode";
  baudrate = 115200;
  type  = "dragon_hvsp";
  connection_type = usb;
;

# AVR Dragon in debugWire mode
programmer
  id    = "dragon_dw";
  desc  = "Atmel AVR Dragon in debugWire mode";
  baudrate = 115200;
  type  = "dragon_dw";
  connection_type = usb;
;

# AVR Dragon in PDI mode
programmer
  id    = "dragon_pdi";
  desc  = "Atmel AVR Dragon in PDI mode";
  baudrate = 115200;
  type  = "dragon_pdi";
  connection_type = usb;
;

programmer
  id    = "jtag3";
  desc  = "Atmel AVR JTAGICE3 in JTAG mode";
  type  = "jtagice3";
  connection_type = usb;
  usbpid = 0x2110, 0x2140;
;

programmer
  id    = "jtag3pdi";
  desc  = "Atmel AVR JTAGICE3 in PDI mode";
  type  = "jtagice3_pdi";
  connection_type = usb;
  usbpid = 0x2110, 0x2140;
;

programmer
  id    = "jtag3dw";
  desc  = "Atmel AVR JTAGICE3 in debugWIRE mode";
  type  = "jtagice3_dw";
  connection_type = usb;
  usbpid = 0x2110, 0x2140;
;

programmer
  id    = "jtag3isp";
  desc  = "Atmel AVR JTAGICE3 in ISP mode";
  type  = "jtagice3_isp";
  connection_type = usb;
  usbpid = 0x2110, 0x2140;
;

programmer
  id    = "xplainedpro";
  desc  = "Atmel AVR XplainedPro in JTAG mode";
  type  = "jtagice3";
  connection_type = usb;
  usbpid = 0x2111;
;

programmer
  id    = "xplainedmini";
  desc  = "Atmel AVR XplainedMini in ISP mode";
  type  = "jtagice3_isp";
  connection_type = usb;
  usbpid = 0x2145;
;

programmer
  id    = "xplainedmini_dw";
  desc  = "Atmel AVR XplainedMini in debugWIRE mode";
  type  = "jtagice3_dw";
  connection_type = usb;
  usbpid = 0x2145;
;

programmer
  id    = "atmelice";
  desc  = "Atmel-ICE (ARM/AVR) in JTAG mode";
  type  = "jtagice3";
  connection_type = usb;
  usbpid = 0x2141;
;

programmer
  id    = "atmelice_pdi";
  desc  = "Atmel-ICE (ARM/AVR) in PDI mode";
  type  = "jtagice3_pdi";
  connection_type = usb;
  usbpid = 0x2141;
;

programmer
  id    = "atmelice_dw";
  desc  = "Atmel-ICE (ARM/AVR) in debugWIRE mode";
  type  = "jtagice3_dw";
  connection_type = usb;
  usbpid = 0x2141;
;

programmer
  id    = "atmelice_isp";
  desc  = "Atmel-ICE (ARM/AVR) in ISP mode";
  type  = "jtagice3_isp";
  connection_type = usb;
  usbpid = 0x2141;
;


programmer
  id    = "pavr";
  desc  = "Jason Kyle's pAVR Serial Programmer";
  type  = "avr910";
  connection_type = serial;
;

programmer
  id    = "pickit2";
  desc  = "MicroChip's PICkit2 Programmer";
  type  = "pickit2";
  connection_type = usb;
;

programmer
  id    = "flip1";
  desc  = "FLIP USB DFU protocol version 1 (doc7618)";
  type  = "flip1";
  connection_type = usb;
;

programmer
  id    = "flip2";
  desc  = "FLIP USB DFU protocol version 2 (AVR4023)";
  type  = "flip2";
  connection_type = usb;
;

# Parallel port programmers.

programmer
  id    = "bsd";
  desc  = "Brian Dean's Programmer, http://www.bsdhome.com/avrdude/";
  type  = "par";
  connection_type = parallel;
  vcc   = 2, 3, 4, 5;
  reset = 7;
  sck   = 8;
  mosi  = 9;
  miso  = 10;
;

programmer
  id    = "stk200";
  desc  = "STK200";
  type  = "par";
  connection_type = parallel;
  buff  = 4, 5;
  sck   = 6;
  mosi  = 7;
  reset = 9;
  miso  = 10;
;

# The programming dongle used by the popular Ponyprog
# utility.  It is almost similar to the STK200 one,
# except that there is a LED indicating that the
# programming is currently in progress.

programmer parent "stk200"
  id    = "pony-stk200";
  desc  = "Pony Prog STK200";
  pgmled = 8; 
;

programmer
  id    = "dt006";
  desc  = "Dontronics DT006";
  type  = "par";
  connection_type = parallel;
  reset = 4;
  sck   = 5;
  mosi  = 2;
  miso  = 11;
;

programmer parent "dt006"
  id    = "bascom";
  desc  = "Bascom SAMPLE programming cable";
;

programmer
  id     = "alf";
  desc   = "Nightshade ALF-PgmAVR, http://nightshade.homeip.net/";
  type   = "par";
  connection_type = parallel;
  vcc    = 2, 3, 4, 5;
  buff   = 6;
  reset  = 7;
  sck    = 8;
  mosi   = 9;
  miso   = 10;
  errled = 1;
  rdyled = 14;
  pgmled = 16;
  vfyled = 17;
;

programmer
  id    = "sp12";
  desc  = "Steve Bolt's Programmer";
  type  = "par";
  connection_type = parallel;
  vcc   = 4,5,6,7,8;
  reset = 3;
  sck   = 2;
  mosi  = 9;
  miso  = 11;
;

programmer
  id     = "picoweb";
  desc   = "Picoweb Programming Cable, http://www.picoweb.net/";
  type   = "par";
  connection_type = parallel;
  reset  = 2;
  sck    = 3;
  mosi   = 4;
  miso   = 13;
;

programmer
  id    = "abcmini";
  desc  = "ABCmini Board, aka Dick Smith HOTCHIP";
  type  = "par";
  connection_type = parallel;
  reset = 4;
  sck   = 3;
  mosi  = 2;
  miso  = 10;
;

programmer
  id    = "futurlec";
  desc  = "Futurlec.com programming cable.";
  type  = "par";
  connection_type = parallel;
  reset = 3;
  sck   = 2;
  mosi  = 1;
  miso  = 10;
;


# From the contributor of the "xil" jtag cable:
# The "vcc" definition isn't really vcc (the cable gets its power from
# the programming circuit) but is necessary to switch one of the
# buffer lines (trying to add it to the "buff" lines doesn't work in 
# avrdude versions before 5.5j).
# With this, TMS connects to RESET, TDI to MOSI, TDO to MISO and TCK
# to SCK (plus vcc/gnd of course)
programmer
  id    = "xil";
  desc  = "Xilinx JTAG cable";
  type  = "par";
  connection_type = parallel;
  mosi  = 2;
  sck   = 3;
  reset = 4;
  buff  = 5;
  miso  = 13;
  vcc   = 6;
;


programmer
  id = "dapa";
  desc = "Direct AVR Parallel Access cable";
  type = "par";
  connection_type = parallel;
  vcc   = 3;
  reset = 16;
  sck = 1;
  mosi = 2;
  miso = 11;
;

programmer
  id    = "atisp";
  desc  = "AT-ISP V1.1 programming cable for AVR-SDK1 from <http://micro-research.co.th/> micro-research.co.th";
  type  = "par";
  connection_type = parallel;
  reset = ~6;
  sck   = ~8;
  mosi  = ~7;
  miso  = ~10;
;

programmer
  id    = "ere-isp-avr";
  desc  = "ERE ISP-AVR <http://www.ere.co.th/download/sch050713.pdf>";
  type  = "par";
  connection_type = parallel;
  reset = ~4;
  sck   = 3;
  mosi  = 2;
  miso  = 10;
;

programmer
  id    = "blaster";
  desc  = "Altera ByteBlaster";
  type  = "par";
  connection_type = parallel;
  sck   = 2;
  miso  = 11;
  reset = 3;
  mosi  = 8;
  buff  = 14;
;

# It is almost same as pony-stk200, except vcc on pin 5 to auto
# disconnect port (download on http://electropol.free.fr/spip/spip.php?article27)
programmer parent "pony-stk200"
  id    = "frank-stk200";
  desc  = "Frank STK200";
  buff  = ; # delete buff pin assignment
  vcc   = 5;
;

# The AT98ISP Cable is a simple parallel dongle for AT89 family.
# http://www.atmel.com/dyn/products/tools_card.asp?tool_id=2877
programmer
  id = "89isp";
  desc = "Atmel at89isp cable";
  type = "par";
  connection_type = parallel;
  reset = 17;
  sck = 1;
  mosi = 2;
  miso = 10;
;


#This programmer bitbangs GPIO lines using the Linux sysfs GPIO interface
#
#To enable it set the configuration below to match the GPIO lines connected to the
#relevant ISP header pins and uncomment the entry definition. In case you don't
#have the required permissions to edit this system wide config file put the
#entry in a separate <your name>.conf file and use it with -C+<your name>.conf
#on the command line.
#
#To check if your avrdude build has support for the linuxgpio programmer compiled in,
#use -c?type on the command line and look for linuxgpio in the list. If it's not available
#you need pass the --enable-linuxgpio=yes option to configure and recompile avrdude.
#
#programmer
#  id    = "linuxgpio";
#  desc  = "Use the Linux sysfs interface to bitbang GPIO lines";
#  type  = "linuxgpio";
#  reset = ?;
#  sck   = ?;
#  mosi  = ?;
#  miso  = ?;
#;

# some ultra cheap programmers use bitbanging on the 
# serialport.
#
# PC - DB9 - Pins for RS232:
#
# GND   5   -- |O
#              |   O| <-   9   RI
# DTR   4   <- |O   |
#              |   O| <-   8   CTS
# TXD   3   <- |O   |
#              |   O| ->   7   RTS
# RXD   2   -> |O   |
#              |   O| <-   6   DSR
# DCD   1   -> |O
#
# Using RXD is currently not supported.
# Using RI is not supported under Win32 but is supported under Posix.

# serial ponyprog design (dasa2 in uisp)
# reset=!txd sck=rts mosi=dtr miso=cts

programmer
  id    = "ponyser";
  desc  = "design ponyprog serial, reset=!txd sck=rts mosi=dtr miso=cts";
  type  = "serbb";
  connection_type = serial;
  reset = ~3;
  sck   = 7;
  mosi  = 4;
  miso  = 8;
;

# Same as above, different name
# reset=!txd sck=rts mosi=dtr miso=cts

programmer parent "ponyser"
  id    = "siprog";
  desc  = "Lancos SI-Prog <http://www.lancos.com/siprogsch.html>";
;

# unknown (dasa in uisp)
# reset=rts sck=dtr mosi=txd miso=cts

programmer
  id    = "dasa";
  desc  = "serial port banging, reset=rts sck=dtr mosi=txd miso=cts";
  type  = "serbb";
  connection_type = serial;
  reset = 7;
  sck   = 4;
  mosi  = 3;
  miso  = 8;
;

# unknown (dasa3 in uisp)
# reset=!dtr sck=rts mosi=txd miso=cts

programmer
  id    = "dasa3";
  desc  = "serial port banging, reset=!dtr sck=rts mosi=txd miso=cts";
  type  = "serbb";
  connection_type = serial;
  reset = ~4;
  sck   = 7;
  mosi  = 3;
  miso  = 8;
;

# C2N232i (jumper configuration "auto")
# reset=dtr sck=!rts mosi=!txd miso=!cts

programmer
  id    = "c2n232i";
  desc  = "serial port banging, reset=dtr sck=!rts mosi=!txd miso=!cts";
  type  = "serbb";
  connection_type = serial;
  reset = 4;
  sck   = ~7;
  mosi  = ~3;
  miso  = ~8;
;

#
# PART DEFINITIONS
#

#------------------------------------------------------------
# ATtiny11
#------------------------------------------------------------

# This is an HVSP-only device.

part
    id                  = "t11";
    desc                = "ATtiny11";
    stk500_devcode      = 0x11;
    signature           = 0x1e 0x90 0x04;
    chip_erase_delay    = 20000;

    timeout		= 200;
    hvsp_controlstack     =
        0x4C, 0x0C, 0x1C, 0x2C, 0x3C, 0x64, 0x74, 0x00,
        0x68, 0x78, 0x68, 0x68, 0x00, 0x00, 0x68, 0x78,
        0x78, 0x00, 0x6D, 0x0C, 0x80, 0x40, 0x20, 0x10,
        0x11, 0x08, 0x04, 0x02, 0x03, 0x08, 0x04, 0x00;
    hventerstabdelay    = 100;
    progmodedelay       = 0;
    hvspcmdexedelay     = 0;
    synchcycles         = 6;
    latchcycles         = 1;
    togglevtg           = 1;
    poweroffdelay       = 25;
    resetdelayms        = 0;
    resetdelayus        = 50;
    hvleavestabdelay    = 100;
    resetdelay          = 25;
    chiperasepolltimeout = 40;
    chiperasetime       = 0;
    programfusepolltimeout = 25;
    programlockpolltimeout = 25;

    memory "eeprom"
        size            = 64;
	blocksize	= 64;
	readsize	= 256;
	delay		= 5;
    ;

    memory "flash"
        size            = 1024;
	blocksize	= 128;
	readsize	= 256;
	delay		= 3;
    ;

    memory "signature"
        size            = 3;
    ;

    memory "lock"
        size            = 1;
    ;

    memory "calibration"
        size            = 1;
    ;

    memory "fuse"
        size            = 1;
    ;
;

#------------------------------------------------------------
# ATtiny12
#------------------------------------------------------------

part
    id                  = "t12";
    desc                = "ATtiny12";
    stk500_devcode      = 0x12;
    avr910_devcode      = 0x55;
    signature           = 0x1e 0x90 0x05;
    chip_erase_delay    = 20000;
    pgm_enable          = "1 0 1 0  1 1 0 0   0 1 0 1  0 0 1 1",
                          "x x x x  x x x x   x x x x  x x x x";

    chip_erase          = "1 0 1 0  1 1 0 0   1 0 0 x  x x x x",
                          "x x x x  x x x x   x x x x  x x x x";

    timeout		= 200;
    stabdelay		= 100;
    cmdexedelay		= 25;
    synchloops		= 32;
    bytedelay		= 0;
    pollindex		= 3;
    pollvalue		= 0x53;
    predelay		= 1;
    postdelay		= 1;
    pollmethod		= 0;

    hvsp_controlstack   =
        0x4C, 0x0C, 0x1C, 0x2C, 0x3C, 0x64, 0x74, 0x00,
        0x68, 0x78, 0x68, 0x68, 0x00, 0x00, 0x68, 0x78,
        0x78, 0x00, 0x6D, 0x0C, 0x80, 0x40, 0x20, 0x10,
        0x11, 0x08, 0x04, 0x02, 0x03, 0x08, 0x04, 0x00;
    hventerstabdelay    = 100;
    hvspcmdexedelay     = 0;
    synchcycles         = 6;
    latchcycles         = 1;
    togglevtg           = 1;
    poweroffdelay       = 25;
    resetdelayms        = 0;
    resetdelayus        = 50;
    hvleavestabdelay    = 100;
    resetdelay          = 25;
    chiperasepolltimeout = 40;
    chiperasetime       = 0;
    programfusepolltimeout = 25;
    programlockpolltimeout = 25;

    memory "eeprom"
        size            = 64;
        min_write_delay = 9000;
        max_write_delay = 20000;
        readback_p1     = 0xff;
        readback_p2     = 0xff;
        read            = "1  0  1  0   0  0  0  0    x x x x  x x x x",
                          "x  x a5 a4  a3 a2 a1 a0    o o o o  o o o o";

        write           = "1  1  0  0   0  0  0  0    x x x x  x x x x",
                          "x  x a5 a4  a3 a2 a1 a0    i i i i  i i i i";

	mode		= 0x04;
	delay		= 8;
	blocksize	= 64;
	readsize	= 256;
    ;

    memory "flash"
        size            = 1024;
        min_write_delay = 4500;
        max_write_delay = 20000;
        readback_p1     = 0xff;
        readback_p2     = 0xff;
        read_lo         = "  0  0  1  0   0  0  0  0",
                          "  x  x  x  x   x  x  x a8",
                          " a7 a6 a5 a4  a3 a2 a1 a0",
                          "  o  o  o  o   o  o  o  o";

        read_hi         = "  0  0  1  0   1  0  0  0",
                          "  x  x  x  x   x  x  x a8",
                          " a7 a6 a5 a4  a3 a2 a1 a0",
                          "  o  o  o  o   o  o  o  o";

        write_lo        = "  0  1  0  0   0  0  0  0",
                          "  x  x  x  x   x  x  x a8",
                          " a7 a6 a5 a4  a3 a2 a1 a0",
                          "  i  i  i  i   i  i  i  i";

        write_hi        = "  0  1  0  0   1  0  0  0",
                          "  x  x  x  x   x  x  x a8",
                          " a7 a6 a5 a4  a3 a2 a1 a0",
                          "  i  i  i  i   i  i  i  i";

	mode		= 0x04;
	delay		= 5;
	blocksize	= 128;
	readsize	= 256;
    ;

    memory "signature"
        size            = 3;
        read            = "0  0  1  1   0  0  0  0    x x x x  x x x x",
                          "0  0  0  0   0  0 a1 a0    o o o o  o o o o";
    ;

    memory "lock"
        size            = 1;
        read            = "0  1  0  1   1  0  0  0    x x x x  x x x x",
                          "x  x  x  x   x  x  x  x    x x x x  x o o x";

        write           = "1  0  1  0   1  1  0  0    1 1 1 1  1 i i 1",
                          "x  x  x  x   x  x  x  x    x x x x  x x x x";
        min_write_delay = 9000;
        max_write_delay = 9000;
    ;

    memory "calibration"
        size            = 1;
        read            = "0  0  1  1   1  0  0  0    x x x x  x x x x",
                          "0  0  0  0   0  0  0  0    o o o o  o o o o";
    ;

    memory "fuse"
        size            = 1;
        read            = "0  1  0  1   0  0  0  0    x x x x  x x x x",
                          "x  x  x  x   x  x  x  x    o o o o  o o o o";

        write           = "1  0  1  0   1  1  0  0    1 0 1 x  x x x x",
                          "x  x  x  x   x  x  x  x    i i i i  i i i i";
        min_write_delay = 9000;
        max_write_delay = 9000;
    ;
;

#------------------------------------------------------------
# ATtiny13
#------------------------------------------------------------

part
    id                  = "t13";
    desc                = "ATtiny13";
     has_debugwire = yes;
     flash_instr   = 0xB4, 0x0E, 0x1E;
     eeprom_instr  = 0xBB, 0xFE, 0xBB, 0xEE, 0xBB, 0xCC, 0xB2, 0x0D,
	             0xBC, 0x0E, 0xB4, 0x0E, 0xBA, 0x0D, 0xBB, 0xBC,
	             0x99, 0xE1, 0xBB, 0xAC;
    stk500_devcode      = 0x14;
    signature           = 0x1e 0x90 0x07;
    chip_erase_delay    = 4000;
    pgm_enable          = "1 0 1 0  1 1 0 0   0 1 0 1  0 0 1 1",
                          "x x x x  x x x x   x x x x  x x x x";

    chip_erase          = "1 0 1 0  1 1 0 0   1 0 0 x  x x x x",
                          "x x x x  x x x x   x x x x  x x x x";

    timeout		= 200;
    stabdelay		= 100;
    cmdexedelay		= 25;
    synchloops		= 32;
    bytedelay		= 0;
    pollindex		= 3;
    pollvalue		= 0x53;
    predelay		= 1;
    postdelay		= 1;
    pollmethod		= 1;

    hvsp_controlstack     =
	0x4C, 0x0C, 0x1C, 0x2C, 0x3C, 0x64, 0x74, 0x66,
        0x68, 0x78, 0x68, 0x68, 0x7A, 0x6A, 0x68, 0x78,
        0x78, 0x7D, 0x6D, 0x0C, 0x80, 0x40, 0x20, 0x10,
        0x11, 0x08, 0x04, 0x02, 0x03, 0x08, 0x04, 0x00;
    hventerstabdelay    = 100;
    progmodedelay       = 0;
    hvspcmdexedelay     = 0;
    synchcycles         = 6;
    latchcycles         = 1;
    togglevtg           = 1;
    poweroffdelay       = 25;
    resetdelayms        = 0;
    resetdelayus        = 90;
    hvleavestabdelay    = 100;
    resetdelay          = 25;
    chiperasepolltimeout = 40;
    chiperasetime       = 0;
    programfusepolltimeout = 25;
    programlockpolltimeout = 25;

    ocdrev              = 0;

    memory "eeprom"
        size            = 64;
        page_size       = 4;
        min_write_delay = 4000;
        max_write_delay = 4000;
        readback_p1     = 0xff;
        readback_p2     = 0xff;
        read            = "1  0  1  0   0  0  0  0    0 0 0 x  x x x x",
                          "x  x a5 a4  a3 a2 a1 a0    o o o o  o o o o";

        write           = "1  1  0  0   0  0  0  0    0 0 0 x  x x x x",
                          "x  x a5 a4  a3 a2 a1 a0    i i i i  i i i i";

	loadpage_lo	= "  1   1   0   0      0   0   0   1",
			  "  0   0   0   0      0   0   0   0",
			  "  0   0   0   0      0   0  a1  a0",
			  "  i   i   i   i      i   i   i   i";

	writepage	= "  1   1   0   0      0   0   1   0",
			  "  0   0   x   x      x   x   x   x",
			  "  x   x  a5  a4     a3  a2   0   0",
			  "  x   x   x   x      x   x   x   x";

	mode		= 0x41;
	delay		= 5;
	blocksize	= 4;
	readsize	= 256;
    ;

    memory "flash"
        paged           = yes;
        size            = 1024;
        page_size       = 32;
        num_pages       = 32;
        min_write_delay = 4500;
        max_write_delay = 4500;
        readback_p1     = 0xff;
        readback_p2     = 0xff;
        read_lo         = "  0  0  1  0   0  0  0  0",
                          "  0  0  0  0   0  0  0 a8",
                          " a7 a6 a5 a4  a3 a2 a1 a0",
                          "  o  o  o  o   o  o  o  o";

        read_hi         = "  0  0  1  0   1  0  0  0",
                          "  0  0  0  0   0  0  0 a8",
                          " a7 a6 a5 a4  a3 a2 a1 a0",
                          "  o  o  o  o   o  o  o  o";

        loadpage_lo     = "  0  1  0  0   0  0  0  0",
                          "  0  0  0  x   x  x  x  x",
                          "  x  x  x  x  a3 a2 a1 a0",
                          "  i  i  i  i   i  i  i  i";

        loadpage_hi     = "  0  1  0  0   1  0  0  0",
                          "  0  0  0  x   x  x  x  x",
                          "  x  x  x  x  a3 a2 a1 a0",
                          "  i  i  i  i   i  i  i  i";

        writepage       = "  0  1  0  0   1  1  0  0",
                          "  0  0  0  0   0  0  0 a8",
                          " a7 a6 a5 a4   x  x  x  x",
                          "  x  x  x  x   x  x  x  x";

	mode		= 0x41;
	delay		= 6;
	blocksize	= 32;
	readsize	= 256;
    ;

    memory "signature"
        size            = 3;
        read            = "0  0  1  1   0  0  0  0    0 0 0 x  x x x x",
                          "x  x  x  x   x  x a1 a0    o o o o  o o o o";
    ;

    memory "lock"
        size            = 1;
        min_write_delay = 4500;
        max_write_delay = 4500;

	read            = "0  1  0  1   1  0  0  0    0 0 0 0  0 0 0 0",
                          "x  x  x  x   x  x  x  x    x x o o  o o o o";

        write           = "1  0  1  0   1  1  0  0    1 1 1 x  x x x x",
                          "x  x  x  x   x  x  x  x    1 1 i i  i i i i";
    ;

    memory "calibration"
        size            = 2;
        read            = "0  0  1  1   1  0  0  0    0 0 0 x  x x x x",
                          "0  0  0  0   0  0  0 a0    o o o o  o o o o";
    ;

    memory "lfuse"
        size            = 1;
        min_write_delay = 4500;
        max_write_delay = 4500;

        write           = "1 0 1 0  1 1 0 0  1 0 1 0  0 0 0 0",
                          "x x x x  x x x x  i i i i  i i i i";

        read            = "0 1 0 1  0 0 0 0  0 0 0 0  0 0 0 0",
                          "x x x x  x x x x  o o o o  o o o o";
      ;

    memory "hfuse"
        size            = 1;
        min_write_delay = 4500;
        max_write_delay = 4500;

        write           = "1 0 1 0  1 1 0 0  1 0 1 0  1 0 0 0",
                          "x x x x  x x x x  i i i i  i i i i";

        read            = "0 1 0 1  1 0 0 0  0 0 0 0  1 0 0 0",
                          "x x x x  x x x x  o o o o  o o o o";
      ;

;


#------------------------------------------------------------
# ATtiny15
#------------------------------------------------------------

part
    id                  = "t15";
    desc                = "ATtiny15";
    stk500_devcode      = 0x13;
    avr910_devcode      = 0x56;
    signature           = 0x1e 0x90 0x06;
    chip_erase_delay    = 8200;
    pgm_enable          = "1 0 1 0  1 1 0 0   0 1 0 1  0 0 1 1",
                          "x x x x  x x x x   x x x x  x x x x";

    chip_erase          = "1 0 1 0  1 1 0 0   1 0 0 x  x x x x",
                          "x x x x  x x x x   x x x x  x x x x";

    timeout		= 200;
    stabdelay		= 100;
    cmdexedelay		= 25;
    synchloops		= 32;
    bytedelay		= 0;
    pollindex		= 3;
    pollvalue		= 0x53;
    predelay		= 1;
    postdelay		= 1;
    pollmethod		= 0;

    hvsp_controlstack   =
        0x4C, 0x0C, 0x1C, 0x2C, 0x3C, 0x64, 0x74, 0x00,
        0x68, 0x78, 0x68, 0x68, 0x00, 0x00, 0x68, 0x78,
        0x78, 0x00, 0x6D, 0x0C, 0x80, 0x40, 0x20, 0x10,
        0x11, 0x08, 0x04, 0x02, 0x03, 0x08, 0x04, 0x00;
    hventerstabdelay    = 100;
    hvspcmdexedelay     = 5;
    synchcycles         = 6;
    latchcycles         = 16;
    togglevtg           = 1;
    poweroffdelay       = 25;
    resetdelayms        = 0;
    resetdelayus        = 50;
    hvleavestabdelay    = 100;
    resetdelay          = 25;
    chiperasepolltimeout = 40;
    chiperasetime       = 0;
    programfusepolltimeout = 25;
    programlockpolltimeout = 25;

    memory "eeprom"
        size            = 64;
        min_write_delay = 8200;
        max_write_delay = 8200;
        readback_p1     = 0xff;
        readback_p2     = 0xff;
        read            = "1  0  1  0   0  0  0  0    x x x x  x x x x",
                          "x  x a5 a4  a3 a2 a1 a0    o o o o  o o o o";

        write           = "1  1  0  0   0  0  0  0    x x x x  x x x x",
                          "x  x a5 a4  a3 a2 a1 a0    i i i i  i i i i";

	mode		= 0x04;
	delay		= 10;
	blocksize	= 64;
	readsize	= 256;
    ;

    memory "flash"
        size            = 1024;
        min_write_delay = 4100;
        max_write_delay = 4100;
        readback_p1     = 0xff;
        readback_p2     = 0xff;
        read_lo         = "  0  0  1  0   0  0  0  0",
                          "  x  x  x  x   x  x  x a8",
                          " a7 a6 a5 a4  a3 a2 a1 a0",
                          "  o  o  o  o   o  o  o  o";

        read_hi         = "  0  0  1  0   1  0  0  0",
                          "  x  x  x  x   x  x  x a8",
                          " a7 a6 a5 a4  a3 a2 a1 a0",
                          "  o  o  o  o   o  o  o  o";

        write_lo        = "  0  1  0  0   0  0  0  0",
                          "  x  x  x  x   x  x  x a8",
                          " a7 a6 a5 a4  a3 a2 a1 a0",
                          "  i  i  i  i   i  i  i  i";

        write_hi        = "  0  1  0  0   1  0  0  0",
                          "  x  x  x  x   x  x  x a8",
                          " a7 a6 a5 a4  a3 a2 a1 a0",
                          "  i  i  i  i   i  i  i  i";

	mode		= 0x04;
	delay		= 5;
	blocksize	= 128;
	readsize	= 256;
    ;

    memory "signature"
        size            = 3;
        read            = "0  0  1  1   0  0  0  0    x x x x  x x x x",
                          "0  0  0  0   0  0 a1 a0    o o o o  o o o o";
    ;

    memory "lock"
        size            = 1;
        read            = "0  1  0  1   1  0  0  0    x x x x  x x x x",
                          "x  x  x  x   x  x  x  x    x x x x  x o o x";

        write           = "1  0  1  0   1  1  0  0    1 1 1 1  1 i i 1",
                          "x  x  x  x   x  x  x  x    x x x x  x x x x";
        min_write_delay = 9000;
        max_write_delay = 9000;
    ;

    memory "calibration"
        size            = 1;
        read            = "0  0  1  1   1  0  0  0    x x x x  x x x x",
                          "0  0  0  0   0  0  0  0    o o o o  o o o o";
    ;

    memory "fuse"
        size            = 1;
        read            = "0  1  0  1   0  0  0  0    x x x x  x x x x",
                          "x  x  x  x   x  x  x  x    o o o o  x x o o";

        write           = "1  0  1  0   1  1  0  0    1 0 1 x  x x x x",
                          "x  x  x  x   x  x  x  x    i i i i  1 1 i i";
        min_write_delay = 9000;
        max_write_delay = 9000;
    ;
;

#------------------------------------------------------------
# AT90s1200
#------------------------------------------------------------

part
    id               = "1200";
    desc             = "AT90S1200";
    is_at90s1200     = yes;
    stk500_devcode   = 0x33;
    avr910_devcode   = 0x13;
    signature        = 0x1e 0x90 0x01;
    pagel            = 0xd7;
    bs2              = 0xa0;
    chip_erase_delay = 20000;
    pgm_enable       = "1 0 1 0  1 1 0 0    0 1 0 1  0 0 1 1",
                       "x x x x  x x x x    x x x x  x x x x";

    chip_erase       = "1 0 1 0  1 1 0 0    1 0 0 0  0 0 0 0",
                       "x x x x  x x x x    x x x x  x x x x";

    timeout		= 200;
    stabdelay		= 100;
    cmdexedelay		= 25;
    synchloops		= 1;
    bytedelay		= 0;
    pollindex		= 0;
    pollvalue		= 0xFF;
    predelay		= 1;
    postdelay		= 1;
    pollmethod		= 0;

    pp_controlstack     =
        0x0E, 0x1E, 0x0F, 0x1F, 0x2E, 0x3E, 0x2F, 0x3F,
        0x4E, 0x5E, 0x4F, 0x5F, 0x6E, 0x7E, 0x6F, 0x7F,
        0x66, 0x76, 0x67, 0x77, 0x6A, 0x7A, 0x6B, 0x7B,
        0xBE, 0xFD, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00;
    hventerstabdelay    = 100;
    progmodedelay       = 0;
    latchcycles         = 0;
    togglevtg           = 0;
    poweroffdelay       = 0;
    resetdelayms        = 0;
    resetdelayus        = 0;
    hvleavestabdelay    = 15;
    chiperasepulsewidth = 15;
    chiperasepolltimeout = 0;
    programfusepulsewidth = 2;
    programfusepolltimeout = 0;
    programlockpulsewidth = 0;
    programlockpolltimeout = 1;

    memory "eeprom"
        size            = 64;
        min_write_delay = 4000;
        max_write_delay = 9000;
        readback_p1     = 0x00;
        readback_p2     = 0xff;
        read            = "1 0  1  0   0  0  0  0   x x x x  x x x x", 
                          "x x a5 a4  a3 a2 a1 a0   o o o o  o o o o";

        write           = "1 1  0  0   0  0  0  0   x x x x  x x x x",
                          "x x a5 a4  a3 a2 a1 a0   i i i i  i i i i";

	mode		= 0x04;
	delay		= 20;
	blocksize	= 32;
	readsize	= 256;
      ;
    memory "flash"
        size            = 1024;
        min_write_delay = 4000;
        max_write_delay = 9000;
        readback_p1     = 0xff;
        readback_p2     = 0xff;
        read_lo         = "  0   0   1   0    0   0   0   0",
                          "  x   x   x   x    x   x   x  a8",
                          " a7  a6  a5  a4   a3  a2  a1  a0",
                          "  o   o   o   o    o   o   o   o";

        read_hi         = "  0   0   1   0    1   0   0   0",
                          "  x   x   x   x    x   x   x  a8",
                          " a7  a6  a5  a4   a3  a2  a1  a0",
                          "  o   o   o   o    o   o   o   o";

        write_lo        = "  0   1   0   0    0   0   0   0",
                          "  x   x   x   x    x   x   x  a8",
                          " a7  a6  a5  a4   a3  a2  a1  a0",
                          "  i   i   i   i    i   i   i   i";

        write_hi        = "  0   1   0   0    1   0   0   0",
                          "  x   x   x   x    x   x   x  a8",
                          " a7  a6  a5  a4   a3  a2  a1  a0",
                          "  i   i   i   i    i   i   i   i";

	mode		= 0x02;
	delay		= 15;
	blocksize	= 128;
	readsize	= 256;
      ;
    memory "signature"
        size            = 3;
        read            = "0  0  1  1   0  0  0  0   x  x  x  x   x  x  x  x",
                          "x  x  x  x   x  x a1 a0   o  o  o  o   o  o  o  o";
      ;
    memory "fuse"
        size            = 1;
      ;
    memory "lock"
        size            = 1;
        min_write_delay = 9000;
        max_write_delay = 20000;
        write           = "1 0 1 0  1 1 0 0   1 1 1 1  1 i i 1",
                          "x x x x  x x x x   x x x x  x x x x";
      ;
  ;

#------------------------------------------------------------
# AT90s4414
#------------------------------------------------------------

part
    id               = "4414";
    desc             = "AT90S4414";
    stk500_devcode   = 0x50;
    avr910_devcode   = 0x28;
    signature        = 0x1e 0x92 0x01;
    chip_erase_delay = 20000;
    pgm_enable       = "1 0 1 0  1 1 0 0    0 1 0 1  0 0 1 1",
                       "x x x x  x x x x    x x x x  x x x x";

    chip_erase       = "1 0 1 0  1 1 0 0    1 0 0 0  0 0 0 0",
                       "x x x x  x x x x    x x x x  x x x x";

    timeout		= 200;
    stabdelay		= 100;
    cmdexedelay		= 25;
    synchloops		= 32;
    bytedelay		= 0;
    pollindex		= 3;
    pollvalue		= 0x53;
    predelay		= 1;
    postdelay		= 1;
    pollmethod		= 0;

    pp_controlstack     =
        0x0E, 0x1E, 0x0F, 0x1F, 0x2E, 0x3E, 0x2F, 0x3F,
        0x4E, 0x5E, 0x4F, 0x5F, 0x6E, 0x7E, 0x6F, 0x7F,
        0x66, 0x76, 0x67, 0x77, 0x6A, 0x7A, 0x6B, 0x7B,
        0xBE, 0xFD, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01;
    hventerstabdelay    = 100;
    progmodedelay       = 0;
    latchcycles         = 0;
    togglevtg           = 0;
    poweroffdelay       = 0;
    resetdelayms        = 0;
    resetdelayus        = 0;
    hvleavestabdelay    = 15;
    chiperasepulsewidth = 15;
    chiperasepolltimeout = 0;
    programfusepulsewidth = 2;
    programfusepolltimeout = 0;
    programlockpulsewidth = 0;
    programlockpolltimeout = 1;

    memory "eeprom"
        size            = 256;
        min_write_delay = 9000;
        max_write_delay = 20000;
        readback_p1     = 0x80;
        readback_p2     = 0x7f;
        read            = " 1  0  1  0   0  0  0  0  x x x x  x x x a8", 
                          "a7 a6 a5 a4 a3 a2 a1 a0   o o o o  o o o o";

        write           = " 1  1  0  0   0  0  0  0   x x x x  x x x a8",
                          "a7 a6 a5 a4  a3 a2 a1 a0   i i i i  i i i i";

	mode		= 0x04;
	delay		= 12;
	blocksize	= 64;
	readsize	= 256;
      ;
    memory "flash"
        size            = 4096;
        min_write_delay = 9000;
        max_write_delay = 20000;
        readback_p1     = 0x7f;
        readback_p2     = 0x7f;
        read_lo         = "  0   0   1   0    0   0   0   0",
                          "  x   x   x   x  a11 a10  a9  a8",
                          " a7  a6  a5  a4   a3  a2  a1  a0",
                          "  o   o   o   o    o   o   o   o";

        read_hi         = "  0   0   1   0    1   0   0   0",
                          "  x   x   x   x  a11 a10  a9  a8",
                          " a7  a6  a5  a4   a3  a2  a1  a0",
                          "  o   o   o   o    o   o   o   o";

        write_lo        = "  0   1   0   0    0   0   0   0",
                          "  x   x   x   x  a11 a10  a9  a8",
                          " a7  a6  a5  a4   a3  a2  a1  a0",
                          "  i   i   i   i    i   i   i   i";

        write_hi        = "  0   1   0   0    1   0   0   0",
                          "  x   x   x   x  a11 a10  a9  a8",
                          " a7  a6  a5  a4   a3  a2  a1  a0",
                          "  i   i   i   i    i   i   i   i";

	mode		= 0x04;
	delay		= 12;
	blocksize	= 64;
	readsize	= 256;
      ;
    memory "signature"
        size            = 3;
        read            = "0  0  1  1   0  0  0  0   x  x  x  x   x  x  x  x",
                          "x  x  x  x   x  x a1 a0   o  o  o  o   o  o  o  o";
      ;
    memory "fuse"
	size		= 1;
      ;
    memory "lock"
	size		= 1;
	write		= "1  0  1  0   1  1  0  0   1  1  1  1   1  i  i  1",
			  "x  x  x  x   x  x  x  x   x  x  x  x   x  x  x  x";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;
  ;

#------------------------------------------------------------
# AT90s2313
#------------------------------------------------------------

part
    id               = "2313";
    desc             = "AT90S2313";
    stk500_devcode   = 0x40;
    avr910_devcode   = 0x20;
    signature        = 0x1e 0x91 0x01;
    chip_erase_delay = 20000;
    pgm_enable       = "1 0 1 0  1 1 0 0    0 1 0 1  0 0 1 1",
                       "x x x x  x x x x    x x x x  x x x x";

    chip_erase       = "1 0 1 0  1 1 0 0    1 0 0 0  0 0 0 0",
                       "x x x x  x x x x    x x x x  x x x x";

    timeout		= 200;
    stabdelay		= 100;
    cmdexedelay		= 25;
    synchloops		= 32;
    bytedelay		= 0;
    pollindex		= 3;
    pollvalue		= 0x53;
    predelay		= 1;
    postdelay		= 1;
    pollmethod		= 0;

    pp_controlstack     =
        0x0E, 0x1E, 0x0F, 0x1F, 0x2E, 0x3E, 0x2F, 0x3F,
        0x4E, 0x5E, 0x4F, 0x5F, 0x6E, 0x7E, 0x6F, 0x7F,
        0x66, 0x76, 0x67, 0x77, 0x6A, 0x7A, 0x6B, 0x7B,
        0xBE, 0xFD, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00;
    hventerstabdelay    = 100;
    progmodedelay       = 0;
    latchcycles         = 0;
    togglevtg           = 0;
    poweroffdelay       = 0;
    resetdelayms        = 0;
    resetdelayus        = 0;
    hvleavestabdelay    = 15;
    chiperasepulsewidth = 15;
    chiperasepolltimeout = 0;
    programfusepulsewidth = 2;
    programfusepolltimeout = 0;
    programlockpulsewidth = 0;
    programlockpolltimeout = 1;

    memory "eeprom"
        size            = 128;
        min_write_delay = 4000;
        max_write_delay = 9000;
        readback_p1     = 0x80;
        readback_p2     = 0x7f;
        read            = "1  0  1  0   0  0  0  0   x x x x  x x x x", 
                          "x a6 a5 a4  a3 a2 a1 a0   o o o o  o o o o";

        write           = "1  1  0  0   0  0  0  0   x x x x  x x x x",
                          "x a6 a5 a4  a3 a2 a1 a0   i i i i  i i i i";

	mode		= 0x04;
	delay		= 12;
	blocksize	= 64;
	readsize	= 256;
      ;
    memory "flash"
        size            = 2048;
        min_write_delay = 4000;
        max_write_delay = 9000;
        readback_p1     = 0x7f;
        readback_p2     = 0x7f;
        read_lo         = "  0   0   1   0    0   0   0   0",
                          "  x   x   x   x    x   x  a9  a8",
                          " a7  a6  a5  a4   a3  a2  a1  a0",
                          "  o   o   o   o    o   o   o   o";

        read_hi         = "  0   0   1   0    1   0   0   0",
                          "  x   x   x   x    x   x  a9  a8",
                          " a7  a6  a5  a4   a3  a2  a1  a0",
                          "  o   o   o   o    o   o   o   o";

        write_lo        = "  0   1   0   0    0   0   0   0",
                          "  x   x   x   x    x   x  a9  a8",
                          " a7  a6  a5  a4   a3  a2  a1  a0",
                          "  i   i   i   i    i   i   i   i";

        write_hi        = "  0   1   0   0    1   0   0   0",
                          "  x   x   x   x    x   x  a9  a8",
                          " a7  a6  a5  a4   a3  a2  a1  a0",
                          "  i   i   i   i    i   i   i   i";

	mode		= 0x04;
	delay		= 12;
	blocksize	= 128;
	readsize	= 256;
      ;
    memory "signature"
        size            = 3;
        read            = "0  0  1  1   0  0  0  0   x  x  x  x   x  x  x  x",
                          "x  x  x  x   x  x a1 a0   o  o  o  o   o  o  o  o";
      ;
    memory "fuse"
        size            = 1;
      ;
    memory "lock"
        size            = 1;
        write           = "1 0 1 0  1 1 0 0  1 1 1 x  x i i x",
                          "x x x x  x x x x  x x x x  x x x x";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;
  ;

#------------------------------------------------------------
# AT90s2333
#------------------------------------------------------------

part
    id               = "2333";
##### WARNING: No XML file for device 'AT90S2333'! #####
    desc             = "AT90S2333";
    stk500_devcode   = 0x42;
    avr910_devcode   = 0x34;
    signature        = 0x1e 0x91 0x05;
    chip_erase_delay = 20000;
    pgm_enable       = "1 0 1 0  1 1 0 0    0 1 0 1  0 0 1 1",
                       "x x x x  x x x x    x x x x  x x x x";

    chip_erase       = "1 0 1 0  1 1 0 0    1 0 0 0  0 0 0 0",
                       "x x x x  x x x x    x x x x  x x x x";

    timeout		= 200;
    stabdelay		= 100;
    cmdexedelay		= 25;
    synchloops		= 32;
    bytedelay		= 0;
    pollindex		= 3;
    pollvalue		= 0x53;
    predelay		= 1;
    postdelay		= 1;
    pollmethod		= 0;

    pp_controlstack     =
        0x0E, 0x1E, 0x0F, 0x1F, 0x2E, 0x3E, 0x2F, 0x3F,
        0x4E, 0x5E, 0x4F, 0x5F, 0x6E, 0x7E, 0x6F, 0x7F,
        0x66, 0x76, 0x67, 0x77, 0x6A, 0x7A, 0x6B, 0x7B,
        0xBE, 0xFD, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00;
    hventerstabdelay    = 100;
    progmodedelay       = 0;
    latchcycles         = 0;
    togglevtg           = 0;
    poweroffdelay       = 0;
    resetdelayms        = 0;
    resetdelayus        = 0;
    hvleavestabdelay    = 15;
    chiperasepulsewidth = 15;
    chiperasepolltimeout = 0;
    programfusepulsewidth = 2;
    programfusepolltimeout = 0;
    programlockpulsewidth = 0;
    programlockpolltimeout = 1;

    memory "eeprom"
        size            = 128;
        min_write_delay = 9000;
        max_write_delay = 20000;
        readback_p1     = 0x00;
        readback_p2     = 0xff;
        read            = "1  0  1  0   0  0  0  0   x x x x  x x x x", 
                          "x a6 a5 a4  a3 a2 a1 a0   o o o o  o o o o";

        write           = "1  1  0  0   0  0  0  0   x x x x  x x x x",
                          "x a6 a5 a4  a3 a2 a1 a0   i i i i  i i i i";

	mode		= 0x04;
	delay		= 12;
	blocksize	= 128;
	readsize	= 256;
      ;

    memory "flash"
        size            = 2048;
        min_write_delay = 9000;
        max_write_delay = 20000;
        readback_p1     = 0xff;
        readback_p2     = 0xff;
        read_lo         = "  0   0   1   0    0   0   0   0",
                          "  x   x   x   x    x   x  a9  a8",
                          " a7  a6  a5  a4   a3  a2  a1  a0",
                          "  o   o   o   o    o   o   o   o";

        read_hi         = "  0   0   1   0    1   0   0   0",
                          "  x   x   x   x    x   x  a9  a8",
                          " a7  a6  a5  a4   a3  a2  a1  a0",
                          "  o   o   o   o    o   o   o   o";

        write_lo        = "  0   1   0   0    0   0   0   0",
                          "  x   x   x   x    x   x  a9  a8",
                          " a7  a6  a5  a4   a3  a2  a1  a0",
                          "  i   i   i   i    i   i   i   i";

        write_hi        = "  0   1   0   0    1   0   0   0",
                          "  x   x   x   x    x   x  a9  a8",
                          " a7  a6  a5  a4   a3  a2  a1  a0",
                          "  i   i   i   i    i   i   i   i";

	mode		= 0x04;
	delay		= 12;
	blocksize	= 128;
	readsize	= 256;
      ;

    memory "signature"
        size            = 3;
        read            = "0  0  1  1   0  0  0  0   x  x  x  x   x  x  x  x",
                          "x  x  x  x   x  x a1 a0   o  o  o  o   o  o  o  o";
      ;
    memory "fuse"
        size            = 1;
        min_write_delay = 9000;
        max_write_delay = 20000;
        pwroff_after_write = yes;
        read            = "0 1 0 1  0 0 0 0   x x x x  x x x x",
                          "x x x x  x x x x   x x o o  o o o o";

        write           = "1 0 1 0  1 1 0 0   1 0 1 i  i i i i",
                          "x x x x  x x x x   x x x x  x x x x";
      ;
    memory "lock"
        size            = 1;
        min_write_delay = 9000;
        max_write_delay = 20000;
        read            = "0 1 0 1  1 0 0 0   x x x x  x x x x",
                          "x x x x  x x x x   x x x x  x o o x";

        write           = "1 0 1 0  1 1 0 0   1 1 1 1  1 i i 1",
                          "x x x x  x x x x   x x x x  x x x x";
      ;
  ;


#------------------------------------------------------------
# AT90s2343 (also AT90s2323 and ATtiny22)
#------------------------------------------------------------

part
    id               = "2343";
    desc             = "AT90S2343";
    stk500_devcode   = 0x43;
    avr910_devcode   = 0x4c;
    signature        = 0x1e 0x91 0x03;
    chip_erase_delay = 18000;
    pgm_enable       = "1 0 1 0  1 1 0 0    0 1 0 1  0 0 1 1",
                       "x x x x  x x x x    x x x x  x x x x";

    chip_erase       = "1 0 1 0  1 1 0 0    1 0 0 x  x x x x",
                       "x x x x  x x x x    x x x x  x x x x";

    timeout		= 200;
    stabdelay		= 100;
    cmdexedelay		= 25;
    synchloops		= 32;
    bytedelay		= 0;
    pollindex		= 3;
    pollvalue		= 0x53;
    predelay		= 1;
    postdelay		= 1;
    pollmethod		= 0;

    hvsp_controlstack   =
        0x4C, 0x0C, 0x1C, 0x2C, 0x3C, 0x64, 0x74, 0x00,
        0x68, 0x78, 0x68, 0x68, 0x00, 0x00, 0x68, 0x78,
        0x78, 0x00, 0x6D, 0x0C, 0x80, 0x40, 0x20, 0x10,
        0x11, 0x08, 0x04, 0x02, 0x03, 0x08, 0x04, 0x00;
    hventerstabdelay    = 100;
    hvspcmdexedelay     = 0;
    synchcycles         = 6;
    latchcycles         = 1;
    togglevtg           = 0;
    poweroffdelay       = 25;
    resetdelayms        = 0;
    resetdelayus        = 50;
    hvleavestabdelay    = 100;
    resetdelay          = 25;
    chiperasepolltimeout = 40;
    chiperasetime       = 0;
    programfusepolltimeout = 25;
    programlockpolltimeout = 25;

    memory "eeprom"
        size            = 128;
        min_write_delay = 9000;
        max_write_delay = 20000;
        readback_p1     = 0x00;
        readback_p2     = 0xff;
        read            = "1  0  1  0   0  0  0  0   0 0 0 0  0 0 0 0", 
                          "x a6 a5 a4  a3 a2 a1 a0   o o o o  o o o o";

        write           = "1  1  0  0   0  0  0  0   0 0 0 0  0 0 0 0",
                          "x a6 a5 a4  a3 a2 a1 a0   i i i i  i i i i";

	mode		= 0x04;
	delay		= 12;
	blocksize	= 64;
	readsize	= 256;
      ;
    memory "flash"
        size            = 2048;
        min_write_delay = 9000;
        max_write_delay = 20000;
        readback_p1     = 0xff;
        readback_p2     = 0xff;
        read_lo         = "  0   0   1   0    0   0   0   0",
                          "  x   x   x   x    x   x  a9  a8",
                          " a7  a6  a5  a4   a3  a2  a1  a0",
                          "  o   o   o   o    o   o   o   o";

        read_hi         = "  0   0   1   0    1   0   0   0",
                          "  x   x   x   x    x   x  a9  a8",
                          " a7  a6  a5  a4   a3  a2  a1  a0",
                          "  o   o   o   o    o   o   o   o";

        write_lo        = "  0   1   0   0    0   0   0   0",
                          "  x   x   x   x    x   x  a9  a8",
                          " a7  a6  a5  a4   a3  a2  a1  a0",
                          "  i   i   i   i    i   i   i   i";

        write_hi        = "  0   1   0   0    1   0   0   0",
                          "  x   x   x   x    x   x  a9  a8",
                          " a7  a6  a5  a4   a3  a2  a1  a0",
                          "  i   i   i   i    i   i   i   i";

	mode		= 0x04;
	delay		= 12;
	blocksize	= 128;
	readsize	= 128;
      ;
    memory "signature"
        size            = 3;
        read            = "0  0  1  1   0  0  0  0   x  x  x  x   x  x  x  x",
                          "x  x  x  x   x  x a1 a0   o  o  o  o   o  o  o  o";
      ;
    memory "fuse"
        size            = 1;
        min_write_delay = 9000;
        max_write_delay = 20000;
        read            = "0 1 0 1  1 0 0 0   x x x x  x x x x",
                          "x x x x  x x x x   o o o x  x x x o";

        write           = "1 0 1 0  1 1 0 0   1 0 1 1  1 1 1 i",
                          "x x x x  x x x x   x x x x  x x x x";
      ;
    memory "lock"
        size            = 1;
        min_write_delay = 9000;
        max_write_delay = 20000;
        read            = "0 1 0 1  1 0 0 0   x x x x  x x x x",
                          "x x x x  x x x x   o o o x  x x x o";

        write           = "1 0 1 0  1 1 0 0   1 1 1 1  1 i i 1",
                          "x x x x  x x x x   x x x x  x x x x";
      ;
  ;


#------------------------------------------------------------
# AT90s4433
#------------------------------------------------------------

part
    id               = "4433";
    desc             = "AT90S4433";
    stk500_devcode   = 0x51;
    avr910_devcode   = 0x30;
    signature        = 0x1e 0x92 0x03;
    chip_erase_delay = 20000;
    pgm_enable       = "1 0 1 0  1 1 0 0    0 1 0 1  0 0 1 1",
                       "x x x x  x x x x    x x x x  x x x x";

    chip_erase       = "1 0 1 0  1 1 0 0    1 0 0 0  0 0 0 0",
                       "x x x x  x x x x    x x x x  x x x x";

    timeout		= 200;
    stabdelay		= 100;
    cmdexedelay		= 25;
    synchloops		= 32;
    bytedelay		= 0;
    pollindex		= 3;
    pollvalue		= 0x53;
    predelay		= 1;
    postdelay		= 1;
    pollmethod		= 0;

    pp_controlstack     =
        0x0E, 0x1E, 0x0F, 0x1F, 0x2E, 0x3E, 0x2F, 0x3F,
        0x4E, 0x5E, 0x4F, 0x5F, 0x6E, 0x7E, 0x6F, 0x7F,
        0x66, 0x76, 0x67, 0x77, 0x6A, 0x7A, 0x6B, 0x7B,
        0xBE, 0xFD, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00;
    hventerstabdelay    = 100;
    progmodedelay       = 0;
    latchcycles         = 0;
    togglevtg           = 0;
    poweroffdelay       = 0;
    resetdelayms        = 0;
    resetdelayus        = 0;
    hvleavestabdelay    = 15;
    chiperasepulsewidth = 15;
    chiperasepolltimeout = 0;
    programfusepulsewidth = 2;
    programfusepolltimeout = 0;
    programlockpulsewidth = 0;
    programlockpolltimeout = 1;

    memory "eeprom"
        size            = 256;
        min_write_delay = 9000;
        max_write_delay = 20000;
        readback_p1     = 0x00;
        readback_p2     = 0xff;
        read            = " 1  0  1  0   0  0  0  0   x x x x  x x x x", 
                          "a7 a6 a5 a4  a3 a2 a1 a0   o o o o  o o o o";

        write           = " 1  1  0  0   0  0  0  0   x x x x  x x x x",
                          "a7 a6 a5 a4  a3 a2 a1 a0   i i i i  i i i i";

	mode		= 0x04;
	delay		= 12;
	blocksize	= 128;
	readsize	= 256;
      ;
    memory "flash"
        size            = 4096;
        min_write_delay = 9000;
        max_write_delay = 20000;
        readback_p1     = 0xff;
        readback_p2     = 0xff;
        read_lo         = "  0   0   1   0    0   0   0   0",
                          "  x   x   x   x    x a10  a9  a8",
                          " a7  a6  a5  a4   a3  a2  a1  a0",
                          "  o   o   o   o    o   o   o   o";

        read_hi         = "  0   0   1   0    1   0   0   0",
                          "  x   x   x   x    x a10  a9  a8",
                          " a7  a6  a5  a4   a3  a2  a1  a0",
                          "  o   o   o   o    o   o   o   o";

        write_lo        = "  0   1   0   0    0   0   0   0",
                          "  x   x   x   x    x a10  a9  a8",
                          " a7  a6  a5  a4   a3  a2  a1  a0",
                          "  i   i   i   i    i   i   i   i";

        write_hi        = "  0   1   0   0    1   0   0   0",
                          "  x   x   x   x    x a10  a9  a8",
                          " a7  a6  a5  a4   a3  a2  a1  a0",
                          "  i   i   i   i    i   i   i   i";

	mode		= 0x04;
	delay		= 12;
	blocksize	= 128;
	readsize	= 256;
      ;
    memory "signature"
        size            = 3;
        read            = "0  0  1  1   0  0  0  0   x  x  x  x   x  x  x  x",
                          "x  x  x  x   x  x a1 a0   o  o  o  o   o  o  o  o";
      ;
    memory "fuse"
        size            = 1;
        min_write_delay = 9000;
        max_write_delay = 20000;
        pwroff_after_write = yes;
        read            = "0 1 0 1  0 0 0 0   x x x x  x x x x",
                          "x x x x  x x x x   x x o o  o o o o";

        write           = "1 0 1 0  1 1 0 0   1 0 1 i  i i i i",
                          "x x x x  x x x x   x x x x  x x x x";
      ;
    memory "lock"
        size            = 1;
        min_write_delay = 9000;
        max_write_delay = 20000;
        read            = "0 1 0 1  1 0 0 0   x x x x  x x x x",
                          "x x x x  x x x x   x x x x  x o o x";

        write           = "1 0 1 0  1 1 0 0   1 1 1 1  1 i i 1",
                          "x x x x  x x x x   x x x x  x x x x";
      ;
  ;

#------------------------------------------------------------
# AT90s4434
#------------------------------------------------------------

part
    id               = "4434";
##### WARNING: No XML file for device 'AT90S4434'! #####
    desc             = "AT90S4434";
    stk500_devcode   = 0x52;
    avr910_devcode   = 0x6c;
    signature        = 0x1e 0x92 0x02;
    chip_erase_delay = 20000;
    pgm_enable       = "1 0 1 0  1 1 0 0    0 1 0 1  0 0 1 1",
                       "x x x x  x x x x    x x x x  x x x x";

    chip_erase       = "1 0 1 0  1 1 0 0    1 0 0 0  0 0 0 0",
                       "x x x x  x x x x    x x x x  x x x x";

    memory "eeprom"
        size            = 256;
        min_write_delay = 9000;
        max_write_delay = 20000;
        readback_p1     = 0x00;
        readback_p2     = 0xff;
        read            = " 1  0  1  0   0  0  0  0   x x x x  x x x x", 
                          "a7 a6 a5 a4  a3 a2 a1 a0   o o o o  o o o o";

        write           = " 1  1  0  0   0  0  0  0   x x x x  x x x x",
                          "a7 a6 a5 a4  a3 a2 a1 a0   i i i i  i i i i";
      ;
    memory "flash"
        size            = 4096;
        min_write_delay = 9000;
        max_write_delay = 20000;
        readback_p1     = 0xff;
        readback_p2     = 0xff;
        read_lo         = "  0   0   1   0    0   0   0   0",
                          "  x   x   x   x    x a10  a9  a8",
                          " a7  a6  a5  a4   a3  a2  a1  a0",
                          "  o   o   o   o    o   o   o   o";

        read_hi         = "  0   0   1   0    1   0   0   0",
                          "  x   x   x   x    x a10  a9  a8",
                          " a7  a6  a5  a4   a3  a2  a1  a0",
                          "  o   o   o   o    o   o   o   o";

        write_lo        = "  0   1   0   0    0   0   0   0",
                          "  x   x   x   x    x a10  a9  a8",
                          " a7  a6  a5  a4   a3  a2  a1  a0",
                          "  i   i   i   i    i   i   i   i";

        write_hi        = "  0   1   0   0    1   0   0   0",
                          "  x   x   x   x    x a10  a9  a8",
                          " a7  a6  a5  a4   a3  a2  a1  a0",
                          "  i   i   i   i    i   i   i   i";
      ;
    memory "signature"
        size            = 3;
        read            = "0  0  1  1   0  0  0  0   x  x  x  x   x  x  x  x",
                          "x  x  x  x   x  x a1 a0   o  o  o  o   o  o  o  o";
      ;
    memory "fuse"
        size            = 1;
        min_write_delay = 9000;
        max_write_delay = 20000;
        read            = "0 1 0 1  0 0 0 0   x x x x  x x x x",
                          "x x x x  x x x x   x x o o  o o o o";

        write           = "1 0 1 0  1 1 0 0   1 0 1 i  i i i i",
                          "x x x x  x x x x   x x x x  x x x x";
      ;
    memory "lock"
        size            = 1;
        min_write_delay = 9000;
        max_write_delay = 20000;
        read            = "0 1 0 1  1 0 0 0   x x x x  x x x x",
                          "x x x x  x x x x   x x x x  x o o x";

        write           = "1 0 1 0  1 1 0 0   1 1 1 1  1 i i 1",
                          "x x x x  x x x x   x x x x  x x x x";
      ;
  ;

#------------------------------------------------------------
# AT90s8515
#------------------------------------------------------------

part
    id               = "8515";
    desc             = "AT90S8515";
    stk500_devcode   = 0x60;
    avr910_devcode   = 0x38;
    signature        = 0x1e 0x93 0x01;
    chip_erase_delay = 20000;
    pgm_enable       = "1 0 1 0  1 1 0 0    0 1 0 1  0 0 1 1",
                       "x x x x  x x x x    x x x x  x x x x";

    chip_erase       = "1 0 1 0  1 1 0 0    1 0 0 x  x x x x",
                       "x x x x  x x x x    x x x x  x x x x";

    timeout		= 200;
    stabdelay		= 100;
    cmdexedelay		= 25;
    synchloops		= 32;
    bytedelay		= 0;
    pollindex		= 3;
    pollvalue		= 0x53;
    predelay		= 1;
    postdelay		= 1;
    pollmethod		= 0;

    pp_controlstack     =
	0x0E, 0x1E, 0x0F, 0x1F, 0x2E, 0x3E, 0x2F, 0x3F,
	0x4E, 0x5E, 0x4F, 0x5F, 0x6E, 0x7E, 0x6F, 0x7F,
	0x66, 0x76, 0x67, 0x77, 0x6A, 0x7A, 0x6B, 0x7B,
	0xBE, 0xFD, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00;
    hventerstabdelay    = 100;
    progmodedelay       = 0;
    latchcycles         = 0;
    togglevtg           = 0;
    poweroffdelay       = 0;
    resetdelayms        = 0;
    resetdelayus        = 0;
    hvleavestabdelay    = 15;
    resetdelay          = 15;
    chiperasepulsewidth = 15;
    chiperasepolltimeout = 0;
    programfusepulsewidth = 2;
    programfusepolltimeout = 0;
    programlockpulsewidth = 0;
    programlockpolltimeout = 1;

    memory "eeprom"
        size            = 512;
        min_write_delay = 4000;
        max_write_delay = 9000;
        readback_p1     = 0x80;
        readback_p2     = 0x7f;
        read            = " 1  0  1  0   0  0  0  0  x x x x  x x x a8", 
                          "a7 a6 a5 a4 a3 a2 a1 a0   o o o o  o o o o";

        write           = " 1  1  0  0   0  0  0  0   x x x x  x x x a8",
                          "a7 a6 a5 a4  a3 a2 a1 a0   i i i i  i i i i";

	mode		= 0x04;
	delay		= 12;
	blocksize	= 128;
	readsize	= 256;
      ;
    memory "flash"
        size            = 8192;
        min_write_delay = 4000;
        max_write_delay = 9000;
        readback_p1     = 0x7f;
        readback_p2     = 0x7f;
        read_lo         = "  0   0   1   0    0   0   0   0",
                          "  x   x   x   x  a11 a10  a9  a8",
                          " a7  a6  a5  a4   a3  a2  a1  a0",
                          "  o   o   o   o    o   o   o   o";

        read_hi         = "  0   0   1   0    1   0   0   0",
                          "  x   x   x   x  a11 a10  a9  a8",
                          " a7  a6  a5  a4   a3  a2  a1  a0",
                          "  o   o   o   o    o   o   o   o";

        write_lo        = "  0   1   0   0    0   0   0   0",
                          "  x   x   x   x  a11 a10  a9  a8",
                          " a7  a6  a5  a4   a3  a2  a1  a0",
                          "  i   i   i   i    i   i   i   i";

        write_hi        = "  0   1   0   0    1   0   0   0",
                          "  x   x   x   x  a11 a10  a9  a8",
                          " a7  a6  a5  a4   a3  a2  a1  a0",
                          "  i   i   i   i    i   i   i   i";

	mode		= 0x04;
	delay		= 12;
	blocksize	= 128;
	readsize	= 256;
      ;
    memory "signature"
        size            = 3;
        read            = "0  0  1  1   0  0  0  0   x  x  x  x   x  x  x  x",
                          "x  x  x  x   x  x a1 a0   o  o  o  o   o  o  o  o";
      ;
    memory "fuse"
	size		= 1;
      ;
    memory "lock"
	size		= 1;
	write		= "1  0  1  0   1  1  0  0   1  1  1  1   1  i  i  1",
			  "x  x  x  x   x  x  x  x   x  x  x  x   x  x  x  x";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;
  ;

#------------------------------------------------------------
# AT90s8535
#------------------------------------------------------------

part
    id               = "8535";
    desc             = "AT90S8535";
    stk500_devcode   = 0x61;
    avr910_devcode   = 0x68;
    signature        = 0x1e 0x93 0x03;
    chip_erase_delay = 20000;
    pgm_enable       = "1 0 1 0  1 1 0 0    0 1 0 1  0 0 1 1",
                       "x x x x  x x x x    x x x x  x x x x";

    chip_erase       = "1 0 1 0  1 1 0 0    1 0 0 0  0 0 0 0",
                       "x x x x  x x x x    x x x x  x x x x";

    timeout		= 200;
    stabdelay		= 100;
    cmdexedelay		= 25;
    synchloops		= 32;
    bytedelay		= 0;
    pollindex		= 3;
    pollvalue		= 0x53;
    predelay		= 1;
    postdelay		= 1;
    pollmethod		= 0;

    pp_controlstack     =
        0x0E, 0x1E, 0x0F, 0x1F, 0x2E, 0x3E, 0x2F, 0x3F,
        0x4E, 0x5E, 0x4F, 0x5F, 0x6E, 0x7E, 0x6F, 0x7F,
        0x66, 0x76, 0x67, 0x77, 0x6A, 0x7A, 0x6B, 0x7B,
        0xBE, 0xFD, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00;
    hventerstabdelay    = 100;
    progmodedelay       = 0;
    latchcycles         = 0;
    togglevtg           = 0;
    poweroffdelay       = 0;
    resetdelayms        = 0;
    resetdelayus        = 0;
    hvleavestabdelay    = 15;
    chiperasepulsewidth = 15;
    chiperasepolltimeout = 0;
    programfusepulsewidth = 2;
    programfusepolltimeout = 0;
    programlockpulsewidth = 0;
    programlockpolltimeout = 1;

    memory "eeprom"
        size            = 512;
        min_write_delay = 9000;
        max_write_delay = 20000;
        readback_p1     = 0x00;
        readback_p2     = 0xff;
        read            = " 1  0  1  0   0  0  0  0   x x x x  x x x a8", 
                          "a7 a6 a5 a4  a3 a2 a1 a0   o o o o  o o o o";

        write           = " 1  1  0  0   0  0  0  0   x x x x  x x x a8",
                          "a7 a6 a5 a4  a3 a2 a1 a0   i i i i  i i i i";

	mode		= 0x04;
	delay		= 12;
	blocksize	= 128;
	readsize	= 256;
      ;
    memory "flash"
        size            = 8192;
        min_write_delay = 9000;
        max_write_delay = 20000;
        readback_p1     = 0xff;
        readback_p2     = 0xff;
        read_lo         = "  0   0   1   0    0   0   0   0",
                          "  x   x   x   x  a11 a10  a9  a8",
                          " a7  a6  a5  a4   a3  a2  a1  a0",
                          "  o   o   o   o    o   o   o   o";

        read_hi         = "  0   0   1   0    1   0   0   0",
                          "  x   x   x   x  a11 a10  a9  a8",
                          " a7  a6  a5  a4   a3  a2  a1  a0",
                          "  o   o   o   o    o   o   o   o";

        write_lo        = "  0   1   0   0    0   0   0   0",
                          "  x   x   x   x  a11 a10  a9  a8",
                          " a7  a6  a5  a4   a3  a2  a1  a0",
                          "  i   i   i   i    i   i   i   i";

        write_hi        = "  0   1   0   0    1   0   0   0",
                          "  x   x   x   x  a11 a10  a9  a8",
                          " a7  a6  a5  a4   a3  a2  a1  a0",
                          "  i   i   i   i    i   i   i   i";

	mode		= 0x04;
	delay		= 12;
	blocksize	= 128;
	readsize	= 256;
      ;
    memory "signature"
        size            = 3;
        read            = "0  0  1  1   0  0  0  0   x  x  x  x   x  x  x  x",
                          "x  x  x  x   x  x a1 a0   o  o  o  o   o  o  o  o";
      ;
    memory "fuse"
	size		= 1;
	read		= "0  1  0  1   1  0  0  0   x  x  x  x   x  x  x  x",
			  "x  x  x  x   x  x  x  x   x  x  x  x   x  x  x  o";
	write		= "1  0  1  0   1  1  0  0   1  0  1  1   1  1  1  i",
			  "x  x  x  x   x  x  x  x   x  x  x  x   x  x  x  x";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;
    memory "lock"
	size		= 1;
	read		= "0  1  0  1   1  0  0  0   x  x  x  x   x  x  x  x",
			  "x  x  x  x   x  x  x  x   o  o  x  x   x  x  x  x";
	write		= "1  0  1  0   1  1  0  0   1  1  1  1   1  i  i  1",
			  "x  x  x  x   x  x  x  x   x  x  x  x   x  x  x  x";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;
  ;

#------------------------------------------------------------
# ATmega103
#------------------------------------------------------------

part
    id               = "m103";
    desc             = "ATmega103";
    stk500_devcode   = 0xB1;
    avr910_devcode   = 0x41;
    signature        = 0x1e 0x97 0x01;
    chip_erase_delay = 112000;
    pgm_enable       = "1 0 1 0  1 1 0 0    0 1 0 1  0 0 1 1",
                       "x x x x  x x x x    x x x x  x x x x";

    chip_erase       = "1 0 1 0  1 1 0 0    1 0 0 0  0 0 0 0",
                       "x x x x  x x x x    x x x x  x x x x";

    timeout		= 200;
    stabdelay		= 100;
    cmdexedelay		= 25;
    synchloops		= 32;
    bytedelay		= 0;
    pollindex		= 3;
    pollvalue		= 0x53;
    predelay		= 1;
    postdelay		= 1;
    pollmethod		= 0;

    pp_controlstack     =
        0x0E, 0x1E, 0x8E, 0x9E, 0x2E, 0x3E, 0xAE, 0xBE,
        0x4E, 0x5E, 0xCE, 0xDE, 0x6E, 0x7E, 0xEE, 0xDE,
        0x66, 0x76, 0xE6, 0xF6, 0x6A, 0x7A, 0xEA, 0x7A,
        0x7F, 0xFD, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00;
    hventerstabdelay    = 100;
    progmodedelay       = 0;
    latchcycles         = 0;
    togglevtg           = 0;
    poweroffdelay       = 0;
    resetdelayms        = 0;
    resetdelayus        = 0;
    hvleavestabdelay    = 15;
    chiperasepulsewidth = 15;
    chiperasepolltimeout = 0;
    programfusepulsewidth = 2;
    programfusepolltimeout = 0;
    programlockpulsewidth = 0;
    programlockpolltimeout = 10;

    memory "eeprom"
        size            = 4096;
        min_write_delay = 4000;
        max_write_delay = 9000;
        readback_p1     = 0x80;
        readback_p2     = 0x7f;
	read            = "  1   0   1   0      0   0   0   0",
                          "  x   x   x   x    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

	write           = "  1   1   0   0      0   0   0   0",
                          "  x   x   x   x    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0", 
                          "  i   i   i   i      i   i   i   i";

	mode		= 0x04;
	delay		= 12;
	blocksize	= 64;
	readsize	= 256;
      ;

    memory "flash"
        paged           = yes;
        size            = 131072;
        page_size       = 256;
        num_pages       = 512;
        min_write_delay = 22000;
        max_write_delay = 56000;
        readback_p1     = 0xff;
        readback_p2     = 0xff;
        read_lo         = "  0   0   1   0      0   0   0   0",
                          "a15 a14 a13 a12    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

        read_hi         = "  0   0   1   0      1   0   0   0",
                          "a15 a14 a13 a12    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

        loadpage_lo     = "  0   1   0   0      0   0   0   0",
                          "  x   x   x   x      x   x   x   x",
                          "  x  a6  a5  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

        loadpage_hi     = "  0   1   0   0      1   0   0   0",
                          "  x   x   x   x      x   x   x   x",
                          "  x  a6  a5  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

        writepage       = "  0   1   0   0      1   1   0   0",
                          "a15 a14 a13 a12    a11 a10  a9  a8",
                          " a7   x   x   x      x   x   x   x",
                          "  x   x   x   x      x   x   x   x";

	mode		= 0x11;
	delay		= 70;
	blocksize	= 256;
	readsize	= 256;
      ;

    memory "fuse"
        size            = 1;
        read            = "0 1 0 1  0 0 0 0  x x x x  x x x x",
                          "x x x x  x x x x  x x o x  o 1 o o";

        write           = "1 0 1 0  1 1 0 0  1 0 1 1  i 1 i i",
                          "x x x x  x x x x  x x x x  x x x x";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "lock"
        size            = 1;
        read            = "0 1 0 1  1 0 0 0   x x x x  x x x x",
                          "x x x x  x x x x   x x x x  x o o x";

        write           = "1 0 1 0  1 1 0 0   1 1 1 1  1 i i 1",
                          "x x x x  x x x x   x x x x  x x x x";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "signature"
        size            = 3;
        read            = "0  0  1  1   0  0  0  0   x  x  x  x   x  x  x  x",
                          "x  x  x  x   x  x a1 a0   o  o  o  o   o  o  o  o";
      ;
  ;


#------------------------------------------------------------
# ATmega64
#------------------------------------------------------------

part
    id               = "m64";
    desc             = "ATmega64";
    has_jtag         = yes;
    stk500_devcode   = 0xA0;
    avr910_devcode   = 0x45;
    signature        = 0x1e 0x96 0x02;
    chip_erase_delay = 9000;
    pagel            = 0xD7;
    bs2              = 0xA0;
    reset            = dedicated;
    pgm_enable       = "1 0 1 0  1 1 0 0    0 1 0 1  0 0 1 1",
                       "x x x x  x x x x    x x x x  x x x x";

    chip_erase       = "1 0 1 0  1 1 0 0    1 0 0 0  0 0 0 0",
                       "x x x x  x x x x    x x x x  x x x x";

    timeout		= 200;
    stabdelay		= 100;
    cmdexedelay		= 25;
    synchloops		= 32;
    bytedelay		= 0;
    pollindex		= 3;
    pollvalue		= 0x53;
    predelay		= 1;
    postdelay		= 1;
    pollmethod		= 0;

    pp_controlstack     =
        0x0E, 0x1E, 0x0F, 0x1F, 0x2E, 0x3E, 0x2F, 0x3F,
        0x4E, 0x5E, 0x4F, 0x5F, 0x6E, 0x7E, 0x6F, 0x7F,
        0x66, 0x76, 0x67, 0x77, 0x6A, 0x7A, 0x6B, 0x7B,
        0xBE, 0xFD, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00;
    hventerstabdelay    = 100;
    progmodedelay       = 0;
    latchcycles         = 6;
    togglevtg           = 0;
    poweroffdelay       = 0;
    resetdelayms        = 0;
    resetdelayus        = 0;
    hvleavestabdelay    = 15;
    chiperasepulsewidth = 0;
    chiperasepolltimeout = 10;
    programfusepulsewidth = 0;
    programfusepolltimeout = 5;
    programlockpulsewidth = 0;
    programlockpolltimeout = 5;

    idr                 = 0x22;
    spmcr               = 0x68;
    allowfullpagebitstream = yes;

    ocdrev              = 2;

    memory "eeprom"
        paged           = no; /* leave this "no" */
        page_size       = 8;  /* for parallel programming */
        size            = 2048;
        min_write_delay = 9000;
        max_write_delay = 9000;
        readback_p1     = 0xff;
        readback_p2     = 0xff;
        read            = "  1   0   1   0      0   0   0   0",
                          "  x   x   x   x    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

        write           = "  1   1   0   0      0   0   0   0",
                          "  x   x   x   x    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0", 
                          "  i   i   i   i      i   i   i   i";

	mode		= 0x04;
	delay		= 20;
	blocksize	= 64;
	readsize	= 256;
      ;

    memory "flash"
        paged           = yes;
        size            = 65536;
        page_size       = 256;
        num_pages       = 256;
        min_write_delay = 4500;
        max_write_delay = 4500;
        readback_p1     = 0xff;
        readback_p2     = 0xff;
        read_lo         = "  0   0   1   0      0   0   0   0",
                          "  x a14 a13 a12    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

        read_hi         = "  0   0   1   0      1   0   0   0",
                          "  x a14 a13 a12    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";


        loadpage_lo     = "  0   1   0   0      0   0   0   0",
                          "  x   x   x   x      x   x   x   x",
                          "  x  a6  a5  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

        loadpage_hi     = "  0   1   0   0      1   0   0   0",
                          "  x   x   x   x      x   x   x   x",
                          "  x  a6  a5  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

        writepage       = "  0   1   0   0      1   1   0   0",
                          "  x a14 a13 a12    a11 a10  a9  a8",
                          " a7   x   x   x      x   x   x   x",
                          "  x   x   x   x      x   x   x   x";

	mode		= 0x21;
	delay		= 6;
	blocksize	= 128;
	readsize	= 256;
      ;

    memory "lfuse"
        size            = 1;
        write           = "1 0 1 0  1 1 0 0  1 0 1 0  0 0 0 0",
                          "x x x x  x x x x  i i i i  i i i i";

        read            = "0 1 0 1  0 0 0 0  0 0 0 0  0 0 0 0",
                          "x x x x  x x x x  o o o o  o o o o";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "hfuse"
        size            = 1;
        write           = "1 0 1 0  1 1 0 0  1 0 1 0  1 0 0 0",
                          "x x x x  x x x x  i i i i  i i i i";

        read            = "0 1 0 1  1 0 0 0  0 0 0 0  1 0 0 0",
                          "x x x x  x x x x  o o o o  o o o o";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "efuse"
        size            = 1;
        write           = "1 0 1 0  1 1 0 0  1 0 1 0  0 1 0 0",
                          "x x x x  x x x x  x x x x  x x i i";

        read            = "0 1 0 1  0 0 0 0  0 0 0 0  1 0 0 0",
                          "x x x x  x x x x  o o o o  o o o o";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "lock"
        size            = 1;
        read            = "0 1 0 1  1 0 0 0   0 0 0 0  0 0 0 0",
                          "x x x x  x x x x   x x o o  o o o o";

        write           = "1 0 1 0  1 1 0 0   1 1 1 x  x x x x",
                          "x x x x  x x x x   1 1 i i  i i i i";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "calibration"
        size            = 4;
        read            = "0 0 1 1  1 0 0 0    x x x x  x x x x",
                          "0 0 0 0  0 0 a1 a0  o o o o  o o o o";
      ;

    memory "signature"
        size            = 3;
        read            = "0  0  1  1   0  0  0  0   x  x  x  x   x  x  x  x",
                          "x  x  x  x   x  x a1 a0   o  o  o  o   o  o  o  o";
      ;
  ;




#------------------------------------------------------------
# ATmega128
#------------------------------------------------------------

part
    id               = "m128";
    desc             = "ATmega128";
    has_jtag         = yes;
    stk500_devcode   = 0xB2;
    avr910_devcode   = 0x43;
    signature        = 0x1e 0x97 0x02;
    chip_erase_delay = 9000;
    pagel            = 0xD7;
    bs2              = 0xA0;
    reset            = dedicated;
    pgm_enable       = "1 0 1 0  1 1 0 0    0 1 0 1  0 0 1 1",
                       "x x x x  x x x x    x x x x  x x x x";

    chip_erase       = "1 0 1 0  1 1 0 0    1 0 0 0  0 0 0 0",
                       "x x x x  x x x x    x x x x  x x x x";

    timeout		= 200;
    stabdelay		= 100;
    cmdexedelay		= 25;
    synchloops		= 32;
    bytedelay		= 0;
    pollindex		= 3;
    pollvalue		= 0x53;
    predelay		= 1;
    postdelay		= 1;
    pollmethod		= 0;

    pp_controlstack     =
        0x0E, 0x1E, 0x0F, 0x1F, 0x2E, 0x3E, 0x2F, 0x3F,
        0x4E, 0x5E, 0x4F, 0x5F, 0x6E, 0x7E, 0x6F, 0x7F,
        0x66, 0x76, 0x67, 0x77, 0x6A, 0x7A, 0x6B, 0x7B,
        0xBE, 0xFD, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00;
    hventerstabdelay    = 100;
    progmodedelay       = 0;
    latchcycles         = 6;
    togglevtg           = 0;
    poweroffdelay       = 0;
    resetdelayms        = 0;
    resetdelayus        = 0;
    hvleavestabdelay    = 15;
    chiperasepulsewidth = 0;
    chiperasepolltimeout = 10;
    programfusepulsewidth = 0;
    programfusepolltimeout = 5;
    programlockpulsewidth = 0;
    programlockpolltimeout = 5;

    idr                 = 0x22;
    spmcr               = 0x68;
    rampz               = 0x3b;
    allowfullpagebitstream = yes;

    ocdrev              = 1;

    memory "eeprom"
        paged           = no; /* leave this "no" */
        page_size       = 8;  /* for parallel programming */
        size            = 4096;
        min_write_delay = 9000;
        max_write_delay = 9000;
        readback_p1     = 0xff;
        readback_p2     = 0xff;
        read            = "  1   0   1   0      0   0   0   0",
                          "  x   x   x   x    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

        write           = "  1   1   0   0      0   0   0   0",
                          "  x   x   x   x    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0", 
                          "  i   i   i   i      i   i   i   i";

	mode		= 0x04;
	delay		= 12;
	blocksize	= 64;
	readsize	= 256;
      ;

    memory "flash"
        paged           = yes;
        size            = 131072;
        page_size       = 256;
        num_pages       = 512;
        min_write_delay = 4500;
        max_write_delay = 4500;
        readback_p1     = 0xff;
        readback_p2     = 0xff;
        read_lo         = "  0   0   1   0      0   0   0   0",
                          "a15 a14 a13 a12    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

        read_hi         = "  0   0   1   0      1   0   0   0",
                          "a15 a14 a13 a12    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

        loadpage_lo     = "  0   1   0   0      0   0   0   0",
                          "  x   x   x   x      x   x   x   x",
                          "  x  a6  a5  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

        loadpage_hi     = "  0   1   0   0      1   0   0   0",
                          "  x   x   x   x      x   x   x   x",
                          "  x  a6  a5  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

        writepage       = "  0   1   0   0      1   1   0   0",
                          "a15 a14 a13 a12    a11 a10  a9  a8",
                          " a7   x   x   x      x   x   x   x",
                          "  x   x   x   x      x   x   x   x";

	mode		= 0x21;
	delay		= 6;
	blocksize	= 128;
	readsize	= 256;
      ;

    memory "lfuse"
        size            = 1;
        write           = "1 0 1 0  1 1 0 0  1 0 1 0  0 0 0 0",
                          "x x x x  x x x x  i i i i  i i i i";

        read            = "0 1 0 1  0 0 0 0  0 0 0 0  0 0 0 0",
                          "x x x x  x x x x  o o o o  o o o o";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "hfuse"
        size            = 1;
        write           = "1 0 1 0  1 1 0 0  1 0 1 0  1 0 0 0",
                          "x x x x  x x x x  i i i i  i i i i";

        read            = "0 1 0 1  1 0 0 0  0 0 0 0  1 0 0 0",
                          "x x x x  x x x x  o o o o  o o o o";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "efuse"
        size            = 1;
        write           = "1 0 1 0  1 1 0 0  1 0 1 0  0 1 0 0",
                          "x x x x  x x x x  x x x x  x x i i";

        read            = "0 1 0 1  0 0 0 0  0 0 0 0  1 0 0 0",
                          "x x x x  x x x x  o o o o  o o o o";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "lock"
        size            = 1;
        read            = "0 1 0 1  1 0 0 0   0 0 0 0  0 0 0 0",
                          "x x x x  x x x x   x x o o  o o o o";

        write           = "1 0 1 0  1 1 0 0   1 1 1 x  x x x x",
                          "x x x x  x x x x   1 1 i i  i i i i";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "calibration"
        size            = 4;
        read            = "0 0 1 1  1 0 0 0    x x x x  x x x x",
                          "0 0 0 0  0 0 a1 a0  o o o o  o o o o";
      ;

    memory "signature"
        size            = 3;
        read            = "0  0  1  1   0  0  0  0   x  x  x  x   x  x  x  x",
                          "x  x  x  x   x  x a1 a0   o  o  o  o   o  o  o  o";
      ;
  ;

#------------------------------------------------------------
# AT90CAN128
#------------------------------------------------------------

part
    id               = "c128";
    desc             = "AT90CAN128";
    has_jtag         = yes;
    stk500_devcode   = 0xB3;
#    avr910_devcode   = 0x43;
    signature        = 0x1e 0x97 0x81;
    chip_erase_delay = 9000;
    pagel            = 0xD7;
    bs2              = 0xA0;
    reset            = dedicated;
    pgm_enable       = "1 0 1 0  1 1 0 0    0 1 0 1  0 0 1 1",
                       "x x x x  x x x x    x x x x  x x x x";

    chip_erase       = "1 0 1 0  1 1 0 0    1 0 0 x  x x x x",
                       "x x x x  x x x x    x x x x  x x x x";

    timeout		= 200;
    stabdelay		= 100;
    cmdexedelay		= 25;
    synchloops		= 32;
    bytedelay		= 0;
    pollindex		= 3;
    pollvalue		= 0x53;
    predelay		= 1;
    postdelay		= 1;
    pollmethod		= 1;

    pp_controlstack     =
        0x0E, 0x1E, 0x0F, 0x1F, 0x2E, 0x3E, 0x2F, 0x3F,
        0x4E, 0x5E, 0x4F, 0x5F, 0x6E, 0x7E, 0x6F, 0x7F,
        0x66, 0x76, 0x67, 0x77, 0x6A, 0x7A, 0x6B, 0x7B,
        0xBE, 0xFD, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01;
    hventerstabdelay    = 100;
    progmodedelay       = 0;
    latchcycles         = 6;
    togglevtg           = 0;
    poweroffdelay       = 0;
    resetdelayms        = 0;
    resetdelayus        = 0;
    hvleavestabdelay    = 15;
    chiperasepulsewidth = 0;
    chiperasepolltimeout = 10;
    programfusepulsewidth = 0;
    programfusepolltimeout = 5;
    programlockpulsewidth = 0;
    programlockpolltimeout = 5;

    idr                 = 0x31;
    spmcr               = 0x57;
    rampz               = 0x3b;
    eecr                = 0x3f;
    allowfullpagebitstream = no;

    ocdrev              = 3;

    memory "eeprom"
        paged           = no; /* leave this "no" */
        page_size       = 8;  /* for parallel programming */
        size            = 4096;
        min_write_delay = 9000;
        max_write_delay = 9000;
        readback_p1     = 0xff;
        readback_p2     = 0xff;
	read            = "  1   0   1   0      0   0   0   0",
                          "  0   0   0   x    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

	write           = "  1   1   0   0      0   0   0   0",
                          "  0   0   0   x    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0", 
                          "  i   i   i   i      i   i   i   i";

	loadpage_lo	= "  1   1   0   0      0   0   0   1",
			  "  0   0   0   0      0   0   0   0",
			  "  0   0   0   0      0  a2  a1  a0",
			  "  i   i   i   i      i   i   i   i";

	writepage	= "  1   1   0   0      0   0   1   0",
			  "  0   0   x   x    a11 a10  a9  a8",
			  " a7  a6  a5  a4     a3   0   0   0",
			  "  x   x   x   x      x   x   x   x";


	mode		= 0x41;
	delay		= 20;
	blocksize	= 8;
	readsize	= 256;
      ;

    memory "flash"
        paged           = yes;
        size            = 131072;
        page_size       = 256;
        num_pages       = 512;
        min_write_delay = 4500;
        max_write_delay = 4500;
        readback_p1     = 0xff;
        readback_p2     = 0xff;
        read_lo         = "  0   0   1   0      0   0   0   0",
                          "a15 a14 a13 a12    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

        read_hi         = "  0   0   1   0      1   0   0   0",
                          "a15 a14 a13 a12    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

        loadpage_lo     = "  0   1   0   0      0   0   0   0",
                          "  0   0   0   x      x   x   x   x",
                          "  x  a6  a5  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

        loadpage_hi     = "  0   1   0   0      1   0   0   0",
                          "  0   0   0   x      x   x   x   x",
                          "  x  a6  a5  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

        writepage       = "  0   1   0   0      1   1   0   0",
                          "a15 a14 a13 a12    a11 a10  a9  a8",
                          " a7   x   x   x      x   x   x   x",
                          "  x   x   x   x      x   x   x   x";

	mode		= 0x41;
	delay		= 6;
	blocksize	= 256;
	readsize	= 256;
      ;

    memory "lfuse"
        size            = 1;
        write           = "1 0 1 0  1 1 0 0  1 0 1 0  0 0 0 0",
                          "x x x x  x x x x  i i i i  i i i i";

        read            = "0 1 0 1  0 0 0 0  0 0 0 0  0 0 0 0",
                          "x x x x  x x x x  o o o o  o o o o";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "hfuse"
        size            = 1;
        write           = "1 0 1 0  1 1 0 0  1 0 1 0  1 0 0 0",
                          "x x x x  x x x x  i i i i  i i i i";

        read            = "0 1 0 1  1 0 0 0  0 0 0 0  1 0 0 0",
                          "x x x x  x x x x  o o o o  o o o o";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "efuse"
        size            = 1;
        write           = "1 0 1 0  1 1 0 0  1 0 1 0  0 1 0 0",
                          "x x x x  x x x x  x x x x  i i i i";

        read            = "0 1 0 1  0 0 0 0  0 0 0 0  1 0 0 0",
                          "x x x x  x x x x  o o o o  o o o o";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "lock"
        size            = 1;
        read            = "0 1 0 1  1 0 0 0   0 0 0 0  0 0 0 0",
                          "x x x x  x x x x   x x o o  o o o o";

        write           = "1 0 1 0  1 1 0 0   1 1 1 x  x x x x",
                          "x x x x  x x x x   1 1 i i  i i i i";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "calibration"
        size            = 1;
        read            = "0 0 1 1  1 0 0 0  0 0 0 x  x x x x",
                          "0 0 0 0  0 0 0 0  o o o o  o o o o";
      ;

    memory "signature"
        size            = 3;
        read            = "0  0  1  1   0  0  0  0   x  x  x  x   x  x  x  x",
                          "x  x  x  x   x  x a1 a0   o  o  o  o   o  o  o  o";
      ;
  ;

#------------------------------------------------------------
# AT90CAN64
#------------------------------------------------------------

part
    id               = "c64";
    desc             = "AT90CAN64";
    has_jtag         = yes;
    stk500_devcode   = 0xB3;
#    avr910_devcode   = 0x43;
    signature        = 0x1e 0x96 0x81;
    chip_erase_delay = 9000;
    pagel            = 0xD7;
    bs2              = 0xA0;
    reset            = dedicated;
    pgm_enable       = "1 0 1 0  1 1 0 0    0 1 0 1  0 0 1 1",
                       "x x x x  x x x x    x x x x  x x x x";

    chip_erase       = "1 0 1 0  1 1 0 0    1 0 0 x  x x x x",
                       "x x x x  x x x x    x x x x  x x x x";

    timeout		= 200;
    stabdelay		= 100;
    cmdexedelay		= 25;
    synchloops		= 32;
    bytedelay		= 0;
    pollindex		= 3;
    pollvalue		= 0x53;
    predelay		= 1;
    postdelay		= 1;
    pollmethod		= 1;

    pp_controlstack     =
        0x0E, 0x1E, 0x0F, 0x1F, 0x2E, 0x3E, 0x2F, 0x3F,
        0x4E, 0x5E, 0x4F, 0x5F, 0x6E, 0x7E, 0x6F, 0x7F,
        0x66, 0x76, 0x67, 0x77, 0x6A, 0x7A, 0x6B, 0x7B,
        0xBE, 0xFD, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01;
    hventerstabdelay    = 100;
    progmodedelay       = 0;
    latchcycles         = 6;
    togglevtg           = 0;
    poweroffdelay       = 0;
    resetdelayms        = 0;
    resetdelayus        = 0;
    hvleavestabdelay    = 15;
    chiperasepulsewidth = 0;
    chiperasepolltimeout = 10;
    programfusepulsewidth = 0;
    programfusepolltimeout = 5;
    programlockpulsewidth = 0;
    programlockpolltimeout = 5;

    idr                 = 0x31;
    spmcr               = 0x57;
    rampz               = 0x3b;
    eecr                = 0x3f;
    allowfullpagebitstream = no;

    ocdrev              = 3;

    memory "eeprom"
        paged           = no; /* leave this "no" */
        page_size       = 8;  /* for parallel programming */
        size            = 2048;
        min_write_delay = 9000;
        max_write_delay = 9000;
        readback_p1     = 0xff;
        readback_p2     = 0xff;
	read            = "  1   0   1   0      0   0   0   0",
                          "  0   0   0   x      x a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

	write           = "  1   1   0   0      0   0   0   0",
                          "  0   0   0   x      x a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

	loadpage_lo	= "  1   1   0   0      0   0   0   1",
			  "  0   0   0   0      0   0   0   0",
			  "  0   0   0   0      0  a2  a1  a0",
			  "  i   i   i   i      i   i   i   i";

	writepage	= "  1   1   0   0      0   0   1   0",
			  "  0   0   x   x      x a10  a9  a8",
			  " a7  a6  a5  a4     a3   0   0   0",
			  "  x   x   x   x      x   x   x   x";


	mode		= 0x41;
	delay		= 20;
	blocksize	= 8;
	readsize	= 256;
      ;

    memory "flash"
        paged           = yes;
        size            = 65536;
        page_size       = 256;
        num_pages       = 256;
        min_write_delay = 4500;
        max_write_delay = 4500;
        readback_p1     = 0xff;
        readback_p2     = 0xff;
        read_lo         = "  0   0   1   0      0   0   0   0",
                          "a15 a14 a13 a12    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

        read_hi         = "  0   0   1   0      1   0   0   0",
                          "a15 a14 a13 a12    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

        loadpage_lo     = "  0   1   0   0      0   0   0   0",
                          "  0   0   0   x      x   x   x   x",
                          "  x  a6  a5  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

        loadpage_hi     = "  0   1   0   0      1   0   0   0",
                          "  0   0   0   x      x   x   x   x",
                          "  x  a6  a5  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

        writepage       = "  0   1   0   0      1   1   0   0",
                          "a15 a14 a13 a12    a11 a10  a9  a8",
                          " a7   x   x   x      x   x   x   x",
                          "  x   x   x   x      x   x   x   x";

	mode		= 0x41;
	delay		= 6;
	blocksize	= 256;
	readsize	= 256;
      ;

    memory "lfuse"
        size            = 1;
        write           = "1 0 1 0  1 1 0 0  1 0 1 0  0 0 0 0",
                          "x x x x  x x x x  i i i i  i i i i";

        read            = "0 1 0 1  0 0 0 0  0 0 0 0  0 0 0 0",
                          "x x x x  x x x x  o o o o  o o o o";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "hfuse"
        size            = 1;
        write           = "1 0 1 0  1 1 0 0  1 0 1 0  1 0 0 0",
                          "x x x x  x x x x  i i i i  i i i i";

        read            = "0 1 0 1  1 0 0 0  0 0 0 0  1 0 0 0",
                          "x x x x  x x x x  o o o o  o o o o";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "efuse"
        size            = 1;
        write           = "1 0 1 0  1 1 0 0  1 0 1 0  0 1 0 0",
                          "x x x x  x x x x  x x x x  i i i i";

        read            = "0 1 0 1  0 0 0 0  0 0 0 0  1 0 0 0",
                          "x x x x  x x x x  o o o o  o o o o";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "lock"
        size            = 1;
        read            = "0 1 0 1  1 0 0 0   0 0 0 0  0 0 0 0",
                          "x x x x  x x x x   x x o o  o o o o";

        write           = "1 0 1 0  1 1 0 0   1 1 1 x  x x x x",
                          "x x x x  x x x x   1 1 i i  i i i i";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "calibration"
        size            = 1;
        read            = "0 0 1 1  1 0 0 0  0 0 0 x  x x x x",
                          "0 0 0 0  0 0 0 0  o o o o  o o o o";
      ;

    memory "signature"
        size            = 3;
        read            = "0  0  1  1   0  0  0  0   x  x  x  x   x  x  x  x",
                          "x  x  x  x   x  x a1 a0   o  o  o  o   o  o  o  o";
      ;
  ;

#------------------------------------------------------------
# AT90CAN32
#------------------------------------------------------------

part
    id               = "c32";
    desc             = "AT90CAN32";
    has_jtag         = yes;
    stk500_devcode   = 0xB3;
#    avr910_devcode   = 0x43;
    signature        = 0x1e 0x95 0x81;
    chip_erase_delay = 9000;
    pagel            = 0xD7;
    bs2              = 0xA0;
    reset            = dedicated;
    pgm_enable       = "1 0 1 0  1 1 0 0    0 1 0 1  0 0 1 1",
                       "x x x x  x x x x    x x x x  x x x x";

    chip_erase       = "1 0 1 0  1 1 0 0    1 0 0 x  x x x x",
                       "x x x x  x x x x    x x x x  x x x x";

    timeout		= 200;
    stabdelay		= 100;
    cmdexedelay		= 25;
    synchloops		= 32;
    bytedelay		= 0;
    pollindex		= 3;
    pollvalue		= 0x53;
    predelay		= 1;
    postdelay		= 1;
    pollmethod		= 1;

    pp_controlstack     =
        0x0E, 0x1E, 0x0F, 0x1F, 0x2E, 0x3E, 0x2F, 0x3F,
        0x4E, 0x5E, 0x4F, 0x5F, 0x6E, 0x7E, 0x6F, 0x7F,
        0x66, 0x76, 0x67, 0x77, 0x6A, 0x7A, 0x6B, 0x7B,
        0xBE, 0xFD, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01;
    hventerstabdelay    = 100;
    progmodedelay       = 0;
    latchcycles         = 6;
    togglevtg           = 0;
    poweroffdelay       = 0;
    resetdelayms        = 0;
    resetdelayus        = 0;
    hvleavestabdelay    = 15;
    chiperasepulsewidth = 0;
    chiperasepolltimeout = 10;
    programfusepulsewidth = 0;
    programfusepolltimeout = 5;
    programlockpulsewidth = 0;
    programlockpolltimeout = 5;

    idr                 = 0x31;
    spmcr               = 0x57;
    rampz               = 0x3b;
    eecr                = 0x3f;
    allowfullpagebitstream = no;

    ocdrev              = 3;

    memory "eeprom"
        paged           = no; /* leave this "no" */
        page_size       = 8;  /* for parallel programming */
        size            = 1024;
        min_write_delay = 9000;
        max_write_delay = 9000;
        readback_p1     = 0xff;
        readback_p2     = 0xff;
	read            = "  1   0   1   0      0   0   0   0",
                          "  0   0   0   x      x   x  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

	write           = "  1   1   0   0      0   0   0   0",
                          "  0   0   0   x      x   x  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

	loadpage_lo	= "  1   1   0   0      0   0   0   1",
			  "  0   0   0   0      0   0   0   0",
			  "  0   0   0   0      0  a2  a1  a0",
			  "  i   i   i   i      i   i   i   i";

	writepage	= "  1   1   0   0      0   0   1   0",
			  "  0   0   x   x      x   x  a9  a8",
			  " a7  a6  a5  a4     a3   0   0   0",
			  "  x   x   x   x      x   x   x   x";


	mode		= 0x41;
	delay		= 20;
	blocksize	= 8;
	readsize	= 256;
      ;

    memory "flash"
        paged           = yes;
        size            = 32768;
        page_size       = 256;
        num_pages       = 128;
        min_write_delay = 4500;
        max_write_delay = 4500;
        readback_p1     = 0xff;
        readback_p2     = 0xff;
        read_lo         = "  0   0   1   0      0   0   0   0",
                          "a15 a14 a13 a12    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

        read_hi         = "  0   0   1   0      1   0   0   0",
                          "a15 a14 a13 a12    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

        loadpage_lo     = "  0   1   0   0      0   0   0   0",
                          "  0   0   0   x      x   x   x   x",
                          "  x  a6  a5  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

        loadpage_hi     = "  0   1   0   0      1   0   0   0",
                          "  0   0   0   x      x   x   x   x",
                          "  x  a6  a5  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

        writepage       = "  0   1   0   0      1   1   0   0",
                          "a15 a14 a13 a12    a11 a10  a9  a8",
                          " a7   x   x   x      x   x   x   x",
                          "  x   x   x   x      x   x   x   x";

	mode		= 0x41;
	delay		= 6;
	blocksize	= 256;
	readsize	= 256;
      ;

    memory "lfuse"
        size            = 1;
        write           = "1 0 1 0  1 1 0 0  1 0 1 0  0 0 0 0",
                          "x x x x  x x x x  i i i i  i i i i";

        read            = "0 1 0 1  0 0 0 0  0 0 0 0  0 0 0 0",
                          "x x x x  x x x x  o o o o  o o o o";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "hfuse"
        size            = 1;
        write           = "1 0 1 0  1 1 0 0  1 0 1 0  1 0 0 0",
                          "x x x x  x x x x  i i i i  i i i i";

        read            = "0 1 0 1  1 0 0 0  0 0 0 0  1 0 0 0",
                          "x x x x  x x x x  o o o o  o o o o";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "efuse"
        size            = 1;
        write           = "1 0 1 0  1 1 0 0  1 0 1 0  0 1 0 0",
                          "x x x x  x x x x  x x x x  i i i i";

        read            = "0 1 0 1  0 0 0 0  0 0 0 0  1 0 0 0",
                          "x x x x  x x x x  o o o o  o o o o";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "lock"
        size            = 1;
        read            = "0 1 0 1  1 0 0 0   0 0 0 0  0 0 0 0",
                          "x x x x  x x x x   x x o o  o o o o";

        write           = "1 0 1 0  1 1 0 0   1 1 1 x  x x x x",
                          "x x x x  x x x x   1 1 i i  i i i i";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "calibration"
        size            = 1;
        read            = "0 0 1 1  1 0 0 0  0 0 0 x  x x x x",
                          "0 0 0 0  0 0 0 0  o o o o  o o o o";
      ;

    memory "signature"
        size            = 3;
        read            = "0  0  1  1   0  0  0  0   x  x  x  x   x  x  x  x",
                          "x  x  x  x   x  x a1 a0   o  o  o  o   o  o  o  o";
      ;
  ;


#------------------------------------------------------------
# ATmega16
#------------------------------------------------------------

part
    id               = "m16";
    desc             = "ATmega16";
    has_jtag         = yes;
    stk500_devcode   = 0x82;
    avr910_devcode   = 0x74;
    signature        = 0x1e 0x94 0x03;
    pagel            = 0xd7;
    bs2              = 0xa0;
    chip_erase_delay = 9000;
    pgm_enable       = "1 0 1 0  1 1 0 0    0 1 0 1  0 0 1 1",
                       "x x x x  x x x x    x x x x  x x x x";

    chip_erase       = "1 0 1 0  1 1 0 0    1 0 0 x  x x x x",
                       "x x x x  x x x x    x x x x  x x x x";

    timeout		= 200;
    stabdelay		= 100;
    cmdexedelay		= 25;
    synchloops		= 32;
    bytedelay		= 0;
    pollindex		= 3;
    pollvalue		= 0x53;
    predelay		= 1;
    postdelay		= 1;
    pollmethod		= 0;

    pp_controlstack     =
	0x0E, 0x1E, 0x0F, 0x1F, 0x2E, 0x3E, 0x2F, 0x3F,
	0x4E, 0x5E, 0x4F, 0x5F, 0x6E, 0x7E, 0x6F, 0x7F,
	0x66, 0x76, 0x67, 0x77, 0x6A, 0x7A, 0x6B, 0x7B,
	0xBE, 0xFD, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00;
    hventerstabdelay    = 100;
    progmodedelay       = 100;
    latchcycles         = 6;
    togglevtg           = 0;
    poweroffdelay       = 0;
    resetdelayms        = 0;
    resetdelayus        = 0;
    hvleavestabdelay    = 15;
    resetdelay          = 15;
    chiperasepulsewidth = 0;
    chiperasepolltimeout = 10;
    programfusepulsewidth = 0;
    programfusepolltimeout = 5;
    programlockpulsewidth = 0;
    programlockpolltimeout = 5;

    idr                 = 0x31;
    spmcr               = 0x57;
    allowfullpagebitstream = yes;

    ocdrev              = 2;

    memory "eeprom"
        paged           = no; /* leave this "no" */
        page_size       = 4;  /* for parallel programming */
        size            = 512;
        min_write_delay = 9000;
        max_write_delay = 9000;
        readback_p1     = 0xff;
        readback_p2     = 0xff;
	read            = "  1   0   1   0      0   0   0   0",
                          "  0   0   x   x      x   x  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

	write           = "  1   1   0   0      0   0   0   0",
                          "  0   0   x   x      x   x  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0", 
                          "  i   i   i   i      i   i   i   i";

	loadpage_lo	= "  1   1   0   0      0   0   0   1",
			  "  0   0   0   0      0   0   0   0",
			  "  0   0   0   0      0   0  a1  a0",
			  "  i   i   i   i      i   i   i   i";

	writepage	= "  1   1   0   0      0   0   1   0",
			  "  0   0   x   x      x   x  a9  a8",
			  " a7  a6  a5  a4     a3  a2   0   0",
			  "  x   x   x   x      x   x   x   x";

	mode		= 0x04;
	delay		= 10;
	blocksize	= 128;
	readsize	= 256;
      ;

    memory "flash"
        paged           = yes;
        size            = 16384;
        page_size       = 128;
        num_pages       = 128;
        min_write_delay = 4500;
        max_write_delay = 4500;
        readback_p1     = 0xff;
        readback_p2     = 0xff;
        read_lo         = "  0   0   1   0      0   0   0   0",
                          "  0   0 a13 a12    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

        read_hi         = "  0   0   1   0      1   0   0   0",
                          "  0   0 a13 a12    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

        loadpage_lo     = "  0   1   0   0      0   0   0   0",
                          "  0   0   x   x      x   x   x   x",
                          "  x   x  a5  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

        loadpage_hi     = "  0   1   0   0      1   0   0   0",
                          "  0   0   x   x      x   x   x   x",
                          "  x   x  a5  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

        writepage       = "  0   1   0   0      1   1   0   0",
                          "  0   0 a13 a12    a11 a10  a9  a8",
                          " a7  a6   x   x      x   x   x   x",
                          "  x   x   x   x      x   x   x   x";

	mode		= 0x21;
	delay		= 6;
	blocksize	= 128;
	readsize	= 256;
      ;

    memory "lock"
        size            = 1;
        read            = "0 1 0 1  1 0 0 0   0 0 0 0  0 0 0 0",
                          "x x x x  x x x x   x x o o  o o o o";

        write           = "1 0 1 0  1 1 0 0   1 1 1 x  x x x x",
                          "x x x x  x x x x   1 1 i i  i i i i";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "lfuse"
        size            = 1;
        read            = "0 1 0 1  0 0 0 0   0 0 0 0  0 0 0 0",
                          "x x x x  x x x x   o o o o  o o o o";

        write           = "1 0 1 0  1 1 0 0   1 0 1 0  0 0 0 0",
                          "x x x x  x x x x   i i i i  i i i i";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "hfuse"
        size            = 1;
        read            = "0 1 0 1  1 0 0 0   0 0 0 0  1 0 0 0",
                          "x x x x  x x x x   o o o o  o o o o";

        write           = "1 0 1 0  1 1 0 0   1 0 1 0  1 0 0 0",
                          "x x x x  x x x x   i i i i  i i i i";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;
    memory "signature"
        size            = 3;
        read            = "0  0  1  1   0  0  0  0   x  x  x  x   x  x  x  x",
                          "x  x  x  x   x  x a1 a0   o  o  o  o   o  o  o  o";
      ;
    memory "calibration"
        size            = 4;

        read            = "0 0 1 1  1 0 0 0   0 0 0 x  x x x x",
                          "0 0 0 0  0 0 a1 a0 o o o o  o o o o";
        ;
  ;


#------------------------------------------------------------
# ATmega164P
#------------------------------------------------------------

# close to ATmega16

part parent "m16"
    id               = "m164p";
    desc             = "ATmega164P";
    signature        = 0x1e 0x94 0x0a;

    progmodedelay       = 0;
    latchcycles         = 5;
    togglevtg           = 1;
    poweroffdelay       = 15;
    resetdelayms        = 1;
    allowfullpagebitstream = no;
    chip_erase_delay = 55000;

    ocdrev              = 3;
  ;


#------------------------------------------------------------
# ATmega324P
#------------------------------------------------------------

# similar to ATmega164P

part
    id               = "m324p";
    desc             = "ATmega324P";
    has_jtag         = yes;
    stk500_devcode   = 0x82; # no STK500v1 support, use the ATmega16 one
    avr910_devcode   = 0x74;
    signature        = 0x1e 0x95 0x08;
    pagel            = 0xd7;
    bs2              = 0xa0;
    chip_erase_delay = 55000;
    pgm_enable       = "1 0 1 0  1 1 0 0    0 1 0 1  0 0 1 1",
                       "x x x x  x x x x    x x x x  x x x x";

    chip_erase       = "1 0 1 0  1 1 0 0    1 0 0 x  x x x x",
                       "x x x x  x x x x    x x x x  x x x x";

    timeout		= 200;
    stabdelay		= 100;
    cmdexedelay		= 25;
    synchloops		= 32;
    bytedelay		= 0;
    pollindex		= 3;
    pollvalue		= 0x53;
    predelay		= 1;
    postdelay		= 1;
    pollmethod		= 0;

    pp_controlstack     =
        0x0E, 0x1E, 0x0F, 0x1F, 0x2E, 0x3E, 0x2F, 0x3F,
        0x4E, 0x5E, 0x4F, 0x5F, 0x6E, 0x7E, 0x6F, 0x7F,
        0x66, 0x76, 0x67, 0x77, 0x6A, 0x7A, 0x6B, 0x7B,
        0xBE, 0xFD, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00;
    hventerstabdelay    = 100;
    progmodedelay       = 0;
    latchcycles         = 5;
    togglevtg           = 1;
    poweroffdelay       = 15;
    resetdelayms        = 1;
    resetdelayus        = 0;
    hvleavestabdelay    = 15;
    chiperasepulsewidth = 0;
    chiperasepolltimeout = 10;
    programfusepulsewidth = 0;
    programfusepolltimeout = 5;
    programlockpulsewidth = 0;
    programlockpolltimeout = 5;

    idr                 = 0x31;
    spmcr               = 0x57;
    allowfullpagebitstream = no;

    ocdrev              = 3;

    memory "eeprom"
        paged           = no; /* leave this "no" */
        page_size       = 4;  /* for parallel programming */
        size            = 1024;
        min_write_delay = 9000;
        max_write_delay = 9000;
        readback_p1     = 0xff;
        readback_p2     = 0xff;
	read            = "  1   0   1   0      0   0   0   0",
                          "  0   0   x   x      x a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

	write           = "  1   1   0   0      0   0   0   0",
                          "  0   0   x   x      x a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0", 
                          "  i   i   i   i      i   i   i   i";

	loadpage_lo	= "  1   1   0   0      0   0   0   1",
			  "  0   0   0   0      0   0   0   0",
			  "  0   0   0   0      0   0  a1  a0",
			  "  i   i   i   i      i   i   i   i";

	writepage	= "  1   1   0   0      0   0   1   0",
			  "  0   0   x   x      x a10  a9  a8",
			  " a7  a6  a5  a4     a3  a2   0   0",
			  "  x   x   x   x      x   x   x   x";

	mode		= 0x41;
	delay		= 10;
	blocksize	= 128;
	readsize	= 256;
      ;

    memory "flash"
        paged           = yes;
        size            = 32768;
        page_size       = 128;
        num_pages       = 256;
        min_write_delay = 4500;
        max_write_delay = 4500;
        readback_p1     = 0xff;
        readback_p2     = 0xff;
        read_lo         = "  0   0   1   0      0   0   0   0",
                          "  0 a14 a13 a12    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

        read_hi         = "  0   0   1   0      1   0   0   0",
                          "  0 a14 a13 a12    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

        loadpage_lo     = "  0   1   0   0      0   0   0   0",
                          "  0   0   x   x      x   x   x   x",
                          "  x   x  a5  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

        loadpage_hi     = "  0   1   0   0      1   0   0   0",
                          "  0   0   x   x      x   x   x   x",
                          "  x   x  a5  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

        writepage       = "  0   1   0   0      1   1   0   0",
                          "  0 a14 a13 a12    a11 a10  a9  a8",
                          " a7  a6   x   x      x   x   x   x",
                          "  x   x   x   x      x   x   x   x";

	mode		= 0x21;
	delay		= 6;
	blocksize	= 256;
	readsize	= 256;
      ;

    memory "lock"
        size            = 1;
        read            = "0 1 0 1  1 0 0 0   0 0 0 0  0 0 0 0",
                          "x x x x  x x x x   x x o o  o o o o";

        write           = "1 0 1 0  1 1 0 0   1 1 1 x  x x x x",
                          "x x x x  x x x x   1 1 i i  i i i i";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "lfuse"
        size            = 1;
        read            = "0 1 0 1  0 0 0 0   0 0 0 0  0 0 0 0",
                          "x x x x  x x x x   o o o o  o o o o";

        write           = "1 0 1 0  1 1 0 0   1 0 1 0  0 0 0 0",
                          "x x x x  x x x x   i i i i  i i i i";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "hfuse"
        size            = 1;
        read            = "0 1 0 1  1 0 0 0   0 0 0 0  1 0 0 0",
                          "x x x x  x x x x   o o o o  o o o o";

        write           = "1 0 1 0  1 1 0 0   1 0 1 0  1 0 0 0",
                          "x x x x  x x x x   i i i i  i i i i";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "efuse"
        size            = 1;

        read            = "0 1 0 1  0 0 0 0  0 0 0 0  1 0 0 0",
                          "x x x x  x x x x  o o o o  o o o o";

        write           = "1 0 1 0  1 1 0 0  1 0 1 0  0 1 0 0",
                          "x x x x  x x x x  1 1 1 1  1 i i i";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "signature"
        size            = 3;
        read            = "0  0  1  1   0  0  0  0   x  x  x  x   x  x  x  x",
                          "x  x  x  x   x  x a1 a0   o  o  o  o   o  o  o  o";
      ;

    memory "calibration"
        size            = 1;

        read            = "0 0 1 1  1 0 0 0   0 0 0 x  x x x x",
                          "0 0 0 0  0 0 0 0   o o o o  o o o o";
        ;
  ;


#------------------------------------------------------------
# ATmega324PA
#------------------------------------------------------------

# similar to ATmega324P

part parent "m324p"
    id               = "m324pa";
    desc             = "ATmega324PA";
    signature        = 0x1e 0x95 0x11;

    ocdrev              = 3;
  ;


#------------------------------------------------------------
# ATmega644
#------------------------------------------------------------

# similar to ATmega164

part
    id               = "m644";
    desc             = "ATmega644";
    has_jtag         = yes;
    stk500_devcode   = 0x82; # no STK500v1 support, use the ATmega16 one
    avr910_devcode   = 0x74;
    signature        = 0x1e 0x96 0x09;
    pagel            = 0xd7;
    bs2              = 0xa0;
    chip_erase_delay = 55000;
    pgm_enable       = "1 0 1 0  1 1 0 0    0 1 0 1  0 0 1 1",
                       "x x x x  x x x x    x x x x  x x x x";

    chip_erase       = "1 0 1 0  1 1 0 0    1 0 0 x  x x x x",
                       "x x x x  x x x x    x x x x  x x x x";

    timeout		= 200;
    stabdelay		= 100;
    cmdexedelay		= 25;
    synchloops		= 32;
    bytedelay		= 0;
    pollindex		= 3;
    pollvalue		= 0x53;
    predelay		= 1;
    postdelay		= 1;
    pollmethod		= 0;

    pp_controlstack     =
        0x0E, 0x1E, 0x0F, 0x1F, 0x2E, 0x3E, 0x2F, 0x3F,
        0x4E, 0x5E, 0x4F, 0x5F, 0x6E, 0x7E, 0x6F, 0x7F,
        0x66, 0x76, 0x67, 0x77, 0x6A, 0x7A, 0x6B, 0x7B,
        0xBE, 0xFD, 0x00, 0x01, 0x00, 0x00, 0x00, 0x02;
    hventerstabdelay    = 100;
    progmodedelay       = 0;
    latchcycles         = 6;
    togglevtg           = 0;
    poweroffdelay       = 0;
    resetdelayms        = 0;
    resetdelayus        = 0;
    hvleavestabdelay    = 15;
    chiperasepulsewidth = 0;
    chiperasepolltimeout = 10;
    programfusepulsewidth = 0;
    programfusepolltimeout = 5;
    programlockpulsewidth = 0;
    programlockpolltimeout = 5;

    idr                 = 0x31;
    spmcr               = 0x57;
    allowfullpagebitstream = no;

    ocdrev              = 3;

    memory "eeprom"
        paged           = no; /* leave this "no" */
        page_size       = 8;  /* for parallel programming */
        size            = 2048;
        min_write_delay = 9000;
        max_write_delay = 9000;
        readback_p1     = 0xff;
        readback_p2     = 0xff;
	read            = "  1   0   1   0      0   0   0   0",
                          "  0   0   x   x    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

	write           = "  1   1   0   0      0   0   0   0",
                          "  0   0   x   x    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0", 
                          "  i   i   i   i      i   i   i   i";

	loadpage_lo	= "  1   1   0   0      0   0   0   1",
			  "  0   0   0   0      0   0   0   0",
			  "  0   0   0   0      0  a2  a1  a0",
			  "  i   i   i   i      i   i   i   i";

	writepage	= "  1   1   0   0      0   0   1   0",
			  "  0   0   x   x    a11 a10  a9  a8",
			  " a7  a6  a5  a4     a3   0   0   0",
			  "  x   x   x   x      x   x   x   x";

	mode		= 0x41;
	delay		= 10;
	blocksize	= 128;
	readsize	= 256;
      ;

    memory "flash"
        paged           = yes;
        size            = 65536;
        page_size       = 256;
        num_pages       = 256;
        min_write_delay = 4500;
        max_write_delay = 4500;
        readback_p1     = 0xff;
        readback_p2     = 0xff;
        read_lo         = "  0   0   1   0      0   0   0   0",
                          "a15 a14 a13 a12    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

        read_hi         = "  0   0   1   0      1   0   0   0",
                          "a15 a14 a13 a12    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

        loadpage_lo     = "  0   1   0   0      0   0   0   0",
                          "  0   0   x   x      x   x   x   x",
                          "  x  a6  a5  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

        loadpage_hi     = "  0   1   0   0      1   0   0   0",
                          "  0   0   x   x      x   x   x   x",
                          "  x  a6  a5  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

        writepage       = "  0   1   0   0      1   1   0   0",
                          "a15 a14 a13 a12    a11 a10  a9  a8",
                          " a7   x   x   x      x   x   x   x",
                          "  x   x   x   x      x   x   x   x";

	mode		= 0x21;
	delay		= 6;
	blocksize	= 256;
	readsize	= 256;
      ;

    memory "lock"
        size            = 1;
        read            = "0 1 0 1  1 0 0 0   0 0 0 0  0 0 0 0",
                          "x x x x  x x x x   x x o o  o o o o";

        write           = "1 0 1 0  1 1 0 0   1 1 1 x  x x x x",
                          "x x x x  x x x x   1 1 i i  i i i i";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "lfuse"
        size            = 1;
        read            = "0 1 0 1  0 0 0 0   0 0 0 0  0 0 0 0",
                          "x x x x  x x x x   o o o o  o o o o";

        write           = "1 0 1 0  1 1 0 0   1 0 1 0  0 0 0 0",
                          "x x x x  x x x x   i i i i  i i i i";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "hfuse"
        size            = 1;
        read            = "0 1 0 1  1 0 0 0   0 0 0 0  1 0 0 0",
                          "x x x x  x x x x   o o o o  o o o o";

        write           = "1 0 1 0  1 1 0 0   1 0 1 0  1 0 0 0",
                          "x x x x  x x x x   i i i i  i i i i";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "efuse"
        size            = 1;

        read            = "0 1 0 1  0 0 0 0  0 0 0 0  1 0 0 0",
                          "x x x x  x x x x  o o o o  o o o o";

        write           = "1 0 1 0  1 1 0 0  1 0 1 0  0 1 0 0",
                          "x x x x  x x x x  1 1 1 1  1 i i i";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "signature"
        size            = 3;
        read            = "0  0  1  1   0  0  0  0   x  x  x  x   x  x  x  x",
                          "x  x  x  x   x  x a1 a0   o  o  o  o   o  o  o  o";
      ;

    memory "calibration"
        size            = 1;

        read            = "0 0 1 1  1 0 0 0   0 0 0 x  x x x x",
                          "0 0 0 0  0 0 0 0   o o o o  o o o o";
        ;
  ;

#------------------------------------------------------------
# ATmega644P
#------------------------------------------------------------

# similar to ATmega164p

part parent "m644"
    id               = "m644p";
    desc             = "ATmega644P";
    signature        = 0x1e 0x96 0x0a;

    ocdrev              = 3;
  ;



#------------------------------------------------------------
# ATmega1284
#------------------------------------------------------------

# similar to ATmega164

part
    id               = "m1284";
    desc             = "ATmega1284";
    has_jtag         = yes;
    stk500_devcode   = 0x82; # no STK500v1 support, use the ATmega16 one
    avr910_devcode   = 0x74;
    signature        = 0x1e 0x97 0x06;
    pagel            = 0xd7;
    bs2              = 0xa0;
    chip_erase_delay = 55000;
    pgm_enable       = "1 0 1 0  1 1 0 0    0 1 0 1  0 0 1 1",
                       "x x x x  x x x x    x x x x  x x x x";

    chip_erase       = "1 0 1 0  1 1 0 0    1 0 0 x  x x x x",
                       "x x x x  x x x x    x x x x  x x x x";

    timeout		= 200;
    stabdelay		= 100;
    cmdexedelay		= 25;
    synchloops		= 32;
    bytedelay		= 0;
    pollindex		= 3;
    pollvalue		= 0x53;
    predelay		= 1;
    postdelay		= 1;
    pollmethod		= 1;

    pp_controlstack     =
        0x0E, 0x1E, 0x0F, 0x1F, 0x2E, 0x3E, 0x2F, 0x3F,
        0x4E, 0x5E, 0x4F, 0x5F, 0x6E, 0x7E, 0x6F, 0x7F,
        0x66, 0x76, 0x67, 0x77, 0x6A, 0x7A, 0x6B, 0x7B,
        0xBE, 0xFD, 0x00, 0x01, 0x00, 0x00, 0x00, 0x02;
    hventerstabdelay    = 100;
    progmodedelay       = 0;
    latchcycles         = 6;
    togglevtg           = 1;
    poweroffdelay       = 15;
    resetdelayms        = 1;
    resetdelayus        = 0;
    hvleavestabdelay    = 15;
    chiperasepulsewidth = 0;
    chiperasepolltimeout = 10;
    programfusepulsewidth = 0;
    programfusepolltimeout = 5;
    programlockpulsewidth = 0;
    programlockpolltimeout = 5;

    idr                 = 0x31;
    spmcr               = 0x57;
    allowfullpagebitstream = no;

    ocdrev              = 3;

    memory "eeprom"
        paged           = no; /* leave this "no" */
        page_size       = 8;  /* for parallel programming */
        size            = 4096;
        min_write_delay = 9000;
        max_write_delay = 9000;
        readback_p1     = 0xff;
        readback_p2     = 0xff;
	read            = "  1   0   1   0      0   0   0   0",
                          "  0   0   x   x    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

	write           = "  1   1   0   0      0   0   0   0",
                          "  0   0   x   x    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0", 
                          "  i   i   i   i      i   i   i   i";

	loadpage_lo	= "  1   1   0   0      0   0   0   1",
			  "  0   0   0   0      0   0   0   0",
			  "  0   0   0   0      0  a2  a1  a0",
			  "  i   i   i   i      i   i   i   i";

	writepage	= "  1   1   0   0      0   0   1   0",
			  "  0   0   x   x    a11 a10  a9  a8",
			  " a7  a6  a5  a4     a3   0   0   0",
			  "  x   x   x   x      x   x   x   x";

	mode		= 0x41;
	delay		= 10;
	blocksize	= 128;
	readsize	= 256;
      ;

    memory "flash"
        paged           = yes;
        size            = 131072;
        page_size       = 256;
        num_pages       = 512;
        min_write_delay = 4500;
        max_write_delay = 4500;
        readback_p1     = 0xff;
        readback_p2     = 0xff;
        read_lo         = "  0   0   1   0      0   0   0   0",
                          "a15 a14 a13 a12    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

        read_hi         = "  0   0   1   0      1   0   0   0",
                          "a15 a14 a13 a12    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

        loadpage_lo     = "  0   1   0   0      0   0   0   0",
                          "  0   0   x   x      x   x   x   x",
                          "  x  a6  a5  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

        loadpage_hi     = "  0   1   0   0      1   0   0   0",
                          "  0   0   x   x      x   x   x   x",
                          "  x  a6  a5  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

        writepage       = "  0   1   0   0      1   1   0   0",
                          "a15 a14 a13 a12    a11 a10  a9  a8",
                          " a7   x   x   x      x   x   x   x",
                          "  x   x   x   x      x   x   x   x";

	mode		= 0x41;
	delay		= 10;
	blocksize	= 256;
	readsize	= 256;
      ;

    memory "lock"
        size            = 1;
        read            = "0 1 0 1  1 0 0 0   0 0 0 0  0 0 0 0",
                          "x x x x  x x x x   x x o o  o o o o";

        write           = "1 0 1 0  1 1 0 0   1 1 1 x  x x x x",
                          "x x x x  x x x x   1 1 i i  i i i i";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "lfuse"
        size            = 1;
        read            = "0 1 0 1  0 0 0 0   0 0 0 0  0 0 0 0",
                          "x x x x  x x x x   o o o o  o o o o";

        write           = "1 0 1 0  1 1 0 0   1 0 1 0  0 0 0 0",
                          "x x x x  x x x x   i i i i  i i i i";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "hfuse"
        size            = 1;
        read            = "0 1 0 1  1 0 0 0   0 0 0 0  1 0 0 0",
                          "x x x x  x x x x   o o o o  o o o o";

        write           = "1 0 1 0  1 1 0 0   1 0 1 0  1 0 0 0",
                          "x x x x  x x x x   i i i i  i i i i";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "efuse"
        size            = 1;

        read            = "0 1 0 1  0 0 0 0  0 0 0 0  1 0 0 0",
                          "x x x x  x x x x  o o o o  o o o o";

        write           = "1 0 1 0  1 1 0 0  1 0 1 0  0 1 0 0",
                          "x x x x  x x x x  1 1 1 1  1 i i i";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "signature"
        size            = 3;
        read            = "0  0  1  1   0  0  0  0   x  x  x  x   x  x  x  x",
                          "x  x  x  x   x  x a1 a0   o  o  o  o   o  o  o  o";
      ;

    memory "calibration"
        size            = 1;

        read            = "0 0 1 1  1 0 0 0   0 0 0 x  x x x x",
                          "0 0 0 0  0 0 0 0   o o o o  o o o o";
        ;
  ;



#------------------------------------------------------------
# ATmega1284P
#------------------------------------------------------------

# similar to ATmega164p

part
    id               = "m1284p";
    desc             = "ATmega1284P";
    has_jtag         = yes;
    stk500_devcode   = 0x82; # no STK500v1 support, use the ATmega16 one
    avr910_devcode   = 0x74;
    signature        = 0x1e 0x97 0x05;
    pagel            = 0xd7;
    bs2              = 0xa0;
    chip_erase_delay = 55000;
    pgm_enable       = "1 0 1 0  1 1 0 0    0 1 0 1  0 0 1 1",
                       "x x x x  x x x x    x x x x  x x x x";

    chip_erase       = "1 0 1 0  1 1 0 0    1 0 0 x  x x x x",
                       "x x x x  x x x x    x x x x  x x x x";

    timeout		= 200;
    stabdelay		= 100;
    cmdexedelay		= 25;
    synchloops		= 32;
    bytedelay		= 0;
    pollindex		= 3;
    pollvalue		= 0x53;
    predelay		= 1;
    postdelay		= 1;
    pollmethod		= 1;

    pp_controlstack     =
        0x0E, 0x1E, 0x0F, 0x1F, 0x2E, 0x3E, 0x2F, 0x3F,
        0x4E, 0x5E, 0x4F, 0x5F, 0x6E, 0x7E, 0x6F, 0x7F,
        0x66, 0x76, 0x67, 0x77, 0x6A, 0x7A, 0x6B, 0x7B,
        0xBE, 0xFD, 0x00, 0x01, 0x00, 0x00, 0x00, 0x02;
    hventerstabdelay    = 100;
    progmodedelay       = 0;
    latchcycles         = 6;
    togglevtg           = 1;
    poweroffdelay       = 15;
    resetdelayms        = 1;
    resetdelayus        = 0;
    hvleavestabdelay    = 15;
    chiperasepulsewidth = 0;
    chiperasepolltimeout = 10;
    programfusepulsewidth = 0;
    programfusepolltimeout = 5;
    programlockpulsewidth = 0;
    programlockpolltimeout = 5;

    idr                 = 0x31;
    spmcr               = 0x57;
    allowfullpagebitstream = no;

    ocdrev              = 3;

    memory "eeprom"
        paged           = no; /* leave this "no" */
        page_size       = 8;  /* for parallel programming */
        size            = 4096;
        min_write_delay = 9000;
        max_write_delay = 9000;
        readback_p1     = 0xff;
        readback_p2     = 0xff;
	read            = "  1   0   1   0      0   0   0   0",
                          "  0   0   x   x    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

	write           = "  1   1   0   0      0   0   0   0",
                          "  0   0   x   x    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0", 
                          "  i   i   i   i      i   i   i   i";

	loadpage_lo	= "  1   1   0   0      0   0   0   1",
			  "  0   0   0   0      0   0   0   0",
			  "  0   0   0   0      0  a2  a1  a0",
			  "  i   i   i   i      i   i   i   i";

	writepage	= "  1   1   0   0      0   0   1   0",
			  "  0   0   x   x    a11 a10  a9  a8",
			  " a7  a6  a5  a4     a3   0   0   0",
			  "  x   x   x   x      x   x   x   x";

	mode		= 0x41;
	delay		= 10;
	blocksize	= 128;
	readsize	= 256;
      ;

    memory "flash"
        paged           = yes;
        size            = 131072;
        page_size       = 256;
        num_pages       = 512;
        min_write_delay = 4500;
        max_write_delay = 4500;
        readback_p1     = 0xff;
        readback_p2     = 0xff;
        read_lo         = "  0   0   1   0      0   0   0   0",
                          "a15 a14 a13 a12    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

        read_hi         = "  0   0   1   0      1   0   0   0",
                          "a15 a14 a13 a12    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

        loadpage_lo     = "  0   1   0   0      0   0   0   0",
                          "  0   0   x   x      x   x   x   x",
                          "  x  a6  a5  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

        loadpage_hi     = "  0   1   0   0      1   0   0   0",
                          "  0   0   x   x      x   x   x   x",
                          "  x  a6  a5  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

        writepage       = "  0   1   0   0      1   1   0   0",
                          "a15 a14 a13 a12    a11 a10  a9  a8",
                          " a7   x   x   x      x   x   x   x",
                          "  x   x   x   x      x   x   x   x";

	mode		= 0x41;
	delay		= 10;
	blocksize	= 256;
	readsize	= 256;
      ;

    memory "lock"
        size            = 1;
        read            = "0 1 0 1  1 0 0 0   0 0 0 0  0 0 0 0",
                          "x x x x  x x x x   x x o o  o o o o";

        write           = "1 0 1 0  1 1 0 0   1 1 1 x  x x x x",
                          "x x x x  x x x x   1 1 i i  i i i i";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "lfuse"
        size            = 1;
        read            = "0 1 0 1  0 0 0 0   0 0 0 0  0 0 0 0",
                          "x x x x  x x x x   o o o o  o o o o";

        write           = "1 0 1 0  1 1 0 0   1 0 1 0  0 0 0 0",
                          "x x x x  x x x x   i i i i  i i i i";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "hfuse"
        size            = 1;
        read            = "0 1 0 1  1 0 0 0   0 0 0 0  1 0 0 0",
                          "x x x x  x x x x   o o o o  o o o o";

        write           = "1 0 1 0  1 1 0 0   1 0 1 0  1 0 0 0",
                          "x x x x  x x x x   i i i i  i i i i";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "efuse"
        size            = 1;

        read            = "0 1 0 1  0 0 0 0  0 0 0 0  1 0 0 0",
                          "x x x x  x x x x  o o o o  o o o o";

        write           = "1 0 1 0  1 1 0 0  1 0 1 0  0 1 0 0",
                          "x x x x  x x x x  1 1 1 1  1 i i i";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "signature"
        size            = 3;
        read            = "0  0  1  1   0  0  0  0   x  x  x  x   x  x  x  x",
                          "x  x  x  x   x  x a1 a0   o  o  o  o   o  o  o  o";
      ;

    memory "calibration"
        size            = 1;

        read            = "0 0 1 1  1 0 0 0   0 0 0 x  x x x x",
                          "0 0 0 0  0 0 0 0   o o o o  o o o o";
        ;
  ;



#------------------------------------------------------------
# ATmega162
#------------------------------------------------------------

part
    id               = "m162";
    desc             = "ATmega162";
    has_jtag         = yes;
    stk500_devcode   = 0x83;
    avr910_devcode   = 0x63;
    signature        = 0x1e 0x94 0x04;
    chip_erase_delay = 9000;
    pagel            = 0xd7;
    bs2              = 0xa0;

    idr              = 0x04;
    spmcr            = 0x57;
    allowfullpagebitstream = yes;

    pgm_enable       = "1 0 1 0  1 1 0 0    0 1 0 1  0 0 1 1",
                       "x x x x  x x x x    x x x x  x x x x";

    chip_erase       = "1 0 1 0  1 1 0 0    1 0 0 x  x x x x",
                       "x x x x  x x x x    x x x x  x x x x";

    ocdrev              = 2;

    memory "flash"
        paged           = yes;
        size            = 16384;
        page_size       = 128;
        num_pages       = 128;
        min_write_delay = 4500;
        max_write_delay = 4500;
        readback_p1     = 0xff;
        readback_p2     = 0xff;

        read_lo         = "  0   0   1   0      0   0   0   0",
                          "  0   0 a13 a12    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

        read_hi         = "  0   0   1   0      1   0   0   0",
                          "  0   0 a13 a12    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

        loadpage_lo     = "  0   1   0   0      0   0   0   0",
                          "  0   0   x   x      x   x   x   x",
                          "  x   x  a5  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

        loadpage_hi     = "  0   1   0   0      1   0   0   0",
                          "  0   0   x   x      x   x   x   x",
                          "  x   x  a5  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

        writepage       = "  0   1   0   0      1   1   0   0",
                          "  0   0 a13 a12    a11 a10  a9  a8",
                          " a7  a6   x   x      x   x   x   x",
                          "  x   x   x   x      x   x   x   x";
       mode        = 0x41;
    delay       = 10;
    blocksize   = 128;
    readsize    = 256;  

        ;

    timeout		= 200;
    stabdelay		= 100;
    cmdexedelay		= 25;
    synchloops		= 32;
    bytedelay		= 0;
    pollindex		= 3;
    pollvalue		= 0x53;
    predelay		= 1;
    postdelay		= 1;
    pollmethod		= 0;

    pp_controlstack     =
        0x0E, 0x1E, 0x0F, 0x1F, 0x2E, 0x3E, 0x2F, 0x3F,
        0x4E, 0x5E, 0x4F, 0x5F, 0x6E, 0x7E, 0x6F, 0x7F,
        0x66, 0x76, 0x67, 0x77, 0x6A, 0x7A, 0x6B, 0x7B,
        0xBE, 0xFD, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00;
    hventerstabdelay    = 100;
    progmodedelay       = 0;
    latchcycles         = 6;
    togglevtg           = 0;
    poweroffdelay       = 0;
    resetdelayms        = 0;
    resetdelayus        = 0;
    hvleavestabdelay    = 15;
    chiperasepulsewidth = 0;
    chiperasepolltimeout = 10;
    programfusepulsewidth = 0;
    programfusepolltimeout = 5;
    programlockpulsewidth = 0;
    programlockpolltimeout = 5;

    memory "eeprom"
        paged           = no; /* leave this "no" */
        page_size       = 4;  /* for parallel programming */
        size            = 512;
        min_write_delay = 9000;
        max_write_delay = 9000;
        readback_p1     = 0xff;
        readback_p2     = 0xff;

                read            = "  1   0   1   0      0   0   0   0",
                          "  0   0   x   x      x   x  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

                write           = "  1   1   0   0      0   0   0   0",
                          "  0   0   x   x      x   x  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

	loadpage_lo	= "  1   1   0   0      0   0   0   1",
			  "  0   0   0   0      0   0   0   0",
			  "  0   0   0   0      0   0  a1  a0",
			  "  i   i   i   i      i   i   i   i";

	writepage	= "  1   1   0   0      0   0   1   0",
			  "  0   0   x   x      x   x  a9  a8",
			  " a7  a6  a5  a4     a3  a2   0   0",
			  "  x   x   x   x      x   x   x   x";

	mode		= 0x41;
	delay		= 20;
	blocksize	= 4;
	readsize	= 256;
        ;

    memory "lfuse"
        size            = 1;
        min_write_delay = 16000;
        max_write_delay = 16000;
        read            = "0 1 0 1  0 0 0 0   0 0 0 0  0 0 0 0",
                          "x x x x  x x x x   o o o o  o o o o";

        write           = "1 0 1 0  1 1 0 0   1 0 1 0  0 0 0 0",
                          "x x x x  x x x x   i i i i  i i i i";
        ;

    memory "hfuse"
        size            = 1;
        min_write_delay = 16000;
        max_write_delay = 16000;

        read            = "0 1 0 1  1 0 0 0   0 0 0 0  1 0 0 0",
                          "x x x x  x x x x   o o o o  o o o o";

        write           = "1 0 1 0  1 1 0 0   1 0 1 0  1 0 0 0",
                          "x x x x  x x x x   i i i i  i i i i";
        ;

    memory "efuse"
        size            = 1;
        min_write_delay = 16000;
        max_write_delay = 16000;

        read            = "0 1 0 1  0 0 0 0  0 0 0 0  1 0 0 0",
                          "x x x x  x x x x  o o o o  o o o o";

        write           = "1 0 1 0  1 1 0 0  1 0 1 0  0 1 0 0",
                          "x x x x  x x x x  1 1 1 1  1 i i i";
      ;

    memory "lock"
        size            = 1;
        min_write_delay = 16000;
        max_write_delay = 16000;

        read            = "0 1 0 1  1 0 0 0   0 0 0 0  0 0 0 0",
                          "x x x x  x x x x   x x o o  o o o o";

        write           = "1 0 1 0  1 1 0 0   1 1 1 x  x x x x",
                          "x x x x  x x x x   1 1 i i  i i i i";
        ;

    memory "signature"
        size            = 3;

        read            = "0  0  1  1   0  0  0  0   0  0  x  x   x  x  x  x",
                          "x  x  x  x   x  x a1 a0   o  o  o  o   o  o  o  o";
        ;

    memory "calibration"
        size            = 1;

        read            = "0 0 1 1  1 0 0 0   0 0 x x  x x x x",
                          "0 0 0 0  0 0 0 0   o o o o  o o o o";
        ;
;



#------------------------------------------------------------
# ATmega163
#------------------------------------------------------------

part
    id               = "m163";
    desc             = "ATmega163";
    stk500_devcode   = 0x81;
    avr910_devcode   = 0x64;
    signature        = 0x1e 0x94 0x02;
    chip_erase_delay = 32000;
    pagel            = 0xd7;
    bs2              = 0xa0;
    pgm_enable       = "1 0 1 0  1 1 0 0    0 1 0 1  0 0 1 1",
                       "x x x x  x x x x    x x x x  x x x x";

    chip_erase       = "1 0 1 0  1 1 0 0    1 0 0 0  0 0 0 0",
                       "x x x x  x x x x    x x x x  x x x x";

    timeout             = 200;
    stabdelay           = 100;
    cmdexedelay         = 25;
    synchloops          = 32;
    bytedelay           = 0;
    pollindex           = 3;
    pollvalue           = 0x53;
    predelay            = 1;
    postdelay           = 1;
    pollmethod          = 0;

    pp_controlstack     =
        0x0E, 0x1E, 0x0F, 0x1F, 0x2E, 0x3E, 0x2F, 0x3F,
        0x4E, 0x5E, 0x4F, 0x5F, 0x6E, 0x7E, 0x6F, 0x7F,
        0x66, 0x76, 0x67, 0x77, 0x6A, 0x7A, 0x6B, 0x7B,
        0xBE, 0xFD, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00;
    hventerstabdelay    = 100;
    progmodedelay       = 0;
    latchcycles         = 0;
    togglevtg           = 0;
    poweroffdelay       = 0;
    resetdelayms        = 0;
    resetdelayus        = 0;
    hvleavestabdelay    = 15;
    chiperasepulsewidth = 0;
    chiperasepolltimeout = 30;
    programfusepulsewidth = 0;
    programfusepolltimeout = 2;
    programlockpulsewidth = 0;
    programlockpolltimeout = 2;


   memory "eeprom"
        size            = 512;
        min_write_delay = 4000;
        max_write_delay = 4000;
        readback_p1     = 0xff;
        readback_p2     = 0xff;
	read            = "  1   0   1   0      0   0   0   0",
                          "  x   x   x   x      x   x   x  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

	write           = "  1   1   0   0      0   0   0   0",
                          "  x   x   x   x      x   x   x  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";
        mode            = 0x41;
        delay           = 20;
        blocksize       = 4;
        readsize        = 256;
      ;

    memory "flash"
        paged           = yes;
        size            = 16384;
        page_size       = 128;
        num_pages       = 128;
        min_write_delay = 16000;
        max_write_delay = 16000;
        readback_p1     = 0xff;
        readback_p2     = 0xff;
        read_lo         = "  0   0   1   0      0   0   0   0",
                          "  x   x   x a12    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

        read_hi         = "  0   0   1   0      1   0   0   0",
                          "  x   x   x a12    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

        loadpage_lo     = "  0   1   0   0      0   0   0   0",
                          "  x   x   x   x      x   x   x   x",
                          "  x   x  a5  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

        loadpage_hi     = "  0   1   0   0      1   0   0   0",
                          "  x   x   x   x      x   x   x   x",
                          "  x   x  a5  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

        writepage       = "  0   1   0   0      1   1   0   0",
                          "  x   x   x a12    a11 a10  a9  a8",
                          " a7  a6   x   x      x   x   x   x",
                          "  x   x   x   x      x   x   x   x";

	mode		= 0x11;
	delay		= 20;
	blocksize	= 128;
	readsize	= 256;
      ;

    memory "lfuse"
        size            = 1;
        min_write_delay = 2000;
        max_write_delay = 2000;
        read            = "0 1 0 1  0 0 0 0   0 0 0 0  0 0 0 0",
                          "x x x x  x x x x   o o x x  o o o o";

        write           = "1 0 1 0  1 1 0 0   1 0 1 0  0 0 0 0",
                          "x x x x  x x x x   i i 1 1  i i i i";
      ;

    memory "hfuse"
        size            = 1;
        min_write_delay = 2000;
        max_write_delay = 2000;
        read            = "0 1 0 1  1 0 0 0   0 0 0 0  1 0 0 0",
                          "x x x x  x x x x   x x x x  1 o o o";

        write           = "1 0 1 0  1 1 0 0   1 0 1 0  1 0 0 0",
                          "x x x x  x x x x   1 1 1 1  1 i i i";
      ;

    memory "lock"
        size            = 1;
        min_write_delay = 2000;
        max_write_delay = 2000;
        read            = "0 1 0 1  1 0 0 0   0 0 0 0  0 0 0 0",
                          "x x x x  0 x x x   x x o o  o o o o";

        write           = "1 0 1 0  1 1 0 0   1 1 1 x  x x x x",
                          "x x x x  x x x x   1 1 i i  i i i i";
      ;

    memory "signature"
        size            = 3;
        read            = "0  0  1  1   0  0  0  0   x  x  x  x   x  x  x  x",
                          "x  x  x  x   x  x a1 a0   o  o  o  o   o  o  o  o";
      ;

    memory "calibration"
        size            = 1;
        read            = "0 0 1 1  1 0 0 0   x x x x  x x x x",
                          "0 0 0 0  0 0 0 0   o o o o  o o o o";
      ;
  ;

#------------------------------------------------------------
# ATmega169
#------------------------------------------------------------

part
    id               = "m169";
    desc             = "ATmega169";
    has_jtag         = yes;
    stk500_devcode   = 0x85;
    avr910_devcode   = 0x78;
    signature        = 0x1e 0x94 0x05;
    chip_erase_delay = 9000;
    pgm_enable       = "1 0 1 0  1 1 0 0    0 1 0 1  0 0 1 1",
                       "x x x x  x x x x    x x x x  x x x x";

    chip_erase       = "1 0 1 0  1 1 0 0    1 0 0 0  0 0 0 0",
                       "x x x x  x x x x    x x x x  x x x x";
    timeout		= 200;
    stabdelay		= 100;
    cmdexedelay		= 25;
    synchloops		= 32;
    bytedelay		= 0;
    pollindex		= 3;
    pollvalue		= 0x53;
    predelay		= 1;
    postdelay		= 1;
    pollmethod		= 1;

    pp_controlstack     =
        0x0E, 0x1E, 0x0F, 0x1F, 0x2E, 0x3E, 0x2F, 0x3F,
        0x4E, 0x5E, 0x4F, 0x5F, 0x6E, 0x7E, 0x6F, 0x7F,
        0x66, 0x76, 0x67, 0x77, 0x6A, 0x7A, 0x6B, 0x7B,
        0xBE, 0xFD, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00;
    hventerstabdelay    = 100;
    progmodedelay       = 0;
    latchcycles         = 5;
    togglevtg           = 1;
    poweroffdelay       = 15;
    resetdelayms        = 1;
    resetdelayus        = 0;
    hvleavestabdelay    = 15;
    chiperasepulsewidth = 0;
    chiperasepolltimeout = 10;
    programfusepulsewidth = 0;
    programfusepolltimeout = 5;
    programlockpulsewidth = 0;
    programlockpolltimeout = 5;

    idr                 = 0x31;
    spmcr               = 0x57;

    ocdrev              = 2;

   memory "eeprom"
        paged           = no; /* leave this "no" */
        page_size       = 4;  /* for parallel programming */
        size            = 512;
        min_write_delay = 9000;
        max_write_delay = 9000;
        readback_p1     = 0xff;
        readback_p2     = 0xff;
	read            = "  1   0   1   0      0   0   0   0",
                          "  x   x   x   x      x   x   x  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

	write           = "  1   1   0   0      0   0   0   0",
                          "  x   x   x   x      x   x   x  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0", 
                          "  i   i   i   i      i   i   i   i";

	loadpage_lo	= "  1   1   0   0      0   0   0   1",
			  "  0   0   0   0      0   0   0   0",
			  "  0   0   0   0      0   0  a1  a0",
			  "  i   i   i   i      i   i   i   i";

	writepage	= "  1   1   0   0      0   0   1   0",
			  "  0   0   x   x      x   x   x  a8",
			  " a7  a6  a5  a4     a3  a2   0   0",
			  "  x   x   x   x      x   x   x   x";

	mode		= 0x41;
	delay		= 20;
	blocksize	= 4;
	readsize	= 256;
      ;

    memory "flash"
        paged           = yes;
        size            = 16384;
        page_size       = 128;
        num_pages       = 128;
        min_write_delay = 4500;
        max_write_delay = 4500;
        readback_p1     = 0xff;
        readback_p2     = 0xff;
        read_lo         = "  0   0   1   0      0   0   0   0",
                          "  x   x   x a12    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

        read_hi         = "  0   0   1   0      1   0   0   0",
                          "  x   x   x a12    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

        loadpage_lo     = "  0   1   0   0      0   0   0   0",
                          "  x   x   x   x      x   x   x   x",
                          "  x   x  a5  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

        loadpage_hi     = "  0   1   0   0      1   0   0   0",
                          "  x   x   x   x      x   x   x   x",
                          "  x   x  a5  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

        writepage       = "  0   1   0   0      1   1   0   0",
                          "  x   x   x a12    a11 a10  a9  a8",
                          " a7  a6   x   x      x   x   x   x",
                          "  x   x   x   x      x   x   x   x";

	mode		= 0x41;
	delay		= 6;
	blocksize	= 128;
	readsize	= 256;
      ;

    memory "lfuse"
        size            = 1;
        min_write_delay = 2000;
        max_write_delay = 2000;
        read            = "0 1 0 1  0 0 0 0   0 0 0 0  0 0 0 0",
                          "x x x x  x x x x   o o o o  o o o o";

        write           = "1 0 1 0  1 1 0 0   1 0 1 0  0 0 0 0",
                          "x x x x  x x x x   i i i i  i i i i";
      ;

    memory "hfuse"
        size            = 1;
        min_write_delay = 2000;
        max_write_delay = 2000;
        read            = "0 1 0 1  1 0 0 0   0 0 0 0  1 0 0 0",
                          "x x x x  x x x x   o o o o  o o o o";

        write           = "1 0 1 0  1 1 0 0   1 0 1 0  1 0 0 0",
                          "x x x x  x x x x   i i i i  i i i i";
      ;

    memory "efuse"
        size            = 1;
        write           = "1 0 1 0  1 1 0 0  1 0 1 0  0 1 0 0",
                          "x x x x  x x x x  x x x x  i i i i";

        read            = "0 1 0 1  0 0 0 0  0 0 0 0  1 0 0 0",
                          "x x x x  x x x x  o o o o  o o o o";
      ;

    memory "lock"
        size            = 1;
        min_write_delay = 2000;
        max_write_delay = 2000;
        read            = "0 1 0 1  1 0 0 0   0 0 0 0  0 0 0 0",
                          "x x x x  x x x x   x x o o  o o o o";

        write           = "1 0 1 0  1 1 0 0   1 1 1 x  x x x x",
                          "x x x x  x x x x   1 1 i i  i i i i";
      ;

    memory "signature"
        size            = 3;
        read            = "0  0  1  1   0  0  0  0   0  0  0  x   x  x  x  x",
                          "x  x  x  x   x  x a1 a0   o  o  o  o   o  o  o  o";
      ;

    memory "calibration"
        size            = 1;
        read            = "0 0 1 1  1 0 0 0   0 0 0 x  x x x x",
                          "0 0 0 0  0 0 0 0   o o o o  o o o o";
      ;
  ;

#------------------------------------------------------------
# ATmega329
#------------------------------------------------------------

part
    id               = "m329";
    desc             = "ATmega329";
    has_jtag         = yes;
#    stk500_devcode   = 0x85; # no STK500 support, only STK500v2
#    avr910_devcode   = 0x?;  # try the ATmega169 one:
    avr910_devcode   = 0x75;
    signature        = 0x1e 0x95 0x03;
    chip_erase_delay = 9000;
    pgm_enable       = "1 0 1 0  1 1 0 0    0 1 0 1  0 0 1 1",
                       "x x x x  x x x x    x x x x  x x x x";

    chip_erase       = "1 0 1 0  1 1 0 0    1 0 0 0  0 0 0 0",
                       "x x x x  x x x x    x x x x  x x x x";
    timeout		= 200;
    stabdelay		= 100;
    cmdexedelay		= 25;
    synchloops		= 32;
    bytedelay		= 0;
    pollindex		= 3;
    pollvalue		= 0x53;
    predelay		= 1;
    postdelay		= 1;
    pollmethod		= 1;

    pp_controlstack     =
        0x0E, 0x1E, 0x0F, 0x1F, 0x2E, 0x3E, 0x2F, 0x3F,
        0x4E, 0x5E, 0x4F, 0x5F, 0x6E, 0x7E, 0x6F, 0x7F,
        0x66, 0x76, 0x67, 0x77, 0x6A, 0x7A, 0x6B, 0x7B,
        0xBE, 0xFD, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00;
    hventerstabdelay    = 100;
    progmodedelay       = 0;
    latchcycles         = 5;
    togglevtg           = 1;
    poweroffdelay       = 15;
    resetdelayms        = 1;
    resetdelayus        = 0;
    hvleavestabdelay    = 15;
    chiperasepulsewidth = 0;
    chiperasepolltimeout = 10;
    programfusepulsewidth = 0;
    programfusepolltimeout = 5;
    programlockpulsewidth = 0;
    programlockpolltimeout = 5;

    idr                 = 0x31;
    spmcr               = 0x57;

    ocdrev              = 3;

   memory "eeprom"
        paged           = no; /* leave this "no" */
        page_size       = 4;  /* for parallel programming */
        size            = 1024;
        min_write_delay = 9000;
        max_write_delay = 9000;
        readback_p1     = 0xff;
        readback_p2     = 0xff;
	read            = "  1   0   1   0      0   0   0   0",
                          "  x   x   x   x      x   x  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

	write           = "  1   1   0   0      0   0   0   0",
                          "  x   x   x   x      x   x  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

	loadpage_lo	= "  1   1   0   0      0   0   0   1",
			  "  0   0   0   0      0   0   0   0",
			  "  0   0   0   0      0   0  a1  a0",
			  "  i   i   i   i      i   i   i   i";

	writepage	= "  1   1   0   0      0   0   1   0",
			  "  0   0   x   x      x   x  a9  a8",
			  " a7  a6  a5  a4     a3  a2   0   0",
			  "  x   x   x   x      x   x   x   x";

	mode		= 0x41;
	delay		= 20;
	blocksize	= 8;
	readsize	= 256;
      ;

    memory "flash"
        paged           = yes;
        size            = 32768;
        page_size       = 128;
        num_pages       = 256;
        min_write_delay = 4500;
        max_write_delay = 4500;
        readback_p1     = 0xff;
        readback_p2     = 0xff;
        read_lo         = "  0   0   1   0      0   0   0   0",
                          "  x a14 a13 a12    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

        read_hi         = "  0   0   1   0      1   0   0   0",
                          "  x a14 a13 a12    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

        loadpage_lo     = "  0   1   0   0      0   0   0   0",
                          "  x   x   x   x      x   x   x   x",
                          "  x   x  a5  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

        loadpage_hi     = "  0   1   0   0      1   0   0   0",
                          "  x   x   x   x      x   x   x   x",
                          "  x   x  a5  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

        writepage       = "  0   1   0   0      1   1   0   0",
                          "  x   x   x a12    a11 a10  a9  a8",
                          " a7  a6   x   x      x   x   x   x",
                          "  x   x   x   x      x   x   x   x";

	mode		= 0x41;
	delay		= 6;
	blocksize	= 256;
	readsize	= 256;
      ;

    memory "lfuse"
        size            = 1;
        min_write_delay = 4500;
        max_write_delay = 4500;
        read            = "0 1 0 1  0 0 0 0   0 0 0 0  0 0 0 0",
                          "x x x x  x x x x   o o o o  o o o o";

        write           = "1 0 1 0  1 1 0 0   1 0 1 0  0 0 0 0",
                          "x x x x  x x x x   i i i i  i i i i";
      ;

    memory "hfuse"
        size            = 1;
        min_write_delay = 4500;
        max_write_delay = 4500;
        read            = "0 1 0 1  1 0 0 0   0 0 0 0  1 0 0 0",
                          "x x x x  x x x x   o o o o  o o o o";

        write           = "1 0 1 0  1 1 0 0   1 0 1 0  1 0 0 0",
                          "x x x x  x x x x   i i i i  i i i i";
      ;

    memory "efuse"
        size            = 1;
        min_write_delay = 4500;
        max_write_delay = 4500;
        read            = "0 1 0 1  0 0 0 0   0 0 0 0  1 0 0 0",
                          "x x x x  x x x x   o o o o  o o o o";

        write           = "1 0 1 0  1 1 0 0   1 0 1 0  0 1 0 0",
                          "x x x x  x x x x   x x x x  x i i i";
      ;

    memory "lock"
        size            = 1;
        min_write_delay = 4500;
        max_write_delay = 4500;
        read            = "0 1 0 1  1 0 0 0   0 0 0 0  0 0 0 0",
                          "x x x x  x x x x   x x o o  o o o o";

        write           = "1 0 1 0  1 1 0 0   1 1 1 x  x x x x",
                          "x x x x  x x x x   1 1 i i  i i i i";
      ;

    memory "signature"
        size            = 3;
        read            = "0  0  1  1   0  0  0  0   0  0  0  x   x  x  x  x",
                          "x  x  x  x   x  x a1 a0   o  o  o  o   o  o  o  o";
      ;

    memory "calibration"
        size            = 1;
        read            = "0 0 1 1  1 0 0 0   0 0 0 x  x x x x",
                          "0 0 0 0  0 0 0 0   o o o o  o o o o";
      ;
  ;

#------------------------------------------------------------
# ATmega329P
#------------------------------------------------------------
# Identical to ATmega329 except of the signature

part parent "m329"
    id               = "m329p";
    desc             = "ATmega329P";
    signature        = 0x1e 0x95 0x0b;

    ocdrev              = 3;
  ;

#------------------------------------------------------------
# ATmega3290
#------------------------------------------------------------

# identical to ATmega329

part parent "m329"
    id               = "m3290";
    desc             = "ATmega3290";
    signature        = 0x1e 0x95 0x04;

    ocdrev              = 3;
  ;

#------------------------------------------------------------
# ATmega3290P
#------------------------------------------------------------

# identical to ATmega3290 except of the signature

part parent "m3290"
    id               = "m3290p";
    desc             = "ATmega3290P";
    signature        = 0x1e 0x95 0x0c;

    ocdrev              = 3;
  ;

#------------------------------------------------------------
# ATmega649
#------------------------------------------------------------

part
    id               = "m649";
    desc             = "ATmega649";
    has_jtag         = yes;
#    stk500_devcode   = 0x85; # no STK500 support, only STK500v2
#    avr910_devcode   = 0x?;  # try the ATmega169 one:
    avr910_devcode   = 0x75;
    signature        = 0x1e 0x96 0x03;
    chip_erase_delay = 9000;
    pgm_enable       = "1 0 1 0  1 1 0 0    0 1 0 1  0 0 1 1",
                       "x x x x  x x x x    x x x x  x x x x";

    chip_erase       = "1 0 1 0  1 1 0 0    1 0 0 0  0 0 0 0",
                       "x x x x  x x x x    x x x x  x x x x";
    timeout		= 200;
    stabdelay		= 100;
    cmdexedelay		= 25;
    synchloops		= 32;
    bytedelay		= 0;
    pollindex		= 3;
    pollvalue		= 0x53;
    predelay		= 1;
    postdelay		= 1;
    pollmethod		= 1;

    pp_controlstack     =
        0x0E, 0x1E, 0x0F, 0x1F, 0x2E, 0x3E, 0x2F, 0x3F,
        0x4E, 0x5E, 0x4F, 0x5F, 0x6E, 0x7E, 0x6F, 0x7F,
        0x66, 0x76, 0x67, 0x77, 0x6A, 0x7A, 0x6B, 0x7B,
        0xBE, 0xFD, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00;
    hventerstabdelay    = 100;
    progmodedelay       = 0;
    latchcycles         = 5;
    togglevtg           = 1;
    poweroffdelay       = 15;
    resetdelayms        = 1;
    resetdelayus        = 0;
    hvleavestabdelay    = 15;
    chiperasepulsewidth = 0;
    chiperasepolltimeout = 10;
    programfusepulsewidth = 0;
    programfusepolltimeout = 5;
    programlockpulsewidth = 0;
    programlockpolltimeout = 5;

    idr                 = 0x31;
    spmcr               = 0x57;

    ocdrev              = 3;

   memory "eeprom"
        paged           = no; /* leave this "no" */
        page_size       = 8;  /* for parallel programming */
        size            = 2048;
        min_write_delay = 9000;
        max_write_delay = 9000;
        readback_p1     = 0xff;
        readback_p2     = 0xff;
	read            = "  1   0   1   0      0   0   0   0",
                          "  x   x   x   x      x a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

	write           = "  1   1   0   0      0   0   0   0",
                          "  x   x   x   x      x a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

	loadpage_lo	= "  1   1   0   0      0   0   0   1",
			  "  0   0   0   0      0   0   0   0",
			  "  0   0   0   0      0  a2  a1  a0",
			  "  i   i   i   i      i   i   i   i";

	writepage	= "  1   1   0   0      0   0   1   0",
			  "  0   0   x   x      x a10  a9  a8",
			  " a7  a6  a5  a4     a3   0   0   0",
			  "  x   x   x   x      x   x   x   x";

	mode		= 0x41;
	delay		= 20;
	blocksize	= 8;
	readsize	= 256;
      ;

    memory "flash"
        paged           = yes;
        size            = 65536;
        page_size       = 256;
        num_pages       = 256;
        min_write_delay = 4500;
        max_write_delay = 4500;
        readback_p1     = 0xff;
        readback_p2     = 0xff;
        read_lo         = "  0   0   1   0      0   0   0   0",
                          "a15 a14 a13 a12    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

        read_hi         = "  0   0   1   0      1   0   0   0",
                          "a15 a14 a13 a12    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

        loadpage_lo     = "  0   1   0   0      0   0   0   0",
                          "  x   x   x   x      x   x   x   x",
                          "  x  a6  a5  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

        loadpage_hi     = "  0   1   0   0      1   0   0   0",
                          "  x   x   x   x      x   x   x   x",
                          "  x  a6  a5  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

        writepage       = "  0   1   0   0      1   1   0   0",
                          "  x   x   x a12    a11 a10  a9  a8",
                          " a7   x   x   x      x   x   x   x",
                          "  x   x   x   x      x   x   x   x";

	mode		= 0x41;
	delay		= 6;
	blocksize	= 256;
	readsize	= 256;
      ;

    memory "lfuse"
        size            = 1;
        min_write_delay = 4500;
        max_write_delay = 4500;
        read            = "0 1 0 1  0 0 0 0   0 0 0 0  0 0 0 0",
                          "x x x x  x x x x   o o o o  o o o o";

        write           = "1 0 1 0  1 1 0 0   1 0 1 0  0 0 0 0",
                          "x x x x  x x x x   i i i i  i i i i";
      ;

    memory "hfuse"
        size            = 1;
        min_write_delay = 4500;
        max_write_delay = 4500;
        read            = "0 1 0 1  1 0 0 0   0 0 0 0  1 0 0 0",
                          "x x x x  x x x x   o o o o  o o o o";

        write           = "1 0 1 0  1 1 0 0   1 0 1 0  1 0 0 0",
                          "x x x x  x x x x   i i i i  i i i i";
      ;

    memory "efuse"
        size            = 1;
        min_write_delay = 4500;
        max_write_delay = 4500;
        read            = "0 1 0 1  0 0 0 0   0 0 0 0  1 0 0 0",
                          "x x x x  x x x x   o o o o  o o o o";

        write           = "1 0 1 0  1 1 0 0   1 0 1 0  0 1 0 0",
                          "x x x x  x x x x   x x x x  x i i i";
      ;

    memory "lock"
        size            = 1;
        min_write_delay = 4500;
        max_write_delay = 4500;
        read            = "0 1 0 1  1 0 0 0   0 0 0 0  0 0 0 0",
                          "x x x x  x x x x   x x o o  o o o o";

        write           = "1 0 1 0  1 1 0 0   1 1 1 x  x x x x",
                          "x x x x  x x x x   1 1 i i  i i i i";
      ;

    memory "signature"
        size            = 3;
        read            = "0  0  1  1   0  0  0  0   0  0  0  x   x  x  x  x",
                          "x  x  x  x   x  x a1 a0   o  o  o  o   o  o  o  o";
      ;

    memory "calibration"
        size            = 1;
        read            = "0 0 1 1  1 0 0 0   0 0 0 x  x x x x",
                          "0 0 0 0  0 0 0 0   o o o o  o o o o";
      ;
  ;

#------------------------------------------------------------
# ATmega6490
#------------------------------------------------------------

# identical to ATmega649

part parent "m649"
    id               = "m6490";
    desc             = "ATmega6490";
    signature        = 0x1e 0x96 0x04;

    ocdrev              = 3;
  ;

#------------------------------------------------------------
# ATmega32
#------------------------------------------------------------

part
    id               = "m32";
    desc             = "ATmega32";
    has_jtag         = yes;
    stk500_devcode   = 0x91;
    avr910_devcode   = 0x72;
    signature        = 0x1e 0x95 0x02;
    chip_erase_delay = 9000;
    pagel            = 0xd7;
    bs2              = 0xa0;
    reset            = dedicated;
    pgm_enable       = "1 0 1 0  1 1 0 0    0 1 0 1  0 0 1 1",
                       "x x x x  x x x x    x x x x  x x x x";

    chip_erase       = "1 0 1 0  1 1 0 0    1 0 0 0  0 0 0 0",
                       "x x x x  x x x x    x x x x  x x x x";
    timeout		= 200;
    stabdelay		= 100;
    cmdexedelay		= 25;
    synchloops		= 32;
    bytedelay		= 0;
    pollindex		= 3;
    pollvalue		= 0x53;
    predelay		= 1;
    postdelay		= 1;
    pollmethod		= 0;

    pp_controlstack     =
        0x0E, 0x1E, 0x0F, 0x1F, 0x2E, 0x3E, 0x2F, 0x3F,
        0x4E, 0x5E, 0x4F, 0x5F, 0x6E, 0x7E, 0x6F, 0x7F,
        0x66, 0x76, 0x67, 0x77, 0x6A, 0x7A, 0x6B, 0x7B,
        0xBE, 0xFD, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00;
    hventerstabdelay    = 100;
    progmodedelay       = 0;
    latchcycles         = 6;
    togglevtg           = 0;
    poweroffdelay       = 0;
    resetdelayms        = 0;
    resetdelayus        = 0;
    hvleavestabdelay    = 15;
    chiperasepulsewidth = 0;
    chiperasepolltimeout = 10;
    programfusepulsewidth = 0;
    programfusepolltimeout = 5;
    programlockpulsewidth = 0;
    programlockpolltimeout = 5;

    idr                 = 0x31;
    spmcr               = 0x57;
    allowfullpagebitstream = yes;

    ocdrev              = 2;

   memory "eeprom"
        paged           = no;   /* leave this "no" */
        page_size       = 4;    /* for parallel programming */
        size            = 1024;
        min_write_delay = 9000;
        max_write_delay = 9000;
        readback_p1     = 0xff;
        readback_p2     = 0xff;
        read            = "  1   0   1   0      0   0   0   0",
                          "  0   0   x   x      x   x  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

        write           = "  1   1   0   0      0   0   0   0",
                          "  0   0   x   x      x   x  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

	loadpage_lo	= "  1   1   0   0      0   0   0   1",
			  "  0   0   0   0      0   0   0   0",
			  "  0   0   0   0      0   0  a1  a0",
			  "  i   i   i   i      i   i   i   i";

	writepage	= "  1   1   0   0      0   0   1   0",
			  "  0   0   x   x      x   x  a9  a8",
			  " a7  a6  a5  a4     a3  a2   0   0",
			  "  x   x   x   x      x   x   x   x";

	mode		= 0x04;
	delay		= 10;
	blocksize	= 64;
	readsize	= 256;
      ;

    memory "flash"
        paged           = yes;
        size            = 32768;
        page_size       = 128;
        num_pages       = 256;
        min_write_delay = 4500;
        max_write_delay = 4500;
        readback_p1     = 0xff;
        readback_p2     = 0xff;
        read_lo         = "  0   0   1   0      0   0   0   0",
                          "  0   0 a13 a12    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

        read_hi         = "  0   0   1   0      1   0   0   0",
                          "  0   0 a13 a12    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

        loadpage_lo     = "  0   1   0   0      0   0   0   0",
                          "  0   0   x   x      x   x   x   x",
                          "  x   x  a5  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

        loadpage_hi     = "  0   1   0   0      1   0   0   0",
                          "  0   0   x   x      x   x   x   x",
                          "  x   x  a5  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

        writepage       = "  0   1   0   0      1   1   0   0",
                          "  0   0 a13 a12    a11 a10  a9  a8",
                          " a7  a6   x   x      x   x   x   x",
                          "  x   x   x   x      x   x   x   x";

	mode		= 0x21;
	delay		= 6;
	blocksize	= 64;
	readsize	= 256;
      ;

    memory "lfuse"
        size            = 1;
        min_write_delay = 2000;
        max_write_delay = 2000;
        read            = "0 1 0 1  0 0 0 0   0 0 0 0  0 0 0 0",
                          "x x x x  x x x x   o o o o  o o o o";

        write           = "1 0 1 0  1 1 0 0   1 0 1 0  0 0 0 0",
                          "x x x x  x x x x   i i i i  i i i i";
      ;

    memory "hfuse"
        size            = 1;
        min_write_delay = 2000;
        max_write_delay = 2000;
        read            = "0 1 0 1  1 0 0 0   0 0 0 0  1 0 0 0",
                          "x x x x  x x x x   o o o o  o o o o";

        write           = "1 0 1 0  1 1 0 0   1 0 1 0  1 0 0 0",
                          "x x x x  x x x x   i i i i  i i i i";
      ;

    memory "lock"
        size            = 1;
        min_write_delay = 2000;
        max_write_delay = 2000;
        read            = "0 1 0 1  1 0 0 0   0 0 0 0  0 0 0 0",
                          "x x x x  x x x x   x x o o  o o o o";

        write           = "1 0 1 0  1 1 0 0   1 1 1 x  x x x x",
                          "x x x x  x x x x   1 1 i i  i i i i";
      ;

    memory "signature"
        size            = 3;
        read            = "0  0  1  1   0  0  0  0   x  x  x  x   x  x  x x",
                          "x  x  x  x   x  x a1 a0   o  o  o  o   o  o  o o";
      ;

    memory "calibration"
        size            = 4;
        read            = "0 0 1 1  1 0 0 0    0 0 x x  x x x x",
                          "0 0 0 0  0 0 a1 a0  o o o o  o o o o";
      ;
  ;

#------------------------------------------------------------
# ATmega161
#------------------------------------------------------------

part
    id               = "m161";
    desc             = "ATmega161";
    stk500_devcode   = 0x80;
    avr910_devcode   = 0x60;
    signature        = 0x1e 0x94 0x01;
    chip_erase_delay = 28000;
    pagel            = 0xd7;
    bs2              = 0xa0;
    pgm_enable       = "1 0 1 0  1 1 0 0    0 1 0 1  0 0 1 1",
                       "x x x x  x x x x    x x x x  x x x x";

    chip_erase       = "1 0 1 0  1 1 0 0    1 0 0 0  0 0 0 0",
                       "x x x x  x x x x    x x x x  x x x x";
    timeout		= 200;
    stabdelay		= 100;
    cmdexedelay		= 25;
    synchloops		= 32;
    bytedelay		= 0;
    pollindex		= 3;
    pollvalue		= 0x53;
    predelay		= 1;
    postdelay		= 1;
    pollmethod		= 0;

    pp_controlstack     =
        0x0E, 0x1E, 0x0F, 0x1F, 0x2E, 0x3E, 0x2F, 0x3F,
        0x4E, 0x5E, 0x4F, 0x5F, 0x6E, 0x7E, 0x6F, 0x7F,
        0x66, 0x76, 0x67, 0x77, 0x6A, 0x7A, 0x6B, 0x7B,
        0xBE, 0xFD, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00;
    hventerstabdelay    = 100;
    progmodedelay       = 0;
    latchcycles         = 0;
    togglevtg           = 0;
    poweroffdelay       = 0;
    resetdelayms        = 0;
    resetdelayus        = 0;
    hvleavestabdelay    = 15;
    chiperasepulsewidth = 0;
    chiperasepolltimeout = 30;
    programfusepulsewidth = 0;
    programfusepolltimeout = 2;
    programlockpulsewidth = 0;
    programlockpolltimeout = 2;

   memory "eeprom"
        size            = 512;
        min_write_delay = 3400;
        max_write_delay = 3400;
        readback_p1     = 0xff;
        readback_p2     = 0xff;
	read            = "  1   0   1   0      0   0   0   0",
                          "  x   x   x   x      x   x   x  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

	write           = "  1   1   0   0      0   0   0   0",
                          "  x   x   x   x      x   x   x  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

	mode		= 0x04;
	delay		= 5;
	blocksize	= 128;
	readsize	= 256;
      ;

    memory "flash"
        paged           = yes;
        size            = 16384;
        page_size       = 128;
        num_pages       = 128;
        min_write_delay = 14000;
        max_write_delay = 14000;
        readback_p1     = 0xff;
        readback_p2     = 0xff;
        read_lo         = "  0   0   1   0      0   0   0   0",
                          "  x   x   x a12    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

        read_hi         = "  0   0   1   0      1   0   0   0",
                          "  x   x   x a12    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

        loadpage_lo     = "  0   1   0   0      0   0   0   0",
                          "  x   x   x   x      x   x   x   x",
                          "  x   x  a5  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

        loadpage_hi     = "  0   1   0   0      1   0   0   0",
                          "  x   x   x   x      x   x   x   x",
                          "  x   x  a5  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

        writepage       = "  0   1   0   0      1   1   0   0",
                          "  x   x   x a12    a11 a10  a9  a8",
                          " a7  a6   x   x      x   x   x   x",
                          "  x   x   x   x      x   x   x   x";

	mode		= 0x21;
	delay		= 16;
	blocksize	= 128;
	readsize	= 256;
      ;

    memory "fuse"
        size            = 1;
        min_write_delay = 2000;
        max_write_delay = 2000;
        read            = "0 1 0 1  0 0 0 0   x x x x  x x x x",
                          "x x x x  x x x x   x o x o  o o o o";

        write           = "1 0 1 0  1 1 0 0   1 0 1 x  x x x x",
                          "x x x x  x x x x   1 i 1 i  i i i i";
      ;

    memory "lock"
        size            = 1;
        min_write_delay = 2000;
        max_write_delay = 2000;
        read            = "0 1 0 1  1 0 0 0   0 0 0 0  0 0 0 0",
                          "x x x x  x x x x   x x o o  o o o o";

        write           = "1 0 1 0  1 1 0 0   1 1 1 x  x x x x",
                          "x x x x  x x x x   1 1 i i  i i i i";
      ;
    memory "signature"
        size            = 3;
        read            = "0  0  1  1   0  0  0  0   x  x  x  x   x  x  x  x",
                          "x  x  x  x   x  x a1 a0   o  o  o  o   o  o  o  o";
      ;
  ;


#------------------------------------------------------------
# ATmega8
#------------------------------------------------------------

part
    id               = "m8";
    desc             = "ATmega8";
    stk500_devcode   = 0x70;
    avr910_devcode   = 0x76;
    signature        = 0x1e 0x93 0x07;
    pagel            = 0xd7;
    bs2              = 0xc2;
    chip_erase_delay = 10000;
    pgm_enable       = "1 0 1 0  1 1 0 0    0 1 0 1  0 0 1 1",
                       "x x x x  x x x x    x x x x  x x x x";

    chip_erase       = "1 0 1 0  1 1 0 0    1 0 0 x  x x x x",
                       "x x x x  x x x x    x x x x  x x x x";

    timeout		= 200;
    stabdelay		= 100;
    cmdexedelay		= 25;
    synchloops		= 32;
    bytedelay		= 0;
    pollindex		= 3;
    pollvalue		= 0x53;
    predelay		= 1;
    postdelay		= 1;
    pollmethod		= 0;

    pp_controlstack     =
	0x0E, 0x1E, 0x0F, 0x1F, 0x2E, 0x3E, 0x2F, 0x3F,
	0x4E, 0x5E, 0x4F, 0x5F, 0x6E, 0x7E, 0x6F, 0x7F,
	0x66, 0x76, 0x67, 0x77, 0x6A, 0x7A, 0x6B, 0x7B,
	0xBE, 0xFD, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00;
    hventerstabdelay    = 100;
    progmodedelay       = 0;
    latchcycles         = 5;
    togglevtg           = 1;
    poweroffdelay       = 15;
    resetdelayms        = 2;
    resetdelayus        = 0;
    hvleavestabdelay    = 15;
    resetdelay          = 15;
    chiperasepulsewidth = 0;
    chiperasepolltimeout = 10;
    programfusepulsewidth = 0;
    programfusepolltimeout = 5;
    programlockpulsewidth = 0;
    programlockpolltimeout = 5;

    memory "eeprom"
        size            = 512;
        page_size       = 4;
        min_write_delay = 9000;
        max_write_delay = 9000;
        readback_p1     = 0xff;
        readback_p2     = 0xff;
	read            = "  1   0   1   0      0   0   0   0",
                          "  0   0   x   x      x   x   x  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

	write           = "  1   1   0   0      0   0   0   0",
                          "  0   0   x   x      x   x   x  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0", 
                          "  i   i   i   i      i   i   i   i";

	mode		= 0x04;
	delay		= 20;
	blocksize	= 128;
	readsize	= 256;
      ;
    memory "flash"
        paged           = yes;
        size            = 8192;
        page_size       = 64;
        num_pages       = 128;
        min_write_delay = 4500;
        max_write_delay = 4500;
        readback_p1     = 0xff;
        readback_p2     = 0x00;
        read_lo         = "  0   0   1   0    0   0   0   0",
                          "  0   0   0   0  a11 a10  a9  a8",
                          " a7  a6  a5  a4   a3  a2  a1  a0",
                          "  o   o   o   o    o   o   o   o";

        read_hi         = "  0   0   1   0    1   0   0   0",
                          "  0   0   0   0  a11 a10  a9  a8",
                          " a7  a6  a5  a4   a3  a2  a1  a0",
                          "  o   o   o   o    o   o   o   o";

        loadpage_lo     = "  0   1   0   0      0   0   0   0",
                          "  0   0   0   0      x   x   x   x",
                          "  x   x   x  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

        loadpage_hi     = "  0   1   0   0      1   0   0   0",
                          "  0   0   0   0      x   x   x   x",
                          "  x   x   x  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

        writepage       = "  0   1   0   0      1   1   0   0",
                          "  0   0   0   0    a11 a10  a9  a8",
                          " a7  a6  a5   x      x   x   x   x",
                          "  x   x   x   x      x   x   x   x";

	mode		= 0x21;
	delay		= 10;
	blocksize	= 64;
	readsize	= 256;
      ;

    memory "lfuse"
        size            = 1;
        min_write_delay = 2000;
        max_write_delay = 2000;
        read            = "0 1 0 1  0 0 0 0   0 0 0 0  0 0 0 0",
                          "x x x x  x x x x   o o o o  o o o o";

        write           = "1 0 1 0  1 1 0 0   1 0 1 0  0 0 0 0",
                          "x x x x  x x x x   i i i i  i i i i";
      ;

    memory "hfuse"
        size            = 1;
        min_write_delay = 2000;
        max_write_delay = 2000;
        read            = "0 1 0 1  1 0 0 0   0 0 0 0  1 0 0 0",
                          "x x x x  x x x x   o o o o  o o o o";

        write           = "1 0 1 0  1 1 0 0   1 0 1 0  1 0 0 0",
                          "x x x x  x x x x   i i i i  i i i i";
      ;

    memory "lock"
        size            = 1;
        min_write_delay = 2000;
        max_write_delay = 2000;
        read            = "0 1 0 1  1 0 0 0   0 0 0 0  0 0 0 0",
                          "x x x x  x x x x   x x o o  o o o o";

        write           = "1 0 1 0  1 1 0 0   1 1 1 x  x x x x",
                          "x x x x  x x x x   1 1 i i  i i i i";
      ;

    memory "calibration"
        size            = 4;
        read            = "0  0  1  1   1  0  0  0   0  0  x  x   x  x  x  x",
                          "0  0  0  0   0  0 a1 a0   o  o  o  o   o  o  o  o";
      ;

    memory "signature"
        size            = 3;
        read            = "0  0  1  1   0  0  0  0   x  x  x  x   x  x  x  x",
                          "x  x  x  x   x  x a1 a0   o  o  o  o   o  o  o  o";
      ;
  ;



#------------------------------------------------------------
# ATmega8515
#------------------------------------------------------------

part
    id               = "m8515";
    desc             = "ATmega8515";
    stk500_devcode   = 0x63;
    avr910_devcode   = 0x3A;
    signature        = 0x1e 0x93 0x06;
    chip_erase_delay = 9000;
    pgm_enable       = "1 0 1 0  1 1 0 0    0 1 0 1  0 0 1 1",
                       "x x x x  x x x x    x x x x  x x x x";

    chip_erase       = "1 0 1 0  1 1 0 0    1 0 0 x  x x x x",
                       "x x x x  x x x x    x x x x  x x x x";

    timeout		= 200;
    stabdelay		= 100;
    cmdexedelay		= 25;
    synchloops		= 32;
    bytedelay		= 0;
    pollindex		= 3;
    pollvalue		= 0x53;
    predelay		= 1;
    postdelay		= 1;
    pollmethod		= 0;

    pp_controlstack     =
        0x0E, 0x1E, 0x0F, 0x1F, 0x2E, 0x3E, 0x2F, 0x3F,
        0x4E, 0x5E, 0x4F, 0x5F, 0x6E, 0x7E, 0x6F, 0x7F,
        0x66, 0x76, 0x67, 0x77, 0x6A, 0x7A, 0x6B, 0x7B,
        0xBE, 0xFD, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00;
    hventerstabdelay    = 100;
    progmodedelay       = 0;
    latchcycles         = 6;
    togglevtg           = 0;
    poweroffdelay       = 0;
    resetdelayms        = 0;
    resetdelayus        = 0;
    hvleavestabdelay    = 15;
    chiperasepulsewidth = 0;
    chiperasepolltimeout = 10;
    programfusepulsewidth = 0;
    programfusepolltimeout = 5;
    programlockpulsewidth = 0;
    programlockpolltimeout = 5;

    memory "eeprom"
        size            = 512;
        min_write_delay = 9000;
        max_write_delay = 9000;
        readback_p1     = 0xff;
        readback_p2     = 0xff;
 read            = "  1   0   1   0      0   0   0   0",
                          "  0   0   x   x      x   x   x  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

 write           = "  1   1   0   0      0   0   0   0",
                          "  0   0   x   x      x   x   x  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

	mode		= 0x04;
	delay		= 20;
	blocksize	= 128;
	readsize	= 256;
      ;
    memory "flash"
        paged           = yes;
        size            = 8192;
        page_size       = 64;
        num_pages       = 128;
        min_write_delay = 4500;
        max_write_delay = 4500;
        readback_p1     = 0xff;
        readback_p2     = 0xff;
        read_lo         = "  0   0   1   0    0   0   0   0",
                          "  0   0   0   0  a11 a10  a9  a8",
                          " a7  a6  a5  a4   a3  a2  a1  a0",
                          "  o   o   o   o    o   o   o   o";

        read_hi         = "  0   0   1   0    1   0   0   0",
                          "  0   0   0   0  a11 a10  a9  a8",
                          " a7  a6  a5  a4   a3  a2  a1  a0",
                          "  o   o   o   o    o   o   o   o";

        loadpage_lo     = "  0   1   0   0      0   0   0   0",
                          "  0   0   0   0      x   x   x   x",
                          "  x   x   x  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

        loadpage_hi     = "  0   1   0   0      1   0   0   0",
                          "  0   0   0   0      x   x   x   x",
                          "  x   x   x  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

        writepage       = "  0   1   0   0      1   1   0   0",
                          "  0   0   0   0    a11 a10  a9  a8",
                          " a7  a6  a5   x      x   x   x   x",
                          "  x   x   x   x      x   x   x   x";

	mode		= 0x21;
	delay		= 6;
	blocksize	= 64;
	readsize	= 256;
      ;

    memory "lfuse"
        size            = 1;
        min_write_delay = 4500;
        max_write_delay = 4500;
        read            = "0 1 0 1  0 0 0 0   0 0 0 0  0 0 0 0",
                          "x x x x  x x x x   o o o o  o o o o";

        write           = "1 0 1 0  1 1 0 0   1 0 1 0  0 0 0 0",
                          "x x x x  x x x x   i i i i  i i i i";
      ;

    memory "hfuse"
        size            = 1;
        min_write_delay = 4500;
        max_write_delay = 4500;
        read            = "0 1 0 1  1 0 0 0   0 0 0 0  1 0 0 0",
                          "x x x x  x x x x   o o o o  o o o o";

        write           = "1 0 1 0  1 1 0 0   1 0 1 0  1 0 0 0",
                          "x x x x  x x x x   i i i i  i i i i";
      ;

    memory "lock"
        size            = 1;
        min_write_delay = 4500;
        max_write_delay = 4500;
        read            = "0 1 0 1  1 0 0 0   0 0 0 0  0 0 0 0",
                          "x x x x  x x x x   x x o o  o o o o";

        write           = "1 0 1 0  1 1 0 0   1 1 1 x  x x x x",
                          "x x x x  x x x x   1 1 i i  i i i i";
      ;

    memory "calibration"
        size            = 4;
        read            = "0 0 1 1  1 0 0 0     0 0 x x  x x x x",
                          "0 0 0 0  0 0 a1 a0   o o o o  o o o o";
      ;

    memory "signature"
        size            = 3;
        read            = "0  0  1  1   0  0  0  0   x  x  x  x   x  x  x  x",
                          "x  x  x  x   x  x a1 a0   o  o  o  o   o  o  o  o";
      ;
  ;




#------------------------------------------------------------
# ATmega8535
#------------------------------------------------------------

part
    id               = "m8535";
    desc             = "ATmega8535";
    stk500_devcode   = 0x64;
    avr910_devcode   = 0x69;
    signature        = 0x1e 0x93 0x08;
    pagel            = 0xd7;
    bs2              = 0xa0;
    chip_erase_delay = 9000;
    pgm_enable       = "1 0 1 0  1 1 0 0    0 1 0 1  0 0 1 1",
                       "x x x x  x x x x    x x x x  x x x x";

    chip_erase       = "1 0 1 0  1 1 0 0    1 0 0 x  x x x x",
                       "x x x x  x x x x    x x x x  x x x x";

    timeout		= 200;
    stabdelay		= 100;
    cmdexedelay		= 25;
    synchloops		= 32;
    bytedelay		= 0;
    pollindex		= 3;
    pollvalue		= 0x53;
    predelay		= 1;
    postdelay		= 1;
    pollmethod		= 0;

    pp_controlstack     =
        0x0E, 0x1E, 0x0F, 0x1F, 0x2E, 0x3E, 0x2F, 0x3F,
        0x4E, 0x5E, 0x4F, 0x5F, 0x6E, 0x7E, 0x6F, 0x7F,
        0x66, 0x76, 0x67, 0x77, 0x6A, 0x7A, 0x6B, 0x7B,
        0xBE, 0xFD, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00;
    hventerstabdelay    = 100;
    progmodedelay       = 0;
    latchcycles         = 6;
    togglevtg           = 0;
    poweroffdelay       = 0;
    resetdelayms        = 0;
    resetdelayus        = 0;
    hvleavestabdelay    = 15;
    chiperasepulsewidth = 0;
    chiperasepolltimeout = 10;
    programfusepulsewidth = 0;
    programfusepolltimeout = 5;
    programlockpulsewidth = 0;
    programlockpolltimeout = 5;

    memory "eeprom"
        size            = 512;
        min_write_delay = 9000;
        max_write_delay = 9000;
        readback_p1     = 0xff;
        readback_p2     = 0xff;
	read            = "  1   0   1   0      0   0   0   0",
                          "  0   0   x   x      x   x   x  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

	write           = "  1   1   0   0      0   0   0   0",
                          "  0   0   x   x      x   x   x  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

	mode		= 0x04;
	delay		= 20;
	blocksize	= 128;
	readsize	= 256;
      ;
    memory "flash"
        paged           = yes;
        size            = 8192;
        page_size       = 64;
        num_pages       = 128;
        min_write_delay = 4500;
        max_write_delay = 4500;
        readback_p1     = 0xff;
        readback_p2     = 0xff;
        read_lo         = "  0   0   1   0    0   0   0   0",
                          "  0   0   0   0  a11 a10  a9  a8",
                          " a7  a6  a5  a4   a3  a2  a1  a0",
                          "  o   o   o   o    o   o   o   o";

        read_hi         = "  0   0   1   0    1   0   0   0",
                          "  0   0   0   0  a11 a10  a9  a8",
                          " a7  a6  a5  a4   a3  a2  a1  a0",
                          "  o   o   o   o    o   o   o   o";

        loadpage_lo     = "  0   1   0   0      0   0   0   0",
                          "  0   0   0   0      x   x   x   x",
                          "  x   x   x  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

        loadpage_hi     = "  0   1   0   0      1   0   0   0",
                          "  0   0   0   0      x   x   x   x",
                          "  x   x   x  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

        writepage       = "  0   1   0   0      1   1   0   0",
                          "  0   0   0   0    a11 a10  a9  a8",
                          " a7  a6  a5   x      x   x   x   x",
                          "  x   x   x   x      x   x   x   x";

	mode		= 0x21;
	delay		= 6;
	blocksize	= 64;
	readsize	= 256;
      ;

    memory "lfuse"
        size            = 1;
        min_write_delay = 2000;
        max_write_delay = 2000;
        read            = "0 1 0 1  0 0 0 0   0 0 0 0  0 0 0 0",
                          "x x x x  x x x x   o o o o  o o o o";

        write           = "1 0 1 0  1 1 0 0   1 0 1 0  0 0 0 0",
                          "x x x x  x x x x   i i i i  i i i i";
      ;

    memory "hfuse"
        size            = 1;
        min_write_delay = 2000;
        max_write_delay = 2000;
        read            = "0 1 0 1  1 0 0 0   0 0 0 0  1 0 0 0",
                          "x x x x  x x x x   o o o o  o o o o";

        write           = "1 0 1 0  1 1 0 0   1 0 1 0  1 0 0 0",
                          "x x x x  x x x x   i i i i  i i i i";
      ;

    memory "lock"
        size            = 1;
        min_write_delay = 2000;
        max_write_delay = 2000;
        read            = "0 1 0 1  1 0 0 0   0 0 0 0  0 0 0 0",
                          "x x x x  x x x x   x x o o  o o o o";

        write           = "1 0 1 0  1 1 0 0   1 1 1 x  x x x x",
                          "x x x x  x x x x   1 1 i i  i i i i";
      ;

    memory "calibration"
        size            = 4;
        read            = "0 0 1 1  1 0 0 0   0 0 x x  x x x x",
                          "0 0 0 0  0 0 a1 a0 o o o o  o o o o";
      ;

    memory "signature"
        size            = 3;
        read            = "0  0  1  1   0  0  0  0   x  x  x  x   x  x  x  x",
                          "x  x  x  x   x  x a1 a0   o  o  o  o   o  o  o  o";
      ;
  ;


#------------------------------------------------------------
# ATtiny26
#------------------------------------------------------------

part
    id                  = "t26";
    desc                = "ATtiny26";
    stk500_devcode      = 0x21;
    avr910_devcode      = 0x5e;
    signature           = 0x1e 0x91 0x09;
    pagel               = 0xb3;
    bs2                 = 0xb2;
    chip_erase_delay    = 9000;
    pgm_enable          = "1 0 1 0  1 1 0 0   0 1 0 1  0 0 1 1",
                          "x x x x  x x x x   x x x x  x x x x";

    chip_erase          = "1 0 1 0  1 1 0 0   1 0 0 x  x x x x",
                          "x x x x  x x x x   x x x x  x x x x";

    timeout		= 200;
    stabdelay		= 100;
    cmdexedelay		= 25;
    synchloops		= 32;
    bytedelay		= 0;
    pollindex		= 3;
    pollvalue		= 0x53;
    predelay		= 1;
    postdelay		= 1;
    pollmethod		= 0;

    pp_controlstack     =
        0xC4, 0xE4, 0xC4, 0xE4, 0xCC, 0xEC, 0xCC, 0xEC,
        0xD4, 0xF4, 0xD4, 0xF4, 0xDC, 0xFC, 0xDC, 0xFC,
        0xC8, 0xE8, 0xD8, 0xF8, 0x4C, 0x6C, 0x5C, 0x7C,
        0xEC, 0xBC, 0x00, 0x06, 0x00, 0x00, 0x00, 0x00;
    hventerstabdelay    = 100;
    progmodedelay       = 0;
    latchcycles         = 5;
    togglevtg           = 1;
    poweroffdelay       = 15;
    resetdelayms        = 2;
    resetdelayus        = 0;
    hvleavestabdelay    = 15;
    chiperasepulsewidth = 0;
    chiperasepolltimeout = 10;
    programfusepulsewidth = 0;
    programfusepolltimeout = 5;
    programlockpulsewidth = 0;
    programlockpolltimeout = 5;

    memory "eeprom"
        size            = 128;
        min_write_delay = 9000;
        max_write_delay = 9000;
        readback_p1     = 0xff;
        readback_p2     = 0xff;
        read            = "1  0  1  0   0  0  0  0    x x x x  x x x x",
                          "x a6 a5 a4  a3 a2 a1 a0    o o o o  o o o o";

        write           = "1  1  0  0   0  0  0  0    x x x x  x x x x",
                          "x a6 a5 a4  a3 a2 a1 a0    i i i i  i i i i";

	mode		= 0x04;
	delay		= 10;
	blocksize	= 64;
	readsize	= 256;
    ;

    memory "flash"
        paged           = yes;
        size            = 2048;
        page_size       = 32;
        num_pages       = 64;
        min_write_delay = 4500;
        max_write_delay = 4500;
        readback_p1     = 0xff;
        readback_p2     = 0xff;
        read_lo         = "  0  0  1  0   0  0  0  0",
                          "  x  x  x  x   x  x a9 a8",
                          " a7 a6 a5 a4  a3 a2 a1 a0",
                          "  o  o  o  o   o  o  o  o";

        read_hi         = "  0  0  1  0   1  0  0  0",
                          "  x  x  x  x   x  x a9 a8",
                          " a7 a6 a5 a4  a3 a2 a1 a0",
                          "  o  o  o  o   o  o  o  o";

        loadpage_lo     = "  0  1  0  0   0  0  0  0",
                          "  x  x  x  x   x  x  x  x",
                          "  x  x  x  x  a3 a2 a1 a0",
                          "  i  i  i  i   i  i  i  i";

        loadpage_hi     = "  0  1  0  0   1  0  0  0",
                          "  x  x  x  x   x  x  x  x",
                          "  x  x  x  x  a3 a2 a1 a0",
                          "  i  i  i  i   i  i  i  i";

        writepage       = "  0  1  0  0   1  1  0  0",
                          "  x  x  x  x   x  x a9 a8",
                          " a7 a6 a5 a4   x  x  x  x",
                          "  x  x  x  x   x  x  x  x";

	mode		= 0x21;
	delay		= 6;
	blocksize	= 16;
	readsize	= 256;
    ;

    memory "signature"
        size            = 3;
        read            = "0  0  1  1   0  0  0  0    x x x x  x x x x",
                          "0  0  0  0   0  0 a1 a0    o o o o  o o o o";
    ;

    memory "lock"
        size            = 1;
        read            = "0  1  0  1   1  0  0  0    x x x x  x x x x",
                          "x  x  x  x   x  x  x  x    x x x x  x x o o";

        write           = "1  0  1  0   1  1  0  0    1 1 1 1  1 1 i i",
                          "x  x  x  x   x  x  x  x    x x x x  x x x x";
        min_write_delay = 9000;
        max_write_delay = 9000;
    ;

    memory "lfuse"
        size            = 1;
        write           = "1 0 1 0  1 1 0 0  1 0 1 0  0 0 0 0",
                          "x x x x  x x x x  i i i i  i i i i";

        read            = "0 1 0 1  0 0 0 0  0 0 0 0  0 0 0 0",
                          "x x x x  x x x x  o o o o  o o o o";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "hfuse"
        size            = 1;
        write           = "1 0 1 0  1 1 0 0  1 0 1 0  1 0 0 0",
                          "x x x x  x x x x  x x x i  i i i i";

        read            = "0 1 0 1  1 0 0 0  0 0 0 0  1 0 0 0",
                          "x x x x  x x x x  o o o o  o o o o";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "calibration"
        size            = 4;
        read            = "0  0  1  1   1  0  0  0    x x x x  x x x x",
                          "0  0  0  0   0  0 a1 a0    o o o o  o o o o";
    ;

;


#------------------------------------------------------------
# ATtiny261
#------------------------------------------------------------
# Close to ATtiny26

part
    id                  = "t261";
    desc                = "ATtiny261";
     has_debugwire = yes;
     flash_instr   = 0xB4, 0x00, 0x10;
     eeprom_instr  = 0xBB, 0xFF, 0xBB, 0xEE, 0xBB, 0xCC, 0xB2, 0x0D,
	             0xBC, 0x00, 0xB4, 0x00, 0xBA, 0x0D, 0xBB, 0xBC,
	             0x99, 0xE1, 0xBB, 0xAC;
#    stk500_devcode      = 0x21;
#    avr910_devcode      = 0x5e;
    signature           = 0x1e 0x91 0x0c;
    pagel               = 0xb3;
    bs2                 = 0xb2;
    chip_erase_delay    = 4000;

    pgm_enable          = "1 0 1 0  1 1 0 0   0 1 0 1  0 0 1 1",
                          "x x x x  x x x x   x x x x  x x x x";

    chip_erase          = "1 0 1 0  1 1 0 0   1 0 0 x  x x x x",
                          "x x x x  x x x x   x x x x  x x x x";

    timeout		= 200;
    stabdelay		= 100;
    cmdexedelay		= 25;
    synchloops		= 32;
    bytedelay		= 0;
    pollindex		= 3;
    pollvalue		= 0x53;
    predelay		= 1;
    postdelay		= 1;
    pollmethod		= 0;

    pp_controlstack     =
        0xC4, 0xE4, 0xC4, 0xE4, 0xCC, 0xEC, 0xCC, 0xEC,
        0xD4, 0xF4, 0xD4, 0xF4, 0xDC, 0xFC, 0xDC, 0xFC,
        0xC8, 0xE8, 0xD8, 0xF8, 0x4C, 0x6C, 0x5C, 0x7C,
        0xEC, 0xBC, 0x00, 0x06, 0x00, 0x00, 0x00, 0x00;
    hventerstabdelay    = 100;
    progmodedelay       = 0;
    latchcycles         = 5;
    togglevtg           = 1;
    poweroffdelay       = 15;
    resetdelayms        = 2;
    resetdelayus        = 0;
    hvleavestabdelay    = 15;
    chiperasepulsewidth = 0;
    chiperasepolltimeout = 10;
    programfusepulsewidth = 0;
    programfusepolltimeout = 5;
    programlockpulsewidth = 0;
    programlockpolltimeout = 5;

    ocdrev              = 1;

    memory "eeprom"
        paged           = no;
        size            = 128;
        page_size       = 4;
        num_pages       = 32;
        min_write_delay = 4000;
        max_write_delay = 4000;
        readback_p1     = 0xff;
        readback_p2     = 0xff;

        read            = "1  0  1  0   0  0  0  0    x x x x  x x x x",
                          "x a6 a5 a4  a3 a2 a1 a0    o o o o  o o o o";

        write           = "1  1  0  0   0  0  0  0    x x x x  x x x x",
                          "x a6 a5 a4  a3 a2 a1 a0    i i i i  i i i i";

	loadpage_lo	= "  1   1   0   0      0   0   0   1",
			  "  0   0   0   0      0   0   0   0",
			  "  0   0   0   0      0   0  a1  a0",
			  "  i   i   i   i      i   i   i   i";

	writepage	= "  1   1   0   0      0   0   1   0",
			  "  0   0   x   x      x   x   x   x",
			  "  x  a6  a5  a4     a3  a2   0   0",
			  "  x   x   x   x      x   x   x   x";

	mode		= 0x41;
	delay		= 10;
	blocksize	= 4;
	readsize	= 256;
    ;

    memory "flash"
        paged           = yes;
        size            = 2048;
        page_size       = 32;
        num_pages       = 64;
        min_write_delay = 4500;
        max_write_delay = 4500;
        readback_p1     = 0xff;
        readback_p2     = 0xff;

        read_lo         = "  0  0  1  0   0  0  0  0",
                          "  x  x  x  x   x  x a9 a8",
                          " a7 a6 a5 a4  a3 a2 a1 a0",
                          "  o  o  o  o   o  o  o  o";

        read_hi         = "  0  0  1  0   1  0  0  0",
                          "  x  x  x  x   x  x a9 a8",
                          " a7 a6 a5 a4  a3 a2 a1 a0",
                          "  o  o  o  o   o  o  o  o";

        loadpage_lo     = "  0  1  0  0   0  0  0  0",
                          "  x  x  x  x   x  x  x  x",
                          "  x  x  x  x  a3 a2 a1 a0",
                          "  i  i  i  i   i  i  i  i";

        loadpage_hi     = "  0  1  0  0   1  0  0  0",
                          "  x  x  x  x   x  x  x  x",
                          "  x  x  x  x  a3 a2 a1 a0",
                          "  i  i  i  i   i  i  i  i";

        writepage       = "  0  1  0  0   1  1  0  0",
                          "  x  x  x  x   x  x a9 a8",
                          " a7 a6 a5 a4   x  x  x  x",
                          "  x  x  x  x   x  x  x  x";

	mode		= 0x41;
	delay		= 6;
	blocksize	= 32;
	readsize	= 256;
    ;

    memory "signature"
        size            = 3;
        read            = "0  0  1  1   0  0  0  0    x x x x  x x x x",
                          "0  0  0  0   0  0 a1 a0    o o o o  o o o o";
    ;

    memory "lock"
        size            = 1;
        read            = "0  1  0  1   1  0  0  0    x x x x  x x x x",
                          "x  x  x  x   x  x  x  x    x x x x  x x o o";

        write           = "1  0  1  0   1  1  0  0    1 1 1 1  1 1 i i",
                          "x  x  x  x   x  x  x  x    x x x x  x x x x";
        min_write_delay = 4500;
        max_write_delay = 4500;
    ;

    memory "lfuse"
        size            = 1;
        write           = "1 0 1 0  1 1 0 0  1 0 1 0  0 0 0 0",
                          "x x x x  x x x x  i i i i  i i i i";

        read            = "0 1 0 1  0 0 0 0  0 0 0 0  0 0 0 0",
                          "x x x x  x x x x  o o o o  o o o o";
        min_write_delay = 4500;
        max_write_delay = 4500;
      ;

    memory "hfuse"
        size            = 1;
        write           = "1 0 1 0  1 1 0 0  1 0 1 0  1 0 0 0",
                          "x x x x  x x x x  i i i i  i i i i";

        read            = "0 1 0 1  1 0 0 0  0 0 0 0  1 0 0 0",
                          "x x x x  x x x x  o o o o  o o o o";
        min_write_delay = 4500;
        max_write_delay = 4500;
      ;

    memory "efuse"
        size            = 1;
        write           = "1 0 1 0  1 1 0 0   1 0 1 0  0 1 0 0",
                          "x x x x  x x x x   x x x x  x x x i";

        read            = "0 1 0 1  0 0 0 0   0 0 0 0  1 0 0 0",
                          "x x x x  x x x x   o o o o  o o o o";
        min_write_delay = 4500;
        max_write_delay = 4500;
      ;

    memory "calibration"
        size            = 1;
        read            = "0  0  1  1   1  0  0  0    x x x x  x x x x",
                          "0  0  0  0   0  0  0  0    o o o o  o o o o";
    ;

;


#------------------------------------------------------------
# ATtiny461
#------------------------------------------------------------
# Close to ATtiny261

part
    id                  = "t461";
    desc                = "ATtiny461";
     has_debugwire = yes;
     flash_instr   = 0xB4, 0x00, 0x10;
     eeprom_instr  = 0xBB, 0xFF, 0xBB, 0xEE, 0xBB, 0xCC, 0xB2, 0x0D,
	             0xBC, 0x00, 0xB4, 0x00, 0xBA, 0x0D, 0xBB, 0xBC,
	             0x99, 0xE1, 0xBB, 0xAC;
#    stk500_devcode      = 0x21;
#    avr910_devcode      = 0x5e;
    signature           = 0x1e 0x92 0x08;
    pagel               = 0xb3;
    bs2                 = 0xb2;
    chip_erase_delay    = 4000;

    pgm_enable          = "1 0 1 0  1 1 0 0   0 1 0 1  0 0 1 1",
                          "x x x x  x x x x   x x x x  x x x x";

    chip_erase          = "1 0 1 0  1 1 0 0   1 0 0 x  x x x x",
                          "x x x x  x x x x   x x x x  x x x x";

    timeout		= 200;
    stabdelay		= 100;
    cmdexedelay		= 25;
    synchloops		= 32;
    bytedelay		= 0;
    pollindex		= 3;
    pollvalue		= 0x53;
    predelay		= 1;
    postdelay		= 1;
    pollmethod		= 0;

    pp_controlstack     =
        0xC4, 0xE4, 0xC4, 0xE4, 0xCC, 0xEC, 0xCC, 0xEC,
        0xD4, 0xF4, 0xD4, 0xF4, 0xDC, 0xFC, 0xDC, 0xFC,
        0xC8, 0xE8, 0xD8, 0xF8, 0x4C, 0x6C, 0x5C, 0x7C,
        0xEC, 0xBC, 0x00, 0x06, 0x00, 0x00, 0x00, 0x00;
    hventerstabdelay    = 100;
    progmodedelay       = 0;
    latchcycles         = 5;
    togglevtg           = 1;
    poweroffdelay       = 15;
    resetdelayms        = 2;
    resetdelayus        = 0;
    hvleavestabdelay    = 15;
    chiperasepulsewidth = 0;
    chiperasepolltimeout = 10;
    programfusepulsewidth = 0;
    programfusepolltimeout = 5;
    programlockpulsewidth = 0;
    programlockpolltimeout = 5;

    ocdrev              = 1;

    memory "eeprom"
        paged           = no;
        size            = 256;
        page_size       = 4;
        num_pages       = 64;
        min_write_delay = 4000;
        max_write_delay = 4000;
        readback_p1     = 0xff;
        readback_p2     = 0xff;

        read            = " 1  0  1  0   0  0  0  0    x x x x  x x x x",
                          "a7 a6 a5 a4  a3 a2 a1 a0    o o o o  o o o o";

        write           = " 1  1  0  0   0  0  0  0    x x x x  x x x x",
                          "a7 a6 a5 a4  a3 a2 a1 a0    i i i i  i i i i";

	loadpage_lo	= "  1   1   0   0      0   0   0   1",
			  "  0   0   0   0      0   0   0   0",
			  "  0   0   0   0      0   0  a1  a0",
			  "  i   i   i   i      i   i   i   i";

	writepage	= "  1   1   0   0      0   0   1   0",
			  "  0   0   x   x      x   x   x   x",
			  " a7  a6  a5  a4     a3  a2   0   0",
			  "  x   x   x   x      x   x   x   x";

	mode		= 0x41;
	delay		= 10;
	blocksize	= 4;
	readsize	= 256;
    ;

    memory "flash"
        paged           = yes;
        size            = 4096;
        page_size       = 64;
        num_pages       = 64;
        min_write_delay = 4500;
        max_write_delay = 4500;
        readback_p1     = 0xff;
        readback_p2     = 0xff;

        read_lo         = "  0  0  1  0   0   0  0  0",
                          "  x  x  x  x   x a10 a9 a8",
                          " a7 a6 a5 a4  a3  a2 a1 a0",
                          "  o  o  o  o   o   o  o  o";

        read_hi         = "  0  0  1  0   1   0  0  0",
                          "  x  x  x  x   x a10 a9 a8",
                          " a7 a6 a5 a4  a3  a2 a1 a0",
                          "  o  o  o  o   o   o  o  o";

        loadpage_lo     = "  0  1  0  0   0  0  0  0",
                          "  x  x  x  x   x  x  x  x",
                          "  x  x  x a4  a3 a2 a1 a0",
                          "  i  i  i  i   i  i  i  i";

        loadpage_hi     = "  0  1  0  0   1  0  0  0",
                          "  x  x  x  x   x  x  x  x",
                          "  x  x  x a4  a3 a2 a1 a0",
                          "  i  i  i  i   i  i  i  i";

        writepage       = "  0  1  0  0   1   1  0  0",
                          "  x  x  x  x   x a10 a9 a8",
                          " a7 a6 a5  x   x   x  x  x",
                          "  x  x  x  x   x   x  x  x";

	mode		= 0x41;
	delay		= 6;
	blocksize	= 64;
	readsize	= 256;
    ;

    memory "signature"
        size            = 3;
        read            = "0  0  1  1   0  0  0  0    x x x x  x x x x",
                          "0  0  0  0   0  0 a1 a0    o o o o  o o o o";
    ;

    memory "lock"
        size            = 1;
        read            = "0  1  0  1   1  0  0  0    x x x x  x x x x",
                          "x  x  x  x   x  x  x  x    x x x x  x x o o";

        write           = "1  0  1  0   1  1  0  0    1 1 1 1  1 1 i i",
                          "x  x  x  x   x  x  x  x    x x x x  x x x x";
        min_write_delay = 4500;
        max_write_delay = 4500;
    ;

    memory "lfuse"
        size            = 1;
        write           = "1 0 1 0  1 1 0 0  1 0 1 0  0 0 0 0",
                          "x x x x  x x x x  i i i i  i i i i";

        read            = "0 1 0 1  0 0 0 0  0 0 0 0  0 0 0 0",
                          "x x x x  x x x x  o o o o  o o o o";
        min_write_delay = 4500;
        max_write_delay = 4500;
      ;

    memory "hfuse"
        size            = 1;
        write           = "1 0 1 0  1 1 0 0  1 0 1 0  1 0 0 0",
                          "x x x x  x x x x  i i i i  i i i i";

        read            = "0 1 0 1  1 0 0 0  0 0 0 0  1 0 0 0",
                          "x x x x  x x x x  o o o o  o o o o";
        min_write_delay = 4500;
        max_write_delay = 4500;
      ;

    memory "efuse"
        size            = 1;
        write           = "1 0 1 0  1 1 0 0   1 0 1 0  0 1 0 0",
                          "x x x x  x x x x   x x x x  x x x i";

        read            = "0 1 0 1  0 0 0 0   0 0 0 0  1 0 0 0",
                          "x x x x  x x x x   o o o o  o o o o";
        min_write_delay = 4500;
        max_write_delay = 4500;
      ;

    memory "calibration"
        size            = 1;
        read            = "0  0  1  1   1  0  0  0    x x x x  x x x x",
                          "0  0  0  0   0  0  0  0    o o o o  o o o o";
    ;

;


#------------------------------------------------------------
# ATtiny861
#------------------------------------------------------------
# Close to ATtiny461

part
    id                  = "t861";
    desc                = "ATtiny861";
     has_debugwire = yes;
     flash_instr   = 0xB4, 0x00, 0x10;
     eeprom_instr  = 0xBB, 0xFF, 0xBB, 0xEE, 0xBB, 0xCC, 0xB2, 0x0D,
	             0xBC, 0x00, 0xB4, 0x00, 0xBA, 0x0D, 0xBB, 0xBC,
	             0x99, 0xE1, 0xBB, 0xAC;
#    stk500_devcode      = 0x21;
#    avr910_devcode      = 0x5e;
    signature           = 0x1e 0x93 0x0d;
    pagel               = 0xb3;
    bs2                 = 0xb2;
    chip_erase_delay    = 4000;

    pgm_enable          = "1 0 1 0  1 1 0 0   0 1 0 1  0 0 1 1",
                          "x x x x  x x x x   x x x x  x x x x";

    chip_erase          = "1 0 1 0  1 1 0 0   1 0 0 x  x x x x",
                          "x x x x  x x x x   x x x x  x x x x";

    timeout		= 200;
    stabdelay		= 100;
    cmdexedelay		= 25;
    synchloops		= 32;
    bytedelay		= 0;
    pollindex		= 3;
    pollvalue		= 0x53;
    predelay		= 1;
    postdelay		= 1;
    pollmethod		= 0;

    pp_controlstack     =
        0xC4, 0xE4, 0xC4, 0xE4, 0xCC, 0xEC, 0xCC, 0xEC,
        0xD4, 0xF4, 0xD4, 0xF4, 0xDC, 0xFC, 0xDC, 0xFC,
        0xC8, 0xE8, 0xD8, 0xF8, 0x4C, 0x6C, 0x5C, 0x7C,
        0xEC, 0xBC, 0x00, 0x06, 0x00, 0x00, 0x00, 0x00;
    hventerstabdelay    = 100;
    progmodedelay       = 0;
    latchcycles         = 5;
    togglevtg           = 1;
    poweroffdelay       = 15;
    resetdelayms        = 2;
    resetdelayus        = 0;
    hvleavestabdelay    = 15;
    chiperasepulsewidth = 0;
    chiperasepolltimeout = 10;
    programfusepulsewidth = 0;
    programfusepolltimeout = 5;
    programlockpulsewidth = 0;
    programlockpolltimeout = 5;

    ocdrev              = 1;

    memory "eeprom"
        paged           = no;
        size            = 512;
        num_pages       = 128;
        page_size       = 4;
        min_write_delay = 4000;
        max_write_delay = 4000;
        readback_p1     = 0xff;
        readback_p2     = 0xff;

        read            = " 1  0  1  0   0  0  0  0    x x x x  x x x a8",
                          "a7 a6 a5 a4  a3 a2 a1 a0    o o o o  o o o  o";

        write           = " 1  1  0  0   0  0  0  0    x x x x  x x x a8",
                          "a7 a6 a5 a4  a3 a2 a1 a0    i i i i  i i i  i";

	loadpage_lo	= "  1   1   0   0      0   0   0   1",
			  "  0   0   0   0      0   0   0   0",
			  "  0   0   0   0      0   0  a1  a0",
			  "  i   i   i   i      i   i   i   i";

	writepage	= "  1   1   0   0      0   0   1   0",
			  "  0   0   x   x      x   x   x  a8",
			  " a7  a6  a5  a4     a3  a2   0   0",
			  "  x   x   x   x      x   x   x   x";

	mode		= 0x41;
	delay		= 10;
	blocksize	= 4;
	readsize	= 256;
    ;

    memory "flash"
        paged           = yes;
        size            = 8192;
        page_size       = 64;
        num_pages       = 128;
        min_write_delay = 4500;
        max_write_delay = 4500;
        readback_p1     = 0xff;
        readback_p2     = 0xff;

        read_lo         = "  0  0  1  0   0   0  0  0",
                          "  x  x  x  x a11 a10 a9 a8",
                          " a7 a6 a5 a4  a3  a2 a1 a0",
                          "  o  o  o  o   o   o  o  o";

        read_hi         = "  0  0  1  0   1   0  0  0",
                          "  x  x  x  x a11 a10 a9 a8",
                          " a7 a6 a5 a4  a3  a2 a1 a0",
                          "  o  o  o  o   o   o  o  o";

        loadpage_lo     = "  0  1  0  0   0  0  0  0",
                          "  x  x  x  x   x  x  x  x",
                          "  x  x  x a4  a3 a2 a1 a0",
                          "  i  i  i  i   i  i  i  i";

        loadpage_hi     = "  0  1  0  0   1  0  0  0",
                          "  x  x  x  x   x  x  x  x",
                          "  x  x  x a4  a3 a2 a1 a0",
                          "  i  i  i  i   i  i  i  i";

        writepage       = "  0  1  0  0   1   1  0  0",
                          "  x  x  x  x a11 a10 a9 a8",
                          " a7 a6 a5  x   x   x  x  x",
                          "  x  x  x  x   x   x  x  x";

	mode		= 0x41;
	delay		= 6;
	blocksize	= 64;
	readsize	= 256;
    ;

    memory "signature"
        size            = 3;
        read            = "0  0  1  1   0  0  0  0    x x x x  x x x x",
                          "0  0  0  0   0  0 a1 a0    o o o o  o o o o";
    ;

    memory "lock"
        size            = 1;
        read            = "0  1  0  1   1  0  0  0    x x x x  x x x x",
                          "x  x  x  x   x  x  x  x    x x x x  x x o o";

        write           = "1  0  1  0   1  1  0  0    1 1 1 1  1 1 i i",
                          "x  x  x  x   x  x  x  x    x x x x  x x x x";
        min_write_delay = 4500;
        max_write_delay = 4500;
    ;

    memory "lfuse"
        size            = 1;
        write           = "1 0 1 0  1 1 0 0  1 0 1 0  0 0 0 0",
                          "x x x x  x x x x  i i i i  i i i i";

        read            = "0 1 0 1  0 0 0 0  0 0 0 0  0 0 0 0",
                          "x x x x  x x x x  o o o o  o o o o";
        min_write_delay = 4500;
        max_write_delay = 4500;
      ;

    memory "hfuse"
        size            = 1;
        write           = "1 0 1 0  1 1 0 0  1 0 1 0  1 0 0 0",
                          "x x x x  x x x x  i i i i  i i i i";

        read            = "0 1 0 1  1 0 0 0  0 0 0 0  1 0 0 0",
                          "x x x x  x x x x  o o o o  o o o o";
        min_write_delay = 4500;
        max_write_delay = 4500;
      ;

    memory "efuse"
        size            = 1;
        write           = "1 0 1 0  1 1 0 0   1 0 1 0  0 1 0 0",
                          "x x x x  x x x x   x x x x  x x x i";

        read            = "0 1 0 1  0 0 0 0   0 0 0 0  1 0 0 0",
                          "x x x x  x x x x   o o o o  o o o o";
        min_write_delay = 4500;
        max_write_delay = 4500;
      ;

    memory "calibration"
        size            = 1;
        read            = "0  0  1  1   1  0  0  0    x x x x  x x x x",
                          "0  0  0  0   0  0  0  0    o o o o  o o o o";
    ;

;


#------------------------------------------------------------
# ATtiny28
#------------------------------------------------------------

# This is an HVPP-only device.

part
    id                  = "t28";
    desc                = "ATtiny28";
    stk500_devcode      = 0x22;
    avr910_devcode      = 0x5c;
    signature           = 0x1e 0x91 0x07;

    pp_controlstack     =
        0x0E, 0x1E, 0x0F, 0x1F, 0x2E, 0x3E, 0x2F, 0x3F,
        0x4E, 0x5E, 0x4F, 0x5F, 0x6E, 0x7E, 0x6F, 0x7F,
        0x66, 0x76, 0x67, 0x77, 0x6A, 0x7A, 0x6B, 0x7B,
        0xBE, 0xFD, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00;
    hventerstabdelay    = 100;
    progmodedelay       = 0;
    latchcycles         = 0;
    togglevtg           = 0;
    poweroffdelay       = 0;
    resetdelayms        = 0;
    resetdelayus        = 0;
    hvleavestabdelay    = 15;
    resetdelay          = 15;
    chiperasepulsewidth = 0;
    chiperasepolltimeout = 10;
    programfusepulsewidth = 0;
    programfusepolltimeout = 5;
    programlockpulsewidth = 0;
    programlockpolltimeout = 5;

    memory "flash"
        size            = 2048;
        page_size       = 2;
        readsize        = 256;
        delay           = 5;
    ;

    memory "signature"
        size            = 3;
    ;

    memory "lock"
        size            = 1;
    ;

    memory "calibration"
        size            = 1;
    ;

    memory "fuse"
        size            = 1;
    ;
;



#------------------------------------------------------------
# ATmega48
#------------------------------------------------------------

part
    id               = "m48";
    desc             = "ATmega48";
     has_debugwire = yes;
     flash_instr   = 0xB6, 0x01, 0x11;
     eeprom_instr  = 0xBD, 0xF2, 0xBD, 0xE1, 0xBB, 0xCF, 0xB4, 0x00,
	             0xBE, 0x01, 0xB6, 0x01, 0xBC, 0x00, 0xBB, 0xBF,
	             0x99, 0xF9, 0xBB, 0xAF;
    stk500_devcode   = 0x59;
#    avr910_devcode   = 0x;
    signature        = 0x1e 0x92 0x05;
    pagel            = 0xd7;
    bs2              = 0xc2;
    chip_erase_delay = 45000;
    pgm_enable       = "1 0 1 0  1 1 0 0    0 1 0 1  0 0 1 1",
                       "x x x x  x x x x    x x x x  x x x x";

    chip_erase       = "1 0 1 0  1 1 0 0    1 0 0 x  x x x x",
                       "x x x x  x x x x    x x x x  x x x x";

    timeout		= 200;
    stabdelay		= 100;
    cmdexedelay		= 25;
    synchloops		= 32;
    bytedelay		= 0;
    pollindex		= 3;
    pollvalue		= 0x53;
    predelay		= 1;
    postdelay		= 1;
    pollmethod		= 1;

    pp_controlstack     =
	0x0E, 0x1E, 0x0F, 0x1F, 0x2E, 0x3E, 0x2F, 0x3F,
	0x4E, 0x5E, 0x4F, 0x5F, 0x6E, 0x7E, 0x6F, 0x7F,
	0x66, 0x76, 0x67, 0x77, 0x6A, 0x7A, 0x6B, 0x7B,
	0xBE, 0xFD, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00;
    hventerstabdelay    = 100;
    progmodedelay       = 0;
    latchcycles         = 5;
    togglevtg           = 1;
    poweroffdelay       = 15;
    resetdelayms        = 1;
    resetdelayus        = 0;
    hvleavestabdelay    = 15;
    resetdelay          = 15;
    chiperasepulsewidth = 0;
    chiperasepolltimeout = 10;
    programfusepulsewidth = 0;
    programfusepolltimeout = 5;
    programlockpulsewidth = 0;
    programlockpolltimeout = 5;

    ocdrev              = 1;

    memory "eeprom"
        paged           = no;
        page_size       = 4;
        size            = 256;
        min_write_delay = 3600;
        max_write_delay = 3600;
        readback_p1     = 0xff;
        readback_p2     = 0xff;
	read            = "  1   0   1   0      0   0   0   0",
                          "  0   0   0   x      x   x   x   x",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

	write           = "  1   1   0   0      0   0   0   0",
                          "  0   0   0   x      x   x   x   x",
                          " a7  a6  a5  a4     a3  a2  a1  a0", 
                          "  i   i   i   i      i   i   i   i";

	loadpage_lo	= "  1   1   0   0      0   0   0   1",
			  "  0   0   0   0      0   0   0   0",
			  "  0   0   0   0      0   0  a1  a0",
			  "  i   i   i   i      i   i   i   i";

	writepage	= "  1   1   0   0      0   0   1   0",
			  "  0   0   x   x      x   x   x   x",
			  " a7  a6  a5  a4     a3  a2   0   0",
			  "  x   x   x   x      x   x   x   x";

	mode		= 0x41;
	delay		= 20;
	blocksize	= 4;
	readsize	= 256;
      ;
    memory "flash"
        paged           = yes;
        size            = 4096;
        page_size       = 64;
        num_pages       = 64;
        min_write_delay = 4500;
        max_write_delay = 4500;
        readback_p1     = 0x00;
        readback_p2     = 0x00;
        read_lo         = "  0   0   1   0    0   0   0   0",
                          "  0   0   0   0    0 a10  a9  a8",
                          " a7  a6  a5  a4   a3  a2  a1  a0",
                          "  o   o   o   o    o   o   o   o";

        read_hi         = "  0   0   1   0    1   0   0   0",
                          "  0   0   0   0    0 a10  a9  a8",
                          " a7  a6  a5  a4   a3  a2  a1  a0",
                          "  o   o   o   o    o   o   o   o";

        loadpage_lo     = "  0   1   0   0      0   0   0   0",
                          "  0   0   0   x      x   x   x   x",
                          "  x   x   x  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

        loadpage_hi     = "  0   1   0   0      1   0   0   0",
                          "  0   0   0   x      x   x   x   x",
                          "  x   x   x  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

        writepage       = "  0   1   0   0      1   1   0   0",
                          "  0   0   0   0      0 a10  a9  a8",
                          " a7  a6  a5   x      x   x   x   x",
                          "  x   x   x   x      x   x   x   x";

	mode		= 0x41;
	delay		= 6;
	blocksize	= 64;
	readsize	= 256;
      ;

    memory "lfuse"
        size            = 1;
        min_write_delay = 4500;
        max_write_delay = 4500;
        read            = "0 1 0 1  0 0 0 0   0 0 0 0  0 0 0 0",
                          "x x x x  x x x x   o o o o  o o o o";

        write           = "1 0 1 0  1 1 0 0   1 0 1 0  0 0 0 0",
                          "x x x x  x x x x   i i i i  i i i i";
      ;

    memory "hfuse"
        size            = 1;
        min_write_delay = 4500;
        max_write_delay = 4500;
        read            = "0 1 0 1  1 0 0 0   0 0 0 0  1 0 0 0",
                          "x x x x  x x x x   o o o o  o o o o";

        write           = "1 0 1 0  1 1 0 0   1 0 1 0  1 0 0 0",
                          "x x x x  x x x x   i i i i  i i i i";
      ;

    memory "efuse"
        size            = 1;
        min_write_delay = 4500;
        max_write_delay = 4500;
        read            = "0 1 0 1  0 0 0 0   0 0 0 0  1 0 0 0",
                          "x x x x  x x x x   o o o o  o o o o";

        write           = "1 0 1 0  1 1 0 0   1 0 1 0  0 1 0 0",
                          "x x x x  x x x x   x x x x  x x x i";
      ;

    memory "lock"
        size            = 1;
        min_write_delay = 4500;
        max_write_delay = 4500;
        read            = "0 1 0 1  1 0 0 0   0 0 0 0  0 0 0 0",
                          "x x x x  x x x x   x x o o  o o o o";

        write           = "1 0 1 0  1 1 0 0   1 1 1 x  x x x x",
                          "x x x x  x x x x   1 1 i i  i i i i";
      ;

    memory "calibration"
        size            = 1;
        read            = "0  0  1  1   1  0  0  0   0  0  0  x   x  x  x  x",
                          "0  0  0  0   0  0  0  0   o  o  o  o   o  o  o  o";
      ;

    memory "signature"
        size            = 3;
        read            = "0  0  1  1   0  0  0  0   0  0  0  x   x  x  x  x",
                          "x  x  x  x   x  x a1 a0   o  o  o  o   o  o  o  o";
      ;
  ;

#------------------------------------------------------------
# ATmega48P
#------------------------------------------------------------

part parent "m48"
    id               = "m48p";
    desc             = "ATmega48P";
    signature        = 0x1e 0x92 0x0a;

    ocdrev              = 1;
  ;

#------------------------------------------------------------
# ATmega48PB
#------------------------------------------------------------

part parent "m48"
    id               = "m48pb";
    desc             = "ATmega48PB";
    signature        = 0x1e 0x92 0x10;

    ocdrev              = 1;
  ;

#------------------------------------------------------------
# ATmega88
#------------------------------------------------------------

part
    id               = "m88";
    desc             = "ATmega88";
     has_debugwire = yes;
     flash_instr   = 0xB6, 0x01, 0x11;
     eeprom_instr  = 0xBD, 0xF2, 0xBD, 0xE1, 0xBB, 0xCF, 0xB4, 0x00,
	             0xBE, 0x01, 0xB6, 0x01, 0xBC, 0x00, 0xBB, 0xBF,
	             0x99, 0xF9, 0xBB, 0xAF;
    stk500_devcode   = 0x73;
#    avr910_devcode   = 0x;
    signature        = 0x1e 0x93 0x0a;
    pagel            = 0xd7;
    bs2              = 0xc2;
    chip_erase_delay = 9000;
    pgm_enable       = "1 0 1 0  1 1 0 0    0 1 0 1  0 0 1 1",
                       "x x x x  x x x x    x x x x  x x x x";

    chip_erase       = "1 0 1 0  1 1 0 0    1 0 0 x  x x x x",
                       "x x x x  x x x x    x x x x  x x x x";

    timeout		= 200;
    stabdelay		= 100;
    cmdexedelay		= 25;
    synchloops		= 32;
    bytedelay		= 0;
    pollindex		= 3;
    pollvalue		= 0x53;
    predelay		= 1;
    postdelay		= 1;
    pollmethod		= 1;

    pp_controlstack     =
	0x0E, 0x1E, 0x0F, 0x1F, 0x2E, 0x3E, 0x2F, 0x3F,
	0x4E, 0x5E, 0x4F, 0x5F, 0x6E, 0x7E, 0x6F, 0x7F,
	0x66, 0x76, 0x67, 0x77, 0x6A, 0x7A, 0x6B, 0x7B,
	0xBE, 0xFD, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00;
    hventerstabdelay    = 100;
    progmodedelay       = 0;
    latchcycles         = 5;
    togglevtg           = 1;
    poweroffdelay       = 15;
    resetdelayms        = 1;
    resetdelayus        = 0;
    hvleavestabdelay    = 15;
    resetdelay          = 15;
    chiperasepulsewidth = 0;
    chiperasepolltimeout = 10;
    programfusepulsewidth = 0;
    programfusepolltimeout = 5;
    programlockpulsewidth = 0;
    programlockpolltimeout = 5;

    ocdrev              = 1;

    memory "eeprom"
        paged           = no;
        page_size       = 4;
        size            = 512;
        min_write_delay = 3600;
        max_write_delay = 3600;
        readback_p1     = 0xff;
        readback_p2     = 0xff;
	read            = "  1   0   1   0      0   0   0   0",
                          "  0   0   0   x      x   x   x  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

	write           = "  1   1   0   0      0   0   0   0",
                          "  0   0   0   x      x   x   x  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0", 
                          "  i   i   i   i      i   i   i   i";

	loadpage_lo	= "  1   1   0   0      0   0   0   1",
			  "  0   0   0   0      0   0   0   0",
			  "  0   0   0   0      0   0  a1  a0",
			  "  i   i   i   i      i   i   i   i";

	writepage	= "  1   1   0   0      0   0   1   0",
			  "  0   0   x   x      x   x   x  a8",
			  " a7  a6  a5  a4     a3  a2   0   0",
			  "  x   x   x   x      x   x   x   x";

	mode		= 0x41;
	delay		= 20;
	blocksize	= 4;
	readsize	= 256;
      ;
    memory "flash"
        paged           = yes;
        size            = 8192;
        page_size       = 64;
        num_pages       = 128;
        min_write_delay = 4500;
        max_write_delay = 4500;
        readback_p1     = 0xff;
        readback_p2     = 0xff;
        read_lo         = "  0   0   1   0    0   0   0   0",
                          "  0   0   0   0  a11 a10  a9  a8",
                          " a7  a6  a5  a4   a3  a2  a1  a0",
                          "  o   o   o   o    o   o   o   o";

        read_hi         = "  0   0   1   0    1   0   0   0",
                          "  0   0   0   0  a11 a10  a9  a8",
                          " a7  a6  a5  a4   a3  a2  a1  a0",
                          "  o   o   o   o    o   o   o   o";

        loadpage_lo     = "  0   1   0   0      0   0   0   0",
                          "  0   0   0   x      x   x   x   x",
                          "  x   x   x  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

        loadpage_hi     = "  0   1   0   0      1   0   0   0",
                          "  0   0   0   x      x   x   x   x",
                          "  x   x   x  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

        writepage       = "  0   1   0   0      1   1   0   0",
                          "  0   0   0   0    a11 a10  a9  a8",
                          " a7  a6  a5   x      x   x   x   x",
                          "  x   x   x   x      x   x   x   x";

	mode		= 0x41;
	delay		= 6;
	blocksize	= 64;
	readsize	= 256;
      ;

    memory "lfuse"
        size            = 1;
        min_write_delay = 4500;
        max_write_delay = 4500;
        read            = "0 1 0 1  0 0 0 0   0 0 0 0  0 0 0 0",
                          "x x x x  x x x x   o o o o  o o o o";

        write           = "1 0 1 0  1 1 0 0   1 0 1 0  0 0 0 0",
                          "x x x x  x x x x   i i i i  i i i i";
      ;

    memory "hfuse"
        size            = 1;
        min_write_delay = 4500;
        max_write_delay = 4500;
        read            = "0 1 0 1  1 0 0 0   0 0 0 0  1 0 0 0",
                          "x x x x  x x x x   o o o o  o o o o";

        write           = "1 0 1 0  1 1 0 0   1 0 1 0  1 0 0 0",
                          "x x x x  x x x x   i i i i  i i i i";
      ;

    memory "efuse"
        size            = 1;
        min_write_delay = 4500;
        max_write_delay = 4500;
        read            = "0 1 0 1  0 0 0 0   0 0 0 0  1 0 0 0",
                          "x x x x  x x x x   o o o o  o o o o";

        write           = "1 0 1 0  1 1 0 0   1 0 1 0  0 1 0 0",
                          "x x x x  x x x x   x x x x  x i i i";
      ;

    memory "lock"
        size            = 1;
        min_write_delay = 4500;
        max_write_delay = 4500;
        read            = "0 1 0 1  1 0 0 0   0 0 0 0  0 0 0 0",
                          "x x x x  x x x x   x x o o  o o o o";

        write           = "1 0 1 0  1 1 0 0   1 1 1 x  x x x x",
                          "x x x x  x x x x   1 1 i i  i i i i";
      ;

    memory "calibration"
        size            = 1;
        read            = "0  0  1  1   1  0  0  0   0  0  0  x   x  x  x  x",
                          "0  0  0  0   0  0  0  0   o  o  o  o   o  o  o  o";
      ;

    memory "signature"
        size            = 3;
        read            = "0  0  1  1   0  0  0  0   0  0  0  x   x  x  x  x",
                          "x  x  x  x   x  x a1 a0   o  o  o  o   o  o  o  o";
      ;
  ;

#------------------------------------------------------------
# ATmega88P
#------------------------------------------------------------

part parent "m88"
    id               = "m88p";
    desc             = "ATmega88P";
    signature        = 0x1e 0x93 0x0f;

    ocdrev              = 1;
  ;

#------------------------------------------------------------
# ATmega88PB
#------------------------------------------------------------

part parent "m88"
    id               = "m88pb";
    desc             = "ATmega88PB";
    signature        = 0x1e 0x93 0x16;

    ocdrev              = 1;
  ;

#------------------------------------------------------------
# ATmega168
#------------------------------------------------------------

part
    id              = "m168";
    desc            = "ATmega168";
     has_debugwire = yes;
     flash_instr   = 0xB6, 0x01, 0x11;
     eeprom_instr  = 0xBD, 0xF2, 0xBD, 0xE1, 0xBB, 0xCF, 0xB4, 0x00,
	             0xBE, 0x01, 0xB6, 0x01, 0xBC, 0x00, 0xBB, 0xBF,
	             0x99, 0xF9, 0xBB, 0xAF;
    stk500_devcode  = 0x86;
    # avr910_devcode = 0x;
    signature       = 0x1e 0x94 0x06;
    pagel           = 0xd7;
    bs2             = 0xc2;
    chip_erase_delay = 9000;
    pgm_enable       = "1 0 1 0 1 1 0 0 0 1 0 1 0 0 1 1",
                       "x x x x x x x x x x x x x x x x";

    chip_erase       = "1 0 1 0 1 1 0 0 1 0 0 x x x x x",
                       "x x x x x x x x x x x x x x x x";

    timeout         = 200;
    stabdelay       = 100;
    cmdexedelay     = 25;
    synchloops      = 32;
    bytedelay       = 0;
    pollindex       = 3;
    pollvalue       = 0x53;
    predelay        = 1;
    postdelay       = 1;
    pollmethod      = 1;

    pp_controlstack     =
	0x0E, 0x1E, 0x0F, 0x1F, 0x2E, 0x3E, 0x2F, 0x3F,
	0x4E, 0x5E, 0x4F, 0x5F, 0x6E, 0x7E, 0x6F, 0x7F,
	0x66, 0x76, 0x67, 0x77, 0x6A, 0x7A, 0x6B, 0x7B,
	0xBE, 0xFD, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00;
    hventerstabdelay    = 100;
    progmodedelay       = 0;
    latchcycles         = 5;
    togglevtg           = 1;
    poweroffdelay       = 15;
    resetdelayms        = 1;
    resetdelayus        = 0;
    hvleavestabdelay    = 15;
    resetdelay          = 15;
    chiperasepulsewidth = 0;
    chiperasepolltimeout = 10;
    programfusepulsewidth = 0;
    programfusepolltimeout = 5;
    programlockpulsewidth = 0;
    programlockpolltimeout = 5;

    ocdrev              = 1;

    memory "eeprom"
        paged           = no;
        page_size       = 4;
        size            = 512;
        min_write_delay = 3600;
        max_write_delay = 3600;
        readback_p1     = 0xff;
        readback_p2     = 0xff;
        read            = " 1 0 1 0 0 0 0 0",
                          " 0 0 0 x x x x a8",
                          " a7 a6 a5 a4 a3 a2 a1 a0",
                          " o o o o o o o o";
    
        write           = " 1 1 0 0 0 0 0 0",
                          " 0 0 0 x x x x a8",
                          " a7 a6 a5 a4 a3 a2 a1 a0",
                          " i i i i i i i i";

	loadpage_lo	= "  1   1   0   0      0   0   0   1",
			  "  0   0   0   0      0   0   0   0",
			  "  0   0   0   0      0   0  a1  a0",
			  "  i   i   i   i      i   i   i   i";

	writepage	= "  1   1   0   0      0   0   1   0",
			  "  0   0   x   x      x   x   x  a8",
			  " a7  a6  a5  a4     a3  a2   0   0",
			  "  x   x   x   x      x   x   x   x";

	mode		= 0x41;
	delay		= 20;
	blocksize	= 4;
	readsize	= 256;
        ;

    memory "flash"
        paged           = yes;
        size            = 16384;
        page_size       = 128;
        num_pages       = 128;
        min_write_delay = 4500;
        max_write_delay = 4500;
        readback_p1     = 0xff;
        readback_p2     = 0xff;
        read_lo         = " 0 0 1 0 0 0 0 0",
                          " 0 0 0 a12 a11 a10 a9 a8",
                          " a7 a6 a5 a4 a3 a2 a1 a0",
                          " o o o o o o o o";
        
        read_hi          = " 0 0 1 0 1 0 0 0",
                           " 0 0 0 a12 a11 a10 a9 a8",
                           " a7 a6 a5 a4 a3 a2 a1 a0",
                           " o o o o o o o o";
        
        loadpage_lo     = " 0 1 0 0 0 0 0 0",
                          " 0 0 0 x x x x x",
                          " x x a5 a4 a3 a2 a1 a0",
                          " i i i i i i i i";
        
        loadpage_hi     = " 0 1 0 0 1 0 0 0",
                          " 0 0 0 x x x x x",
                          " x x a5 a4 a3 a2 a1 a0",
                          " i i i i i i i i";
        
        writepage       = " 0 1 0 0 1 1 0 0",
                          " 0 0 0 a12 a11 a10 a9 a8",
                          " a7 a6 x x x x x x",
                          " x x x x x x x x";

        mode        = 0x41;
        delay       = 6;
        blocksize   = 128;
        readsize    = 256;

        ;
        
    memory "lfuse"
        size            = 1;
        min_write_delay = 4500;
        max_write_delay = 4500;
        read            = "0 1 0 1 0 0 0 0 0 0 0 0 0 0 0 0",
                          "x x x x x x x x o o o o o o o o";
        
        write           = "1 0 1 0 1 1 0 0 1 0 1 0 0 0 0 0",
                          "x x x x x x x x i i i i i i i i";
        ;
    
    memory "hfuse"
        size            = 1;
        min_write_delay = 4500;
        max_write_delay = 4500;
        read            = "0 1 0 1 1 0 0 0 0 0 0 0 1 0 0 0",
                          "x x x x x x x x o o o o o o o o";
        
        write           = "1 0 1 0 1 1 0 0 1 0 1 0 1 0 0 0",
                          "x x x x x x x x i i i i i i i i";
        ;
    
    memory "efuse"
        size            = 1;
        min_write_delay = 4500;
        max_write_delay = 4500;
        read            = "0 1 0 1 0 0 0 0 0 0 0 0 1 0 0 0",
                          "x x x x x x x x o o o o o o o o";
        
        write           = "1 0 1 0 1 1 0 0 1 0 1 0 0 1 0 0",
                          "x x x x x x x x x x x x x i i i";
        ;
    
    memory "lock"
        size            = 1;
        min_write_delay = 4500;
        max_write_delay = 4500;
        read            = "0 1 0 1 1 0 0 0 0 0 0 0 0 0 0 0",
                          "x x x x x x x x x x o o o o o o";
        
        write           = "1 0 1 0 1 1 0 0 1 1 1 x x x x x",
                          "x x x x x x x x 1 1 i i i i i i";
        ;
    
    memory "calibration"
        size            = 1;
        read            = "0 0 1 1 1 0 0 0 0 0 0 x x x x x",
                          "0 0 0 0 0 0 0 0 o o o o o o o o";
        ;
    
    memory "signature"
        size            = 3;
        read            = "0 0 1 1 0 0 0 0 0 0 0 x x x x x",
                          "x x x x x x a1 a0 o o o o o o o o";
        ;
;

#------------------------------------------------------------
# ATmega168P
#------------------------------------------------------------

part parent "m168"
    id              = "m168p";
    desc            = "ATmega168P";
    signature       = 0x1e 0x94 0x0b;

    ocdrev              = 1;
;

#------------------------------------------------------------
# ATmega168PB
#------------------------------------------------------------

part parent "m168"
    id              = "m168pb";
    desc            = "ATmega168PB";
    signature       = 0x1e 0x94 0x15;

    ocdrev              = 1;
;

#------------------------------------------------------------
# ATtiny88
#------------------------------------------------------------

part
    id               = "t88";
    desc             = "ATtiny88";
     has_debugwire = yes;
     flash_instr   = 0xB6, 0x01, 0x11;
     eeprom_instr  = 0xBD, 0xF2, 0xBD, 0xE1, 0xBB, 0xCF, 0xB4, 0x00,
	             0xBE, 0x01, 0xB6, 0x01, 0xBC, 0x00, 0xBB, 0xBF,
	             0x99, 0xF9, 0xBB, 0xAF;
    stk500_devcode   = 0x73;
#    avr910_devcode   = 0x;
    signature        = 0x1e 0x93 0x11;
    pagel            = 0xd7;
    bs2              = 0xc2;
    chip_erase_delay = 9000;
    pgm_enable       = "1 0 1 0  1 1 0 0    0 1 0 1  0 0 1 1",
                       "x x x x  x x x x    x x x x  x x x x";

    chip_erase       = "1 0 1 0  1 1 0 0    1 0 0 x  x x x x",
                       "x x x x  x x x x    x x x x  x x x x";

    timeout		= 200;
    stabdelay		= 100;
    cmdexedelay		= 25;
    synchloops		= 32;
    bytedelay		= 0;
    pollindex		= 3;
    pollvalue		= 0x53;
    predelay		= 1;
    postdelay		= 1;
    pollmethod		= 1;

    pp_controlstack     =
	0x0E, 0x1E, 0x0F, 0x1F, 0x2E, 0x3E, 0x2F, 0x3F,
	0x4E, 0x5E, 0x4F, 0x5F, 0x6E, 0x7E, 0x6F, 0x7F,
	0x66, 0x76, 0x67, 0x77, 0x6A, 0x7A, 0x6B, 0x7B,
	0xBE, 0xFD, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00;
    hventerstabdelay    = 100;
    progmodedelay       = 0;
    latchcycles         = 5;
    togglevtg           = 1;
    poweroffdelay       = 15;
    resetdelayms        = 1;
    resetdelayus        = 0;
    hvleavestabdelay    = 15;
    resetdelay          = 15;
    chiperasepulsewidth = 0;
    chiperasepolltimeout = 10;
    programfusepulsewidth = 0;
    programfusepolltimeout = 5;
    programlockpulsewidth = 0;
    programlockpolltimeout = 5;

    ocdrev              = 1;

    memory "eeprom"
        paged           = no;
        page_size       = 4;
        size            = 64;
        min_write_delay = 3600;
        max_write_delay = 3600;
        readback_p1     = 0xff;
        readback_p2     = 0xff;
	read            = "  1   0   1   0      0   0   0   0",
                          "  0   0   0   x      x   x   x   x",
                          "  x  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

	write           = "  1   1   0   0      0   0   0   0",
                          "  0   0   0   x      x   x   x   x",
                          "  x  a6  a5  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

	loadpage_lo	= "  1   1   0   0      0   0   0   1",
			  "  0   0   0   0      0   0   0   0",
			  "  0   0   0   0      0   0  a1  a0",
			  "  i   i   i   i      i   i   i   i";

	writepage	= "  1   1   0   0      0   0   1   0",
			  "  0   0   x   x      x   x   x   x",
			  "  x  a6  a5  a4     a3  a2   0   0",
			  "  x   x   x   x      x   x   x   x";

	mode		= 0x41;
	delay		= 20;
	blocksize	= 4;
	readsize	= 64;
      ;
    memory "flash"
        paged           = yes;
        size            = 8192;
        page_size       = 64;
        num_pages       = 128;
        min_write_delay = 4500;
        max_write_delay = 4500;
        readback_p1     = 0xff;
        readback_p2     = 0xff;
        read_lo         = "  0   0   1   0    0   0   0   0",
                          "  0   0   0   0  a11 a10  a9  a8",
                          " a7  a6  a5  a4   a3  a2  a1  a0",
                          "  o   o   o   o    o   o   o   o";

        read_hi         = "  0   0   1   0    1   0   0   0",
                          "  0   0   0   0  a11 a10  a9  a8",
                          " a7  a6  a5  a4   a3  a2  a1  a0",
                          "  o   o   o   o    o   o   o   o";

        loadpage_lo     = "  0   1   0   0      0   0   0   0",
                          "  0   0   0   x      x   x   x   x",
                          "  x   x   x  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

        loadpage_hi     = "  0   1   0   0      1   0   0   0",
                          "  0   0   0   x      x   x   x   x",
                          "  x   x   x  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

        writepage       = "  0   1   0   0      1   1   0   0",
                          "  0   0   0   0    a11 a10  a9  a8",
                          " a7  a6  a5   x      x   x   x   x",
                          "  x   x   x   x      x   x   x   x";

	mode		= 0x41;
	delay		= 6;
	blocksize	= 64;
	readsize	= 256;
      ;

    memory "lfuse"
        size            = 1;
        min_write_delay = 4500;
        max_write_delay = 4500;
        read            = "0 1 0 1  0 0 0 0   0 0 0 0  0 0 0 0",
                          "x x x x  x x x x   o o o o  o o o o";

        write           = "1 0 1 0  1 1 0 0   1 0 1 0  0 0 0 0",
                          "x x x x  x x x x   i i i i  i i i i";
      ;

    memory "hfuse"
        size            = 1;
        min_write_delay = 4500;
        max_write_delay = 4500;
        read            = "0 1 0 1  1 0 0 0   0 0 0 0  1 0 0 0",
                          "x x x x  x x x x   o o o o  o o o o";

        write           = "1 0 1 0  1 1 0 0   1 0 1 0  1 0 0 0",
                          "x x x x  x x x x   i i i i  i i i i";
      ;

    memory "efuse"
        size            = 1;
        min_write_delay = 4500;
        max_write_delay = 4500;
        read            = "0 1 0 1  0 0 0 0   0 0 0 0  1 0 0 0",
                          "x x x x  x x x x   o o o o  o o o o";

        write           = "1 0 1 0  1 1 0 0   1 0 1 0  0 1 0 0",
                          "x x x x  x x x x   x x x x  x x x i";
      ;

    memory "lock"
        size            = 1;
        min_write_delay = 4500;
        max_write_delay = 4500;
        read            = "0 1 0 1  1 0 0 0   0 0 0 0  0 0 0 0",
                          "x x x x  x x x x   x x o o  o o o o";

        write           = "1 0 1 0  1 1 0 0   1 1 1 x  x x x x",
                          "x x x x  x x x x   1 1 i i  i i i i";
      ;

    memory "calibration"
        size            = 1;
        read            = "0  0  1  1   1  0  0  0   0  0  0  x   x  x  x  x",
                          "0  0  0  0   0  0  0  0   o  o  o  o   o  o  o  o";
      ;

    memory "signature"
        size            = 3;
        read            = "0  0  1  1   0  0  0  0   0  0  0  x   x  x  x  x",
                          "x  x  x  x   x  x a1 a0   o  o  o  o   o  o  o  o";
      ;
  ;

#------------------------------------------------------------
# ATmega328
#------------------------------------------------------------

part
    id			= "m328";
    desc		= "ATmega328";
    has_debugwire	= yes;
    flash_instr		= 0xB6, 0x01, 0x11;
    eeprom_instr	= 0xBD, 0xF2, 0xBD, 0xE1, 0xBB, 0xCF, 0xB4, 0x00,
			  0xBE, 0x01, 0xB6, 0x01, 0xBC, 0x00, 0xBB, 0xBF,
			  0x99, 0xF9, 0xBB, 0xAF;
    stk500_devcode	= 0x86;
    # avr910_devcode	= 0x;
    signature		= 0x1e 0x95 0x14;
    pagel		= 0xd7;
    bs2			= 0xc2;
    chip_erase_delay	= 9000;
    pgm_enable = "1 0 1 0 1 1 0 0 0 1 0 1 0 0 1 1",
		 "x x x x x x x x x x x x x x x x";

    chip_erase = "1 0 1 0 1 1 0 0 1 0 0 x x x x x",
		 "x x x x x x x x x x x x x x x x";

    timeout	= 200;
    stabdelay	= 100;
    cmdexedelay	= 25;
    synchloops	= 32;
    bytedelay	= 0;
    pollindex	= 3;
    pollvalue	= 0x53;
    predelay	= 1;
    postdelay	= 1;
    pollmethod	= 1;

    pp_controlstack =
	0x0E, 0x1E, 0x0F, 0x1F, 0x2E, 0x3E, 0x2F, 0x3F,
	0x4E, 0x5E, 0x4F, 0x5F, 0x6E, 0x7E, 0x6F, 0x7F,
	0x66, 0x76, 0x67, 0x77, 0x6A, 0x7A, 0x6B, 0x7B,
	0xBE, 0xFD, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00;
    hventerstabdelay	= 100;
    progmodedelay	= 0;
    latchcycles		= 5;
    togglevtg		= 1;
    poweroffdelay	= 15;
    resetdelayms	= 1;
    resetdelayus	= 0;
    hvleavestabdelay	= 15;
    resetdelay		= 15;
    chiperasepulsewidth	= 0;
    chiperasepolltimeout = 10;
    programfusepulsewidth = 0;
    programfusepolltimeout = 5;
    programlockpulsewidth = 0;
    programlockpolltimeout = 5;

    ocdrev              = 1;

    memory "eeprom"
	paged		= no;
	page_size	= 4;
	size		= 1024;
	min_write_delay = 3600;
	max_write_delay = 3600;
	readback_p1	= 0xff;
	readback_p2	= 0xff;
	read = " 1 0 1 0 0 0 0 0",
	       " 0 0 0 x x x a9 a8",
	       " a7 a6 a5 a4 a3 a2 a1 a0",
	       " o o o o o o o o";

	write = " 1 1 0 0 0 0 0 0",
	      	" 0 0 0 x x x a9 a8",
		" a7 a6 a5 a4 a3 a2 a1 a0",
		" i i i i i i i i";

	loadpage_lo = " 1 1 0 0 0 0 0 1",
		      " 0 0 0 0 0 0 0 0",
		      " 0 0 0 0 0 0 a1 a0",
		      " i i i i i i i i";

	writepage = " 1 1 0 0 0 0 1 0",
		    " 0 0 x x x x a9 a8",
		    " a7 a6 a5 a4 a3 a2 0 0",
		    " x x x x x x x x";

	mode		= 0x41;
	delay		= 20;
	blocksize	= 4;
	readsize	= 256;
    ;

    memory "flash"
	paged		= yes;
	size		= 32768;
	page_size	= 128;
	num_pages	= 256;
	min_write_delay = 4500;
	max_write_delay = 4500;
	readback_p1	= 0xff;
	readback_p2	= 0xff;
	read_lo = " 0 0 1 0 0 0 0 0",
		  " 0 0 a13 a12 a11 a10 a9 a8",
		  " a7 a6 a5 a4 a3 a2 a1 a0",
		  " o o o o o o o o";

	read_hi = " 0 0 1 0 1 0 0 0",
		  " 0 0 a13 a12 a11 a10 a9 a8",
		  " a7 a6 a5 a4 a3 a2 a1 a0",
		  " o o o o o o o o";

	loadpage_lo = " 0 1 0 0 0 0 0 0",
		      " 0 0 0 x x x x x",
		      " x x a5 a4 a3 a2 a1 a0",
		      " i i i i i i i i";

	loadpage_hi = " 0 1 0 0 1 0 0 0",
		      " 0 0 0 x x x x x",
		      " x x a5 a4 a3 a2 a1 a0",
		      " i i i i i i i i";

	writepage = " 0 1 0 0 1 1 0 0",
		    " 0 0 a13 a12 a11 a10 a9 a8",
		    " a7 a6 x x x x x x",
		    " x x x x x x x x";

	mode		= 0x41;
	delay		= 6;
	blocksize	= 128;
	readsize	= 256;

    ;

    memory "lfuse"
	size = 1;
	min_write_delay = 4500;
	max_write_delay = 4500;
	read = "0 1 0 1 0 0 0 0 0 0 0 0 0 0 0 0",
	       "x x x x x x x x o o o o o o o o";

	write = "1 0 1 0 1 1 0 0 1 0 1 0 0 0 0 0",
	      	"x x x x x x x x i i i i i i i i";
    ;

    memory "hfuse"
	size = 1;
	min_write_delay = 4500;
	max_write_delay = 4500;
	read = "0 1 0 1 1 0 0 0 0 0 0 0 1 0 0 0",
	       "x x x x x x x x o o o o o o o o";

	write = "1 0 1 0 1 1 0 0 1 0 1 0 1 0 0 0",
	      	"x x x x x x x x i i i i i i i i";
    ;

    memory "efuse"
	size = 1;
	min_write_delay = 4500;
	max_write_delay = 4500;
	read = "0 1 0 1 0 0 0 0 0 0 0 0 1 0 0 0",
	       "x x x x x x x x o o o o o o o o";

	write = "1 0 1 0 1 1 0 0 1 0 1 0 0 1 0 0",
	      	"x x x x x x x x x x x x x i i i";
    ;

    memory "lock"
	size = 1;
	min_write_delay = 4500;
	max_write_delay = 4500;
	read = "0 1 0 1 1 0 0 0 0 0 0 0 0 0 0 0",
	       "x x x x x x x x x x o o o o o o";

	write = "1 0 1 0 1 1 0 0 1 1 1 x x x x x",
	      	"x x x x x x x x 1 1 i i i i i i";
    ;

    memory "calibration"
	size = 1;
	read = "0 0 1 1 1 0 0 0 0 0 0 x x x x x",
	       "0 0 0 0 0 0 0 0 o o o o o o o o";
    ;

    memory "signature"
	size = 3;
	read = "0 0 1 1 0 0 0 0 0 0 0 x x x x x",
	       "x x x x x x a1 a0 o o o o o o o o";
    ;
;

part parent "m328"
    id			= "m328p";
    desc		= "ATmega328P";
    signature		= 0x1e 0x95 0x0F;

    ocdrev              = 1;
;

#------------------------------------------------------------
# ATmega32m1
#------------------------------------------------------------

part parent "m328"
    id              = "m32m1";
    desc            = "ATmega32M1";
    # stk500_devcode	= 0x;
    # avr910_devcode	= 0x;
    signature       = 0x1e 0x95 0x84;
    bs2             = 0xe2;

    memory "efuse"
        read            = "0 1 0 1 0 0 0 0 0 0 0 0 1 0 0 0",
                          "x x x x x x x x o o o o o o o o";
        write           = "1 0 1 0 1 1 0 0 1 0 1 0 0 1 0 0",
                          "x x x x x x x x x x i i i i i i";
    ;
;

#------------------------------------------------------------
# ATtiny2313
#------------------------------------------------------------

part
     id            = "t2313";
     desc          = "ATtiny2313";
     has_debugwire = yes;
     flash_instr   = 0xB2, 0x0F, 0x1F;
     eeprom_instr  = 0xBB, 0xFE, 0xBB, 0xEE, 0xBB, 0xCC, 0xB2, 0x0D,
	             0xBA, 0x0F, 0xB2, 0x0F, 0xBA, 0x0D, 0xBB, 0xBC,
	             0x99, 0xE1, 0xBB, 0xAC;
     stk500_devcode   = 0x23;
##   Use the ATtiny26 devcode:
     avr910_devcode   = 0x5e;
     signature        = 0x1e 0x91 0x0a;
     pagel            = 0xD4;
     bs2              = 0xD6;
     reset            = io;
     chip_erase_delay = 9000;

     pgm_enable       = "1 0 1 0  1 1 0 0    0 1 0 1  0 0 1 1",
                        "x x x x  x x x x    x x x x  x x x x";

     chip_erase       = "1 0 1 0  1 1 0 0    1 0 0 x  x x x x",
                        "x x x x  x x x x    x x x x  x x x x";

    timeout		= 200;
    stabdelay		= 100;
    cmdexedelay		= 25;
    synchloops		= 32;
    bytedelay		= 0;
    pollindex		= 3;
    pollvalue		= 0x53;
    predelay		= 1;
    postdelay		= 1;
    pollmethod		= 1;

    pp_controlstack     =
        0x0E, 0x1E, 0x0E, 0x1E, 0x2E, 0x3E, 0x2E, 0x3E,
        0x4E, 0x5E, 0x4E, 0x5E, 0x6E, 0x7E, 0x6E, 0x7E,
        0x26, 0x36, 0x66, 0x76, 0x2A, 0x3A, 0x6A, 0x7A,
        0x2E, 0xFD, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00;
    hventerstabdelay    = 100;
    progmodedelay       = 0;
    latchcycles         = 5;
    togglevtg           = 1;
    poweroffdelay       = 15;
    resetdelayms        = 1;
    resetdelayus        = 0;
    hvleavestabdelay    = 15;
    chiperasepulsewidth = 0;
    chiperasepolltimeout = 10;
    programfusepulsewidth = 0;
    programfusepolltimeout = 5;
    programlockpulsewidth = 0;
    programlockpolltimeout = 5;

    ocdrev              = 0;

     memory "eeprom"
         size            = 128;
        paged           = no;
        page_size       = 4;
         min_write_delay = 4000;
         max_write_delay = 4500;
         readback_p1     = 0xff;
         readback_p2     = 0xff;
         read            = "1  0  1  0   0  0  0  0   0 0 0 x  x x x x",
                           "x a6 a5 a4  a3 a2 a1 a0   o o o o  o o o o";

         write           = "1  1  0  0   0  0  0  0   0 0 0 x  x x x x",
                           "x a6 a5 a4  a3 a2 a1 a0   i i i i  i i i i";

	loadpage_lo	= "  1   1   0   0      0   0   0   1",
			  "  0   0   0   0      0   0   0   0",
			  "  0   0   0   0      0   0  a1  a0",
			  "  i   i   i   i      i   i   i   i";

	writepage	= "  1   1   0   0      0   0   1   0",
			  "  0   0   x   x      x   x   x   x",
			  "  x  a6  a5  a4     a3  a2   0   0",
			  "  x   x   x   x      x   x   x   x";

	mode		= 0x41;
	delay		= 6;
	blocksize	= 4;
	readsize	= 256;
       ;
     memory "flash"
         paged           = yes;
         size            = 2048;
         page_size       = 32;
         num_pages       = 64;
         min_write_delay = 4500;
         max_write_delay = 4500;
         readback_p1     = 0xff;
         readback_p2     = 0xff;
         read_lo         = "  0   0   1   0    0   0   0   0",
                           "  0   0   0   0    0   0  a9  a8",
                           " a7  a6  a5  a4   a3  a2  a1  a0",
                           "  o   o   o   o    o   o   o   o";

         read_hi         = "  0   0   1   0    1   0   0   0",
                           "  0   0   0   0    0   0  a9  a8",
                           " a7  a6  a5  a4   a3  a2  a1  a0",
                           "  o   o   o   o    o   o   o   o";

# The information in the data sheet of April/2004 is wrong, this works:
         loadpage_lo     = "  0   1   0   0    0   0   0   0",
                           "  0   0   0   x    x   x   x   x",
                           "  x   x   x   x   a3  a2  a1  a0",
                           "  i   i   i   i    i   i   i   i";

# The information in the data sheet of April/2004 is wrong, this works:
         loadpage_hi     = "  0   1   0   0    1   0   0   0",
                           "  0   0   0   x    x   x   x   x",
                           "  x   x   x   x   a3  a2  a1  a0",
                           "  i   i   i   i    i   i   i   i";

# The information in the data sheet of April/2004 is wrong, this works:
         writepage       = "  0  1  0  0   1  1  0  0",
                           "  0  0  0  0   0  0 a9 a8",
                           " a7 a6 a5 a4   x  x  x  x",
                           "  x  x  x  x   x  x  x  x";

	mode		= 0x41;
	delay		= 6;
	blocksize	= 32;
	readsize	= 256;
       ;
#   ATtiny2313 has Signature Bytes: 0x1E 0x91 0x0A.
     memory "signature"
         size            = 3;
         read            = "0  0  1  1   0  0  0  0   0  0  0  x   x  x  x  x",
                           "x  x  x  x   x  x a1 a0   o  o  o  o   o  o  o  o";
       ;
     memory "lock"
         size            = 1;
         write           = "1 0 1 0  1 1 0 0  1 1 1 x  x x x x",
                           "x x x x  x x x x  1 1 i i  i i i i";
         read           = "0 1 0 1  1 0 0 0  0 0 0 0  0 0 0 0",
                          "x x x x  x x x x  x x o o  o o o o";
        min_write_delay = 9000;
        max_write_delay = 9000;
       ;

     memory "lfuse"
         size            = 1;
         write           = "1 0 1 0  1 1 0 0  1 0 1 0  0 0 0 0",
                           "x x x x  x x x x  i i i i  i i i i";

         read            = "0 1 0 1  0 0 0 0  0 0 0 0  0 0 0 0",
                           "x x x x  x x x x  o o o o  o o o o";
        min_write_delay = 9000;
        max_write_delay = 9000;
       ;

     memory "hfuse"
         size            = 1;
         write           = "1 0 1 0  1 1 0 0  1 0 1 0  1 0 0 0",
                           "x x x x  x x x x  i i i i  i i i i";

         read            = "0 1 0 1  1 0 0 0  0 0 0 0  1 0 0 0",
                           "x x x x  x x x x  o o o o  o o o o";
        min_write_delay = 9000;
        max_write_delay = 9000;
       ;

     memory "efuse"
         size            = 1;
         write           = "1 0 1 0  1 1 0 0  1 0 1 0  0 1 0 0",
                           "x x x x  x x x x  x x x x  x x x i";

         read            = "0 1 0 1  0 0 0 0  0 0 0 0  1 0 0 0",
                           "x x x x  x x x x  o o o o  o o o o";
        min_write_delay = 9000;
        max_write_delay = 9000;
     ;
# The Tiny2313 has calibration data for both 4 MHz and 8 MHz.
# The information in the data sheet of April/2004 is wrong, this works:

     memory "calibration"
         size            = 2;
         read            = "0  0  1  1   1  0  0  0    0 0 0 x  x x x x",
                           "0  0  0  0   0  0  0  a0   o o o o  o o o o";
     ;
  ;

#------------------------------------------------------------
# ATtiny4313
#------------------------------------------------------------

part
     id            = "t4313";
     desc          = "ATtiny4313";
     has_debugwire = yes;
     flash_instr   = 0xB2, 0x0F, 0x1F;
     eeprom_instr  = 0xBB, 0xFE, 0xBB, 0xEE, 0xBB, 0xCC, 0xB2, 0x0D,
	             0xBA, 0x0F, 0xB2, 0x0F, 0xBA, 0x0D, 0xBB, 0xBC,
	             0x99, 0xE1, 0xBB, 0xAC;
     stk500_devcode   = 0x23;
##   Use the ATtiny26 devcode:
     avr910_devcode   = 0x5e;
     signature        = 0x1e 0x92 0x0d;
     pagel            = 0xD4;
     bs2              = 0xD6;
     reset            = io;
     chip_erase_delay = 9000;

     pgm_enable       = "1 0 1 0  1 1 0 0    0 1 0 1  0 0 1 1",
                        "x x x x  x x x x    x x x x  x x x x";

     chip_erase       = "1 0 1 0  1 1 0 0    1 0 0 x  x x x x",
                        "x x x x  x x x x    x x x x  x x x x";

    timeout		= 200;
    stabdelay		= 100;
    cmdexedelay		= 25;
    synchloops		= 32;
    bytedelay		= 0;
    pollindex		= 3;
    pollvalue		= 0x53;
    predelay		= 1;
    postdelay		= 1;
    pollmethod		= 1;

    pp_controlstack     =
        0x0E, 0x1E, 0x0E, 0x1E, 0x2E, 0x3E, 0x2E, 0x3E,
        0x4E, 0x5E, 0x4E, 0x5E, 0x6E, 0x7E, 0x6E, 0x7E,
        0x26, 0x36, 0x66, 0x76, 0x2A, 0x3A, 0x6A, 0x7A,
        0x2E, 0xFD, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00;
    hventerstabdelay    = 100;
    progmodedelay       = 0;
    latchcycles         = 5;
    togglevtg           = 1;
    poweroffdelay       = 15;
    resetdelayms        = 1;
    resetdelayus        = 0;
    hvleavestabdelay    = 15;
    chiperasepulsewidth = 0;
    chiperasepolltimeout = 10;
    programfusepulsewidth = 0;
    programfusepolltimeout = 5;
    programlockpulsewidth = 0;
    programlockpolltimeout = 5;

    ocdrev              = 0;

     memory "eeprom"
         size            = 256;
        paged           = no;
        page_size       = 4;
         min_write_delay = 4000;
         max_write_delay = 4500;
         readback_p1     = 0xff;
         readback_p2     = 0xff;
         read            = "1   0  1  0   0  0  0  0   0 0 0 x  x x x x",
                           "a7 a6 a5 a4  a3 a2 a1 a0   o o o o  o o o o";

         write           = "1   1  0  0   0  0  0  0   0 0 0 x  x x x x",
                           "a7 a6 a5 a4  a3 a2 a1 a0   i i i i  i i i i";

	loadpage_lo	= "  1   1   0   0      0   0   0   1",
			  "  0   0   0   0      0   0   0   0",
			  "  0   0   0   0      0   0  a1  a0",
			  "  i   i   i   i      i   i   i   i";

	writepage	= "  1   1   0   0      0   0   1   0",
			  "  0   0   x   x      x   x   x   x",
			  " a7  a6  a5  a4     a3  a2   0   0",
			  "  x   x   x   x      x   x   x   x";

	mode		= 0x41;
	delay		= 6;
	blocksize	= 4;
	readsize	= 256;
       ;
     memory "flash"
         paged           = yes;
         size            = 4096;
         page_size       = 64;
         num_pages       = 64;
         min_write_delay = 4500;
         max_write_delay = 4500;
         readback_p1     = 0xff;
         readback_p2     = 0xff;
         read_lo         = "  0   0   1   0    0   0   0   0",
                           "  0   0   0   0    0 a10  a9  a8",
                           " a7  a6  a5  a4   a3  a2  a1  a0",
                           "  o   o   o   o    o   o   o   o";

         read_hi         = "  0   0   1   0    1   0   0   0",
                           "  0   0   0   0    0 a10  a9  a8",
                           " a7  a6  a5  a4   a3  a2  a1  a0",
                           "  o   o   o   o    o   o   o   o";

         loadpage_lo     = "  0   1   0   0    0   0   0   0",
                           "  0   0   0   x    x   x   x   x",
                           "  x   x   x  a4   a3  a2  a1  a0",
                           "  i   i   i   i    i   i   i   i";

         loadpage_hi     = "  0   1   0   0    1   0   0   0",
                           "  0   0   0   x    x   x   x   x",
                           "  x   x   x  a4   a3  a2  a1  a0",
                           "  i   i   i   i    i   i   i   i";

         writepage       = "  0  1  0  0   1   1  0  0",
                           "  0  0  0  0   0 a10 a9 a8",
                           " a7 a6 a5  x   x   x  x  x",
                           "  x  x  x  x   x   x  x  x";

	mode		= 0x41;
	delay		= 6;
	blocksize	= 32;
	readsize	= 256;
       ;
#   ATtiny4313 has Signature Bytes: 0x1E 0x92 0x0D.
     memory "signature"
         size            = 3;
         read            = "0  0  1  1   0  0  0  0   0  0  0  x   x  x  x  x",
                           "x  x  x  x   x  x a1 a0   o  o  o  o   o  o  o  o";
       ;
     memory "lock"
         size            = 1;
         write           = "1 0 1 0  1 1 0 0  1 1 1 x  x x x x",
                           "x x x x  x x x x  1 1 i i  i i i i";
         read           = "0 1 0 1  1 0 0 0  0 0 0 0  0 0 0 0",
                          "x x x x  x x x x  x x o o  o o o o";
        min_write_delay = 9000;
        max_write_delay = 9000;
       ;

     memory "lfuse"
         size            = 1;
         write           = "1 0 1 0  1 1 0 0  1 0 1 0  0 0 0 0",
                           "x x x x  x x x x  i i i i  i i i i";

         read            = "0 1 0 1  0 0 0 0  0 0 0 0  0 0 0 0",
                           "x x x x  x x x x  o o o o  o o o o";
        min_write_delay = 9000;
        max_write_delay = 9000;
       ;

     memory "hfuse"
         size            = 1;
         write           = "1 0 1 0  1 1 0 0  1 0 1 0  1 0 0 0",
                           "x x x x  x x x x  i i i i  i i i i";

         read            = "0 1 0 1  1 0 0 0  0 0 0 0  1 0 0 0",
                           "x x x x  x x x x  o o o o  o o o o";
        min_write_delay = 9000;
        max_write_delay = 9000;
       ;

     memory "efuse"
         size            = 1;
         write           = "1 0 1 0  1 1 0 0  1 0 1 0  0 1 0 0",
                           "x x x x  x x x x  x x x x  x x x i";

         read            = "0 1 0 1  0 0 0 0  0 0 0 0  1 0 0 0",
                           "x x x x  x x x x  o o o o  o o o o";
        min_write_delay = 9000;
        max_write_delay = 9000;
     ;

     memory "calibration"
         size            = 2;
         read            = "0  0  1  1   1  0  0  0    0 0 0 x  x x x x",
                           "0  0  0  0   0  0  0  a0   o o o o  o o o o";
     ;
  ;

#------------------------------------------------------------
# AT90PWM2
#------------------------------------------------------------

part
     id            = "pwm2";
     desc          = "AT90PWM2";
     has_debugwire = yes;
     flash_instr   = 0xB6, 0x01, 0x11;
     eeprom_instr  = 0xBD, 0xF2, 0xBD, 0xE1, 0xBB, 0xCF, 0xB4, 0x00,
	             0xBE, 0x01, 0xB6, 0x01, 0xBC, 0x00, 0xBB, 0xBF,
	             0x99, 0xF9, 0xBB, 0xAF;
     stk500_devcode   = 0x65;
##  avr910_devcode   = ?;
     signature        = 0x1e 0x93 0x81;
     pagel            = 0xD8;
     bs2              = 0xE2;
     reset            = io;
     chip_erase_delay = 9000;

     pgm_enable       = "1 0 1 0  1 1 0 0    0 1 0 1  0 0 1 1",
                        "x x x x  x x x x    x x x x  x x x x";

     chip_erase       = "1 0 1 0  1 1 0 0    1 0 0 x  x x x x",
                        "x x x x  x x x x    x x x x  x x x x";

    timeout		= 200;
    stabdelay		= 100;
    cmdexedelay		= 25;
    synchloops		= 32;
    bytedelay		= 0;
    pollindex		= 3;
    pollvalue		= 0x53;
    predelay		= 1;
    postdelay		= 1;
    pollmethod		= 1;

    pp_controlstack     =
        0x0E, 0x1E, 0x0F, 0x1F, 0x2E, 0x3E, 0x2F, 0x3F,
        0x4E, 0x5E, 0x4F, 0x5F, 0x6E, 0x7E, 0x6F, 0x7F,
        0x66, 0x76, 0x67, 0x77, 0x6A, 0x7A, 0x6B, 0x7B,
        0xBE, 0xFD, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00;
    hventerstabdelay    = 100;
    progmodedelay       = 0;
    latchcycles         = 5;
    togglevtg           = 1;
    poweroffdelay       = 15;
    resetdelayms        = 1;
    resetdelayus        = 0;
    hvleavestabdelay    = 15;
    chiperasepulsewidth = 0;
    chiperasepolltimeout = 10;
    programfusepulsewidth = 0;
    programfusepolltimeout = 5;
    programlockpulsewidth = 0;
    programlockpolltimeout = 5;

     memory "eeprom"
         size            = 512;
        paged           = no;
        page_size       = 4;
         min_write_delay = 4000;
         max_write_delay = 4500;
         readback_p1     = 0xff;
         readback_p2     = 0xff;
         read            = "1  0  1  0   0  0  0  0   0 0 0 x  x x x a8",
                           "a7 a6 a5 a4  a3 a2 a1 a0  o o o o  o o o o";

         write           = "1  1  0  0   0  0  0  0   0 0 0 x  x x x a8",
                           "a7 a6 a5 a4  a3 a2 a1 a0  i i i i  i i i i";

	loadpage_lo	= "  1   1   0   0      0   0   0   1",
			  "  0   0   0   0      0   0   0   0",
			  "  0   0   0   0      0   0  a1  a0",
			  "  i   i   i   i      i   i   i   i";

	writepage	= "  1   1   0   0      0   0   1   0",
			  "  0   0   x   x      x   x   x   x",
			  " a7  a6  a5  a4     a3  a2   0   0",
			  "  x   x   x   x      x   x   x   x";

	mode		= 0x41;
	delay		= 6;
	blocksize	= 4;
	readsize	= 256;
       ;
     memory "flash"
         paged           = yes;
         size            = 8192;
         page_size       = 64;
         num_pages       = 128;
         min_write_delay = 4500;
         max_write_delay = 4500;
         readback_p1     = 0xff;
         readback_p2     = 0xff;
         read_lo         = "  0   0   1   0    0   0   0   0",
                           "  0   0   0   0   a11 a10 a9  a8",
                           " a7  a6  a5  a4   a3  a2  a1  a0",
                           "  o   o   o   o    o   o   o   o";

         read_hi         = "  0   0   1   0    1   0   0   0",
                           "  0   0   0   0   a11 a10 a9  a8",
                           " a7  a6  a5  a4   a3  a2  a1  a0",
                           "  o   o   o   o    o   o   o   o";

         loadpage_lo     = "  0   1   0   0    0   0   0   0",
                           "  0   0   0   x    x   x   x   x",
                           "  x   x   x  a4   a3  a2  a1  a0",
                           "  i   i   i   i    i   i   i   i";

         loadpage_hi     = "  0   1   0   0    1   0   0   0",
                           "  0   0   0   x    x   x   x   x",
                           "  x   x   x  a4   a3  a2  a1  a0",
                           "  i   i   i   i    i   i   i   i";

         writepage       = "  0  1  0  0   1   1   0   0",
                           "  0  0  0  0   a11 a10 a9  a8",
                           " a7 a6 a5  x   x   x   x   x",
                           "  x  x  x  x   x   x   x   x";

	mode		= 0x41;
	delay		= 6;
	blocksize	= 64;
	readsize	= 256;
       ;
#   AT90PWM2 has Signature Bytes: 0x1E 0x93 0x81.
     memory "signature"
         size            = 3;
         read            = "0  0  1  1   0  0  0  0   0  0  x  x   x  x  x  x",
                           "x  x  x  x   x  x a1 a0   o  o  o  o   o  o  o  o";
       ;
     memory "lock"
         size            = 1;
         write           = "1 0 1 0  1 1 0 0  1 1 1 x  x x x x",
                           "x x x x  x x x x  1 1 i i  i i i i";

         read            = "0 1 0 1  1 0 0 0  0 0 0 0  0 0 0 0",
                           "x x x x  x x x x  x x o o  o o o o";
        min_write_delay = 9000;
        max_write_delay = 9000;
       ;

     memory "lfuse"
         size            = 1;
         write           = "1 0 1 0  1 1 0 0  1 0 1 0  0 0 0 0",
                           "x x x x  x x x x  i i i i  i i i i";

         read            = "0 1 0 1  0 0 0 0  0 0 0 0  0 0 0 0",
                           "x x x x  x x x x  o o o o  o o o o";
        min_write_delay = 9000;
        max_write_delay = 9000;
       ;

     memory "hfuse"
         size            = 1;
         write           = "1 0 1 0  1 1 0 0  1 0 1 0  1 0 0 0",
                           "x x x x  x x x x  i i i i  i i i i";

         read            = "0 1 0 1  1 0 0 0  0 0 0 0  1 0 0 0",
                           "x x x x  x x x x  o o o o  o o o o";
        min_write_delay = 9000;
        max_write_delay = 9000;
       ;

     memory "efuse"
         size            = 1;
         write           = "1 0 1 0  1 1 0 0  1 0 1 0  0 1 0 0",
                           "x x x x  x x x x  i i i i  i i i i";

         read            = "0 1 0 1  0 0 0 0  0 0 0 0  1 0 0 0",
                           "x x x x  x x x x  o o o o  o o o o";
        min_write_delay = 9000;
        max_write_delay = 9000;
     ;

     memory "calibration"
         size            = 1;
         read            = "0  0  1  1   1  0  0  0    0 0 0 x  x x x x",
                           "0  0  0  0   0  0  0  0    o o o o  o o o o";
     ;
  ;

#------------------------------------------------------------
# AT90PWM3
#------------------------------------------------------------

# Completely identical to AT90PWM2 (including the signature!)

part parent "pwm2"
     id            = "pwm3";
     desc          = "AT90PWM3";
  ;

#------------------------------------------------------------
# AT90PWM2B
#------------------------------------------------------------
# Same as AT90PWM2 but different signature.

part parent "pwm2"
     id            = "pwm2b";
     desc          = "AT90PWM2B";
     signature     = 0x1e 0x93 0x83;

    ocdrev              = 1;
  ;

#------------------------------------------------------------
# AT90PWM3B
#------------------------------------------------------------

# Completely identical to AT90PWM2B (including the signature!)

part parent "pwm2b"
     id            = "pwm3b";
     desc          = "AT90PWM3B";

    ocdrev              = 1;
  ;

#------------------------------------------------------------
# AT90PWM316
#------------------------------------------------------------

# Similar to AT90PWM3B, but with 16 kiB flash, 512 B EEPROM, and 1024 B SRAM.

part parent "pwm3b"
     id            = "pwm316";
     desc          = "AT90PWM316";
     signature     = 0x1e 0x94 0x83;

    ocdrev              = 1;

    memory "flash"
        paged           = yes;
        size            = 16384;
        page_size       = 128;
        num_pages       = 128;
        min_write_delay = 4500;
        max_write_delay = 4500;
        readback_p1     = 0xff;
        readback_p2     = 0xff;
        read_lo         = "  0   0   1   0      0   0   0   0",
                          "  0   0 a13 a12    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

        read_hi         = "  0   0   1   0      1   0   0   0",
                          "  0   0 a13 a12    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

        loadpage_lo     = "  0   1   0   0      0   0   0   0",
                          "  0   0   x   x      x   x   x   x",
                          "  x   x  a5  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

        loadpage_hi     = "  0   1   0   0      1   0   0   0",
                          "  0   0   x   x      x   x   x   x",
                          "  x   x  a5  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

        writepage       = "  0   1   0   0      1   1   0   0",
                          "  0   0 a13 a12    a11 a10  a9  a8",
                          " a7  a6   x   x      x   x   x   x",
                          "  x   x   x   x      x   x   x   x";

	mode		= 0x21;
	delay		= 6;
	blocksize	= 128;
	readsize	= 256;
      ;
  ;

#------------------------------------------------------------
# AT90PWM216
#------------------------------------------------------------
# Completely identical to AT90PWM316 (including the signature!)

part parent "pwm316"
     id = "pwm216";
     desc = "AT90PWM216";
  ; 

#------------------------------------------------------------
# ATtiny25
#------------------------------------------------------------

part
     id            = "t25";
     desc          = "ATtiny25";
     has_debugwire = yes;
     flash_instr   = 0xB4, 0x02, 0x12;
     eeprom_instr  = 0xBB, 0xFF, 0xBB, 0xEE, 0xBB, 0xCC, 0xB2, 0x0D,
	             0xBC, 0x02, 0xB4, 0x02, 0xBA, 0x0D, 0xBB, 0xBC,
	             0x99, 0xE1, 0xBB, 0xAC;
## no STK500 devcode in XML file, use the ATtiny45 one
     stk500_devcode   = 0x14;
##  avr910_devcode   = ?;
##  Try the AT90S2313 devcode:
     avr910_devcode   = 0x20;
     signature        = 0x1e 0x91 0x08;
     reset            = io;
     chip_erase_delay = 4500;

     pgm_enable       = "1 0 1 0  1 1 0 0    0 1 0 1  0 0 1 1",
                        "x x x x  x x x x    x x x x  x x x x";

     chip_erase       = "1 0 1 0  1 1 0 0    1 0 0 x  x x x x",
                        "x x x x  x x x x    x x x x  x x x x";

    timeout		= 200;
    stabdelay		= 100;
    cmdexedelay		= 25;
    synchloops		= 32;
    bytedelay		= 0;
    pollindex		= 3;
    pollvalue		= 0x53;
    predelay		= 1;
    postdelay		= 1;
    pollmethod		= 1;

    hvsp_controlstack   =
        0x4C, 0x0C, 0x1C, 0x2C, 0x3C, 0x64, 0x74, 0x66,
        0x68, 0x78, 0x68, 0x68, 0x7A, 0x6A, 0x68, 0x78,
        0x78, 0x7D, 0x6D, 0x0C, 0x80, 0x40, 0x20, 0x10,
        0x11, 0x08, 0x04, 0x02, 0x03, 0x08, 0x04, 0x00;
    hventerstabdelay    = 100;
    hvspcmdexedelay     = 0;
    synchcycles         = 6;
    latchcycles         = 1;
    togglevtg           = 1;
    poweroffdelay       = 25;
    resetdelayms        = 1;
    resetdelayus        = 0;
    hvleavestabdelay    = 100;
    resetdelay          = 25;
    chiperasepolltimeout = 40;
    chiperasetime       = 0;
    programfusepolltimeout = 25;
    programlockpolltimeout = 25;

    ocdrev              = 1;

     memory "eeprom"
         size            = 128;
        paged           = no;
        page_size       = 4;
         min_write_delay = 4000;
         max_write_delay = 4500;
         readback_p1     = 0xff;
         readback_p2     = 0xff;
         read            = "1  0  1  0   0  0  0  0   0 0 0 x  x x x x",
                           "x a6 a5 a4  a3 a2 a1 a0   o o o o  o o o o";

         write           = "1  1  0  0   0  0  0  0   0 0 0 x  x x x x",
                           "x a6 a5 a4  a3 a2 a1 a0   i i i i  i i i i";

	loadpage_lo	= "  1   1   0   0      0   0   0   1",
			  "  0   0   0   0      0   0   0   0",
			  "  0   0   0   0      0   0  a1  a0",
			  "  i   i   i   i      i   i   i   i";

	writepage	= "  1   1   0   0      0   0   1   0",
			  "  0   0   x   x      x   x   x   x",
			  "  x  a6  a5  a4     a3  a2   0   0",
			  "  x   x   x   x      x   x   x   x";

	mode		= 0x41;
	delay		= 6;
	blocksize	= 4;
	readsize	= 256;
       ;
     memory "flash"
         paged           = yes;
         size            = 2048;
         page_size       = 32;
         num_pages       = 64;
         min_write_delay = 4500;
         max_write_delay = 4500;
         readback_p1     = 0xff;
         readback_p2     = 0xff;
         read_lo         = "  0   0   1   0    0   0   0   0",
                           "  0   0   0   0    0   0  a9  a8",
                           " a7  a6  a5  a4   a3  a2  a1  a0",
                           "  o   o   o   o    o   o   o   o";

         read_hi         = "  0   0   1   0    1   0   0   0",
                           "  0   0   0   0    0   0  a9  a8",
                           " a7  a6  a5  a4   a3  a2  a1  a0",
                           "  o   o   o   o    o   o   o   o";

         loadpage_lo     = "  0   1   0   0    0   0   0   0",
                           "  0   0   0   x    x   x   x   x",
                           "  x   x   x   x   a3  a2  a1  a0",
                           "  i   i   i   i    i   i   i   i";

         loadpage_hi     = "  0   1   0   0    1   0   0   0",
                           "  0   0   0   x    x   x   x   x",
                           "  x   x   x   x   a3  a2  a1  a0",
                           "  i   i   i   i    i   i   i   i";

         writepage       = "  0  1  0  0   1  1  0  0",
                           "  0  0  0  0   0  0 a9 a8",
                           " a7 a6 a5 a4   x  x  x  x",
                           "  x  x  x  x   x  x  x  x";

	mode		= 0x41;
	delay		= 6;
	blocksize	= 32;
	readsize	= 256;
       ;
#   ATtiny25 has Signature Bytes: 0x1E 0x91 0x08.
     memory "signature"
         size            = 3;
         read            = "0  0  1  1   0  0  0  0   0  0  0  x   x  x  x  x",
                           "x  x  x  x   x  x a1 a0   o  o  o  o   o  o  o  o";
       ;
     memory "lock"
         size            = 1;
         write           = "1 0 1 0  1 1 0 0  1 1 1 x  x x x x",
                           "x x x x  x x x x  1 1 i i  i i i i";
         read            = "0 1 0 1  1 0 0 0  0 0 0 0  0 0 0 0",
                           "0 0 0 0  0 0 0 0  o o o o  o o o o";
        min_write_delay = 9000;
        max_write_delay = 9000;
       ;

     memory "lfuse"
         size            = 1;
         write           = "1 0 1 0  1 1 0 0  1 0 1 0  0 0 0 0",
                           "x x x x  x x x x  i i i i  i i i i";

         read            = "0 1 0 1  0 0 0 0  0 0 0 0  0 0 0 0",
                           "x x x x  x x x x  o o o o  o o o o";
        min_write_delay = 9000;
        max_write_delay = 9000;
       ;

     memory "hfuse"
         size            = 1;
         write           = "1 0 1 0  1 1 0 0  1 0 1 0  1 0 0 0",
                           "x x x x  x x x x  i i i i  i i i i";

         read            = "0 1 0 1  1 0 0 0  0 0 0 0  1 0 0 0",
                           "x x x x  x x x x  o o o o  o o o o";
        min_write_delay = 9000;
        max_write_delay = 9000;
       ;

     memory "efuse"
         size            = 1;
         write           = "1 0 1 0  1 1 0 0  1 0 1 0  0 1 0 0",
                           "x x x x  x x x x  x x x x  x x x i";

         read            = "0 1 0 1  0 0 0 0  0 0 0 0  1 0 0 0",
                           "x x x x  x x x x  o o o o  o o o o";
        min_write_delay = 9000;
        max_write_delay = 9000;
     ;

     memory "calibration"
         size            = 1;
         read            = "0  0  1  1   1  0  0  0    0 0 0 x  x x x x",
                           "0  0  0  0   0  0  0  a0   o o o o  o o o o";
     ;
  ;

#------------------------------------------------------------
# ATtiny45
#------------------------------------------------------------

part
     id            = "t45";
     desc          = "ATtiny45";
     has_debugwire = yes;
     flash_instr   = 0xB4, 0x02, 0x12;
     eeprom_instr  = 0xBB, 0xFF, 0xBB, 0xEE, 0xBB, 0xCC, 0xB2, 0x0D,
	             0xBC, 0x02, 0xB4, 0x02, 0xBA, 0x0D, 0xBB, 0xBC,
	             0x99, 0xE1, 0xBB, 0xAC;
     stk500_devcode   = 0x14;
##  avr910_devcode   = ?;
##  Try the AT90S2313 devcode:
     avr910_devcode   = 0x20;
     signature        = 0x1e 0x92 0x06;
     reset            = io;
     chip_erase_delay = 4500;

     pgm_enable       = "1 0 1 0  1 1 0 0    0 1 0 1  0 0 1 1",
                        "x x x x  x x x x    x x x x  x x x x";

     chip_erase       = "1 0 1 0  1 1 0 0    1 0 0 x  x x x x",
                        "x x x x  x x x x    x x x x  x x x x";

    timeout		= 200;
    stabdelay		= 100;
    cmdexedelay		= 25;
    synchloops		= 32;
    bytedelay		= 0;
    pollindex		= 3;
    pollvalue		= 0x53;
    predelay		= 1;
    postdelay		= 1;
    pollmethod		= 1;

    hvsp_controlstack     =
	0x4C, 0x0C, 0x1C, 0x2C, 0x3C, 0x64, 0x74, 0x66,
        0x68, 0x78, 0x68, 0x68, 0x7A, 0x6A, 0x68, 0x78,
        0x78, 0x7D, 0x6D, 0x0C, 0x80, 0x40, 0x20, 0x10,
        0x11, 0x08, 0x04, 0x02, 0x03, 0x08, 0x04, 0x00;
    hventerstabdelay    = 100;
    progmodedelay       = 0;
    hvspcmdexedelay     = 0;
    synchcycles         = 6;
    latchcycles         = 1;
    togglevtg           = 1;
    poweroffdelay       = 25;
    resetdelayms        = 1;
    resetdelayus        = 0;
    hvleavestabdelay    = 100;
    resetdelay          = 25;
    chiperasepolltimeout = 40;
    chiperasetime       = 0;
    programfusepolltimeout = 25;
    programlockpolltimeout = 25;

    ocdrev              = 1;

     memory "eeprom"
         size            = 256;
         page_size       = 4;
         min_write_delay = 4000;
         max_write_delay = 4500;
         readback_p1     = 0xff;
         readback_p2     = 0xff;
         read            = "1  0  1  0   0  0  0  0    0 0 0 x  x x x x",
                           "a7 a6 a5 a4  a3 a2 a1 a0   o o o o  o o o o";

         write           = "1  1  0  0   0  0  0  0    0 0 0 x  x x x x",
                           "a7 a6 a5 a4  a3 a2 a1 a0   i i i i  i i i i";

	loadpage_lo	= "  1   1   0   0      0   0   0   1",
			  "  0   0   0   0      0   0   0   0",
			  "  0   0   0   0      0   0  a1  a0",
			  "  i   i   i   i      i   i   i   i";

	writepage	= "  1   1   0   0      0   0   1   0",
			  "  0   0   x   x      x   x   x   x",
			  " a7  a6  a5  a4     a3  a2   0   0",
			  "  x   x   x   x      x   x   x   x";

	mode		= 0x41;
	delay		= 6;
	blocksize	= 4;
	readsize	= 256;
       ;
     memory "flash"
         paged           = yes;
         size            = 4096;
         page_size       = 64;
         num_pages       = 64;
         min_write_delay = 4500;
         max_write_delay = 4500;
         readback_p1     = 0xff;
         readback_p2     = 0xff;
         read_lo         = "  0   0   1   0    0   0   0   0",
                           "  0   0   0   0    0  a10 a9  a8",
                           " a7  a6  a5  a4   a3  a2  a1  a0",
                           "  o   o   o   o    o   o   o   o";

         read_hi         = "  0   0   1   0    1   0   0   0",
                           "  0   0   0   0    0  a10 a9  a8",
                           " a7  a6  a5  a4   a3  a2  a1  a0",
                           "  o   o   o   o    o   o   o   o";

         loadpage_lo     = "  0   1   0   0    0   0   0   0",
                           "  0   0   0   x    x   x   x   x",
                           "  x   x   x  a4   a3  a2  a1  a0",
                           "  i   i   i   i    i   i   i   i";

         loadpage_hi     = "  0   1   0   0    1   0   0   0",
                           "  0   0   0   x    x   x   x   x",
                           "  x   x   x  a4   a3  a2  a1  a0",
                           "  i   i   i   i    i   i   i   i";

         writepage       = "  0  1  0  0   1  1  0  0",
                           "  0  0  0  0   0 a10 a9 a8",
                           " a7 a6 a5  x   x  x  x  x",
                           "  x  x  x  x   x  x  x  x";

	mode		= 0x41;
	delay		= 6;
	blocksize	= 32;
	readsize	= 256;
       ;
#   ATtiny45 has Signature Bytes: 0x1E 0x92 0x08. (Data sheet 2586C-AVR-06/05 (doc2586.pdf) indicates otherwise!)
     memory "signature"
         size            = 3;
         read            = "0  0  1  1   0  0  0  0   0  0  0  x   x  x  x  x",
                           "x  x  x  x   x  x a1 a0   o  o  o  o   o  o  o  o";
       ;
     memory "lock"
         size            = 1;
         write           = "1 0 1 0  1 1 0 0  1 1 1 x  x x x x",
                           "x x x x  x x x x  1 1 i i  i i i i";
         read            = "0 1 0 1  1 0 0 0  0 0 0 0  0 0 0 0",
                           "0 0 0 0  0 0 0 0  o o o o  o o o o";
        min_write_delay = 9000;
        max_write_delay = 9000;
       ;

     memory "lfuse"
         size            = 1;
         write           = "1 0 1 0  1 1 0 0  1 0 1 0  0 0 0 0",
                           "x x x x  x x x x  i i i i  i i i i";

         read            = "0 1 0 1  0 0 0 0  0 0 0 0  0 0 0 0",
                           "x x x x  x x x x  o o o o  o o o o";
        min_write_delay = 9000;
        max_write_delay = 9000;
       ;

     memory "hfuse"
         size            = 1;
         write           = "1 0 1 0  1 1 0 0  1 0 1 0  1 0 0 0",
                           "x x x x  x x x x  i i i i  i i i i";

         read            = "0 1 0 1  1 0 0 0  0 0 0 0  1 0 0 0",
                           "x x x x  x x x x  o o o o  o o o o";
        min_write_delay = 9000;
        max_write_delay = 9000;
       ;

     memory "efuse"
         size            = 1;
         write           = "1 0 1 0  1 1 0 0  1 0 1 0  0 1 0 0",
                           "x x x x  x x x x  x x x x  x x x i";

         read            = "0 1 0 1  0 0 0 0  0 0 0 0  1 0 0 0",
                           "x x x x  x x x x  o o o o  o o o o";
        min_write_delay = 9000;
        max_write_delay = 9000;
     ;

     memory "calibration"
         size            = 1;
         read            = "0  0  1  1   1  0  0  0    0 0 0 x  x x x x",
                           "0  0  0  0   0  0  0  a0   o o o o  o o o o";
     ;
  ;

#------------------------------------------------------------
# ATtiny85
#------------------------------------------------------------

part
     id            = "t85";
     desc          = "ATtiny85";
     has_debugwire = yes;
     flash_instr   = 0xB4, 0x02, 0x12;
     eeprom_instr  = 0xBB, 0xFF, 0xBB, 0xEE, 0xBB, 0xCC, 0xB2, 0x0D,
	             0xBC, 0x02, 0xB4, 0x02, 0xBA, 0x0D, 0xBB, 0xBC,
	             0x99, 0xE1, 0xBB, 0xAC;
## no STK500 devcode in XML file, use the ATtiny45 one
     stk500_devcode   = 0x14;
##  avr910_devcode   = ?;
##  Try the AT90S2313 devcode:
     avr910_devcode   = 0x20;
     signature        = 0x1e 0x93 0x0b;
     reset            = io;
     chip_erase_delay = 4500;

     pgm_enable       = "1 0 1 0  1 1 0 0    0 1 0 1  0 0 1 1",
                        "x x x x  x x x x    x x x x  x x x x";

     chip_erase       = "1 0 1 0  1 1 0 0    1 0 0 x  x x x x",
                        "x x x x  x x x x    x x x x  x x x x";

    timeout		= 200;
    stabdelay		= 100;
    cmdexedelay		= 25;
    synchloops		= 32;
    bytedelay		= 0;
    pollindex		= 3;
    pollvalue		= 0x53;
    predelay		= 1;
    postdelay		= 1;
    pollmethod		= 1;

    hvsp_controlstack   =
        0x4C, 0x0C, 0x1C, 0x2C, 0x3C, 0x64, 0x74, 0x66,
        0x68, 0x78, 0x68, 0x68, 0x7A, 0x6A, 0x68, 0x78,
        0x78, 0x7D, 0x6D, 0x0C, 0x80, 0x40, 0x20, 0x10,
        0x11, 0x08, 0x04, 0x02, 0x03, 0x08, 0x04, 0x00;
    hventerstabdelay    = 100;
    hvspcmdexedelay     = 0;
    synchcycles         = 6;
    latchcycles         = 1;
    togglevtg           = 1;
    poweroffdelay       = 25;
    resetdelayms        = 1;
    resetdelayus        = 0;
    hvleavestabdelay    = 100;
    resetdelay          = 25;
    chiperasepolltimeout = 40;
    chiperasetime       = 0;
    programfusepolltimeout = 25;
    programlockpolltimeout = 25;

    ocdrev              = 1;

     memory "eeprom"
         size            = 512;
        paged           = no;
        page_size       = 4;
         min_write_delay = 4000;
         max_write_delay = 4500;
         readback_p1     = 0xff;
         readback_p2     = 0xff;
         read            = "1  0  1  0   0  0  0  0    0 0 0 x  x x x a8",
                           "a7 a6 a5 a4  a3 a2 a1 a0   o o o o  o o o o";

         write           = "1  1  0  0   0  0  0  0    0 0 0 x  x x x a8",
                           "a7 a6 a5 a4  a3 a2 a1 a0   i i i i  i i i i";

	loadpage_lo	= "  1   1   0   0      0   0   0   1",
			  "  0   0   0   0      0   0   0   0",
			  "  0   0   0   0      0   0  a1  a0",
			  "  i   i   i   i      i   i   i   i";

	writepage	= "  1   1   0   0      0   0   1   0",
			  "  0   0   x   x      x   x   x  a8",
			  " a7  a6  a5  a4     a3  a2   0   0",
			  "  x   x   x   x      x   x   x   x";

	mode		= 0x41;
	delay		= 6;
	blocksize	= 4;
	readsize	= 256;
       ;
     memory "flash"
         paged           = yes;
         size            = 8192;
         page_size       = 64;
         num_pages       = 128;
         min_write_delay = 4500;
         max_write_delay = 4500;
         readback_p1     = 0xff;
         readback_p2     = 0xff;
         read_lo         = "  0   0   1   0    0   0   0   0",
                           "  0   0   0   0  a11 a10  a9  a8",
                           " a7  a6  a5  a4   a3  a2  a1  a0",
                           "  o   o   o   o    o   o   o   o";

         read_hi         = "  0   0   1   0    1   0   0   0",
                           "  0   0   0   0  a11 a10  a9  a8",
                           " a7  a6  a5  a4   a3  a2  a1  a0",
                           "  o   o   o   o    o   o   o   o";

         loadpage_lo     = "  0   1   0   0    0   0   0   0",
                           "  0   0   0   x    x   x   x   x",
                           "  x   x   x  a4   a3  a2  a1  a0",
                           "  i   i   i   i    i   i   i   i";

         loadpage_hi     = "  0   1   0   0    1   0   0   0",
                           "  0   0   0   x    x   x   x   x",
                           "  x   x   x  a4   a3  a2  a1  a0",
                           "  i   i   i   i    i   i   i   i";

         writepage       = "  0  1  0  0   1   1   0  0",
                           "  0  0  0  0  a11 a10 a9 a8",
                           " a7 a6 a5  x   x  x  x  x",
                           "  x  x  x  x   x  x  x  x";

	mode		= 0x41;
	delay		= 6;
	blocksize	= 32;
	readsize	= 256;
       ;
#   ATtiny85 has Signature Bytes: 0x1E 0x93 0x08.
     memory "signature"
         size            = 3;
         read            = "0  0  1  1   0  0  0  0   0  0  0  x   x  x  x  x",
                           "x  x  x  x   x  x a1 a0   o  o  o  o   o  o  o  o";
       ;
     memory "lock"
         size            = 1;
         write           = "1 0 1 0  1 1 0 0  1 1 1 x  x x x x",
                           "x x x x  x x x x  1 1 i i  i i i i";
         read            = "0 1 0 1  1 0 0 0  0 0 0 0  0 0 0 0",
                           "0 0 0 0  0 0 0 0  o o o o  o o o o";
        min_write_delay = 9000;
        max_write_delay = 9000;
       ;

     memory "lfuse"
         size            = 1;
         write           = "1 0 1 0  1 1 0 0  1 0 1 0  0 0 0 0",
                           "x x x x  x x x x  i i i i  i i i i";

         read            = "0 1 0 1  0 0 0 0  0 0 0 0  0 0 0 0",
                           "x x x x  x x x x  o o o o  o o o o";
        min_write_delay = 9000;
        max_write_delay = 9000;
       ;

     memory "hfuse"
         size            = 1;
         write           = "1 0 1 0  1 1 0 0  1 0 1 0  1 0 0 0",
                           "x x x x  x x x x  i i i i  i i i i";

         read            = "0 1 0 1  1 0 0 0  0 0 0 0  1 0 0 0",
                           "x x x x  x x x x  o o o o  o o o o";
        min_write_delay = 9000;
        max_write_delay = 9000;
       ;

     memory "efuse"
         size            = 1;
         write           = "1 0 1 0  1 1 0 0  1 0 1 0  0 1 0 0",
                           "x x x x  x x x x  x x x x  x x x i";

         read            = "0 1 0 1  0 0 0 0  0 0 0 0  1 0 0 0",
                           "x x x x  x x x x  o o o o  o o o o";
        min_write_delay = 9000;
        max_write_delay = 9000;
     ;

     memory "calibration"
         size            = 1;
         read            = "0  0  1  1   1  0  0  0    0 0 0 x  x x x x",
                           "0  0  0  0   0  0  0  a0   o o o o  o o o o";
     ;
  ;

#------------------------------------------------------------
# ATmega640
#------------------------------------------------------------
# Almost same as ATmega1280, except for different memory sizes

part
    id               = "m640";
    desc             = "ATmega640";
    signature        = 0x1e 0x96 0x08;
    has_jtag         = yes;
#    stk500_devcode   = 0xB2;
#    avr910_devcode   = 0x43;
    chip_erase_delay = 9000;
    pagel            = 0xD7;
    bs2              = 0xA0;
    reset            = dedicated;
    pgm_enable       = "1 0 1 0  1 1 0 0    0 1 0 1  0 0 1 1",
                       "x x x x  x x x x    x x x x  x x x x";

    chip_erase       = "1 0 1 0  1 1 0 0    1 0 0 0  0 0 0 0",
                       "x x x x  x x x x    x x x x  x x x x";

    timeout		= 200;
    stabdelay		= 100;
    cmdexedelay		= 25;
    synchloops		= 32;
    bytedelay		= 0;
    pollindex		= 3;
    pollvalue		= 0x53;
    predelay		= 1;
    postdelay		= 1;
    pollmethod		= 1;

    pp_controlstack     =
        0x0E, 0x1E, 0x0F, 0x1F, 0x2E, 0x3E, 0x2F, 0x3F,
        0x4E, 0x5E, 0x4F, 0x5F, 0x6E, 0x7E, 0x6F, 0x7F,
        0x66, 0x76, 0x67, 0x77, 0x6A, 0x7A, 0x6B, 0x7B,
        0xBE, 0xFD, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00;
    hventerstabdelay    = 100;
    progmodedelay       = 0;
    latchcycles         = 5;
    togglevtg           = 1;
    poweroffdelay       = 15;
    resetdelayms        = 1;
    resetdelayus        = 0;
    hvleavestabdelay    = 15;
    chiperasepulsewidth = 0;
    chiperasepolltimeout = 10;
    programfusepulsewidth = 0;
    programfusepolltimeout = 5;
    programlockpulsewidth = 0;
    programlockpolltimeout = 5;

    idr                 = 0x31;
    spmcr               = 0x57;
    rampz               = 0x3b;
    allowfullpagebitstream = no;

    ocdrev              = 3;

    memory "eeprom"
        paged           = no; /* leave this "no" */
        page_size       = 8;  /* for parallel programming */
        size            = 4096;
        min_write_delay = 9000;
        max_write_delay = 9000;
        readback_p1     = 0x00;
        readback_p2     = 0x00;
        read            = "  1   0   1   0      0   0   0   0",
                          "  x   x   x   x    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

        write           = "  1   1   0   0      0   0   0   0",
                          "  x   x   x   x    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0", 
                          "  i   i   i   i      i   i   i   i";

	loadpage_lo	= "  1   1   0   0      0   0   0   1",
			  "  0   0   0   0      0   0   0   0",
			  "  0   0   0   0      0  a2  a1  a0",
			  "  i   i   i   i      i   i   i   i";

	writepage	= "  1   1   0   0      0   0   1   0",
			  "  0   0   x   x    a11 a10  a9  a8",
			  " a7  a6  a5  a4     a3   0   0   0",
			  "  x   x   x   x      x   x   x   x";

	mode		= 0x41;
	delay		= 10;
	blocksize	= 8;
	readsize	= 256;
      ;

    memory "flash"
        paged           = yes;
        size            = 65536;
        page_size       = 256;
        num_pages       = 256;
        min_write_delay = 4500;
        max_write_delay = 4500;
        readback_p1     = 0x00;
        readback_p2     = 0x00;
        read_lo         = "  0   0   1   0      0   0   0   0",
                          "  0 a14 a13 a12    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

        read_hi         = "  0   0   1   0      1   0   0   0",
                          "  0 a14 a13 a12    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

        loadpage_lo     = "  0   1   0   0      0   0   0   0",
                          "  x   x   x   x      x   x   x   x",
                          "  x  a6  a5  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

        loadpage_hi     = "  0   1   0   0      1   0   0   0",
                          "  x   x   x   x      x   x   x   x",
                          "  x  a6  a5  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

        writepage       = "  0   1   0   0      1   1   0   0",
                          "  0 a14 a13 a12    a11 a10  a9  a8",
                          " a7   x   x   x      x   x   x   x",
                          "  x   x   x   x      x   x   x   x";

	mode		= 0x41;
	delay		= 10;
	blocksize	= 256;
	readsize	= 256;
      ;

    memory "lfuse"
        size            = 1;
        write           = "1 0 1 0  1 1 0 0  1 0 1 0  0 0 0 0",
                          "x x x x  x x x x  i i i i  i i i i";

        read            = "0 1 0 1  0 0 0 0  0 0 0 0  0 0 0 0",
                          "x x x x  x x x x  o o o o  o o o o";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "hfuse"
        size            = 1;
        write           = "1 0 1 0  1 1 0 0  1 0 1 0  1 0 0 0",
                          "x x x x  x x x x  i i i i  i i i i";

        read            = "0 1 0 1  1 0 0 0  0 0 0 0  1 0 0 0",
                          "x x x x  x x x x  o o o o  o o o o";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "efuse"
        size            = 1;
        write           = "1 0 1 0  1 1 0 0  1 0 1 0  0 1 0 0",
                          "x x x x  x x x x  x x x x  x i i i";

        read            = "0 1 0 1  0 0 0 0  0 0 0 0  1 0 0 0",
                          "x x x x  x x x x  o o o o  o o o o";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "lock"
        size            = 1;
        read            = "0 1 0 1  1 0 0 0   0 0 0 0  0 0 0 0",
                          "x x x x  x x x x   x x o o  o o o o";

        write           = "1 0 1 0  1 1 0 0   1 1 1 x  x x x x",
                          "x x x x  x x x x   1 1 i i  i i i i";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "calibration"
        size            = 1;
        read            = "0 0 1 1  1 0 0 0    x x x x  x x x x",
                          "0 0 0 0  0 0 0 0    o o o o  o o o o";
      ;

    memory "signature"
        size            = 3;
        read            = "0  0  1  1   0  0  0  0   x  x  x  x   x  x  x  x",
                          "x  x  x  x   x  x a1 a0   o  o  o  o   o  o  o  o";
      ;
  ;

#------------------------------------------------------------
# ATmega1280
#------------------------------------------------------------

part
    id               = "m1280";
    desc             = "ATmega1280";
    signature        = 0x1e 0x97 0x03;
    has_jtag         = yes;
#    stk500_devcode   = 0xB2;
#    avr910_devcode   = 0x43;
    chip_erase_delay = 9000;
    pagel            = 0xD7;
    bs2              = 0xA0;
    reset            = dedicated;
    pgm_enable       = "1 0 1 0  1 1 0 0    0 1 0 1  0 0 1 1",
                       "x x x x  x x x x    x x x x  x x x x";

    chip_erase       = "1 0 1 0  1 1 0 0    1 0 0 0  0 0 0 0",
                       "x x x x  x x x x    x x x x  x x x x";

    timeout		= 200;
    stabdelay		= 100;
    cmdexedelay		= 25;
    synchloops		= 32;
    bytedelay		= 0;
    pollindex		= 3;
    pollvalue		= 0x53;
    predelay		= 1;
    postdelay		= 1;
    pollmethod		= 1;

    pp_controlstack     =
        0x0E, 0x1E, 0x0F, 0x1F, 0x2E, 0x3E, 0x2F, 0x3F,
        0x4E, 0x5E, 0x4F, 0x5F, 0x6E, 0x7E, 0x6F, 0x7F,
        0x66, 0x76, 0x67, 0x77, 0x6A, 0x7A, 0x6B, 0x7B,
        0xBE, 0xFD, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00;
    hventerstabdelay    = 100;
    progmodedelay       = 0;
    latchcycles         = 5;
    togglevtg           = 1;
    poweroffdelay       = 15;
    resetdelayms        = 1;
    resetdelayus        = 0;
    hvleavestabdelay    = 15;
    chiperasepulsewidth = 0;
    chiperasepolltimeout = 10;
    programfusepulsewidth = 0;
    programfusepolltimeout = 5;
    programlockpulsewidth = 0;
    programlockpolltimeout = 5;

    idr                 = 0x31;
    spmcr               = 0x57;
    rampz               = 0x3b;
    allowfullpagebitstream = no;

    ocdrev              = 3;

    memory "eeprom"
        paged           = no; /* leave this "no" */
        page_size       = 8;  /* for parallel programming */
        size            = 4096;
        min_write_delay = 9000;
        max_write_delay = 9000;
        readback_p1     = 0x00;
        readback_p2     = 0x00;
        read            = "  1   0   1   0      0   0   0   0",
                          "  x   x   x   x    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

        write           = "  1   1   0   0      0   0   0   0",
                          "  x   x   x   x    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0", 
                          "  i   i   i   i      i   i   i   i";

	loadpage_lo	= "  1   1   0   0      0   0   0   1",
			  "  0   0   0   0      0   0   0   0",
			  "  0   0   0   0      0  a2  a1  a0",
			  "  i   i   i   i      i   i   i   i";

	writepage	= "  1   1   0   0      0   0   1   0",
			  "  0   0   x   x    a11 a10  a9  a8",
			  " a7  a6  a5  a4     a3   0   0   0",
			  "  x   x   x   x      x   x   x   x";

	mode		= 0x41;
	delay		= 10;
	blocksize	= 8;
	readsize	= 256;
      ;

    memory "flash"
        paged           = yes;
        size            = 131072;
        page_size       = 256;
        num_pages       = 512;
        min_write_delay = 4500;
        max_write_delay = 4500;
        readback_p1     = 0x00;
        readback_p2     = 0x00;
        read_lo         = "  0   0   1   0      0   0   0   0",
                          "a15 a14 a13 a12    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

        read_hi         = "  0   0   1   0      1   0   0   0",
                          "a15 a14 a13 a12    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

        loadpage_lo     = "  0   1   0   0      0   0   0   0",
                          "  x   x   x   x      x   x   x   x",
                          "  x  a6  a5  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

        loadpage_hi     = "  0   1   0   0      1   0   0   0",
                          "  x   x   x   x      x   x   x   x",
                          "  x  a6  a5  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

        writepage       = "  0   1   0   0      1   1   0   0",
                          "a15 a14 a13 a12    a11 a10  a9  a8",
                          " a7   x   x   x      x   x   x   x",
                          "  x   x   x   x      x   x   x   x";

	mode		= 0x41;
	delay		= 10;
	blocksize	= 256;
	readsize	= 256;
      ;

    memory "lfuse"
        size            = 1;
        write           = "1 0 1 0  1 1 0 0  1 0 1 0  0 0 0 0",
                          "x x x x  x x x x  i i i i  i i i i";

        read            = "0 1 0 1  0 0 0 0  0 0 0 0  0 0 0 0",
                          "x x x x  x x x x  o o o o  o o o o";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "hfuse"
        size            = 1;
        write           = "1 0 1 0  1 1 0 0  1 0 1 0  1 0 0 0",
                          "x x x x  x x x x  i i i i  i i i i";

        read            = "0 1 0 1  1 0 0 0  0 0 0 0  1 0 0 0",
                          "x x x x  x x x x  o o o o  o o o o";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "efuse"
        size            = 1;
        write           = "1 0 1 0  1 1 0 0  1 0 1 0  0 1 0 0",
                          "x x x x  x x x x  x x x x  x i i i";

        read            = "0 1 0 1  0 0 0 0  0 0 0 0  1 0 0 0",
                          "x x x x  x x x x  o o o o  o o o o";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "lock"
        size            = 1;
        read            = "0 1 0 1  1 0 0 0   0 0 0 0  0 0 0 0",
                          "x x x x  x x x x   x x o o  o o o o";

        write           = "1 0 1 0  1 1 0 0   1 1 1 x  x x x x",
                          "x x x x  x x x x   1 1 i i  i i i i";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "calibration"
        size            = 1;
        read            = "0 0 1 1  1 0 0 0    x x x x  x x x x",
                          "0 0 0 0  0 0 0 0    o o o o  o o o o";
      ;

    memory "signature"
        size            = 3;
        read            = "0  0  1  1   0  0  0  0   x  x  x  x   x  x  x  x",
                          "x  x  x  x   x  x a1 a0   o  o  o  o   o  o  o  o";
      ;
  ;

#------------------------------------------------------------
# ATmega1281
#------------------------------------------------------------
# Identical to ATmega1280

part parent "m1280"
    id               = "m1281";
    desc             = "ATmega1281";
    signature        = 0x1e 0x97 0x04;

    ocdrev              = 3;
  ;

#------------------------------------------------------------
# ATmega2560
#------------------------------------------------------------

part
    id               = "m2560";
    desc             = "ATmega2560";
    signature        = 0x1e 0x98 0x01;
    has_jtag         = yes;
    stk500_devcode   = 0xB2;
#    avr910_devcode   = 0x43;
    chip_erase_delay = 9000;
    pagel            = 0xD7;
    bs2              = 0xA0;
    reset            = dedicated;
    pgm_enable       = "1 0 1 0  1 1 0 0    0 1 0 1  0 0 1 1",
                       "x x x x  x x x x    x x x x  x x x x";

    chip_erase       = "1 0 1 0  1 1 0 0    1 0 0 0  0 0 0 0",
                       "x x x x  x x x x    x x x x  x x x x";

    timeout		= 200;
    stabdelay		= 100;
    cmdexedelay		= 25;
    synchloops		= 32;
    bytedelay		= 0;
    pollindex		= 3;
    pollvalue		= 0x53;
    predelay		= 1;
    postdelay		= 1;
    pollmethod		= 1;

    pp_controlstack     =
        0x0E, 0x1E, 0x0F, 0x1F, 0x2E, 0x3E, 0x2F, 0x3F,
        0x4E, 0x5E, 0x4F, 0x5F, 0x6E, 0x7E, 0x6F, 0x7F,
        0x66, 0x76, 0x67, 0x77, 0x6A, 0x7A, 0x6B, 0x7B,
        0xBE, 0xFD, 0x00, 0x01, 0x00, 0x00, 0x00, 0x02;
    hventerstabdelay    = 100;
    progmodedelay       = 0;
    latchcycles         = 5;
    togglevtg           = 1;
    poweroffdelay       = 15;
    resetdelayms        = 1;
    resetdelayus        = 0;
    hvleavestabdelay    = 15;
    chiperasepulsewidth = 0;
    chiperasepolltimeout = 10;
    programfusepulsewidth = 0;
    programfusepolltimeout = 5;
    programlockpulsewidth = 0;
    programlockpolltimeout = 5;

    idr                 = 0x31;
    spmcr               = 0x57;
    rampz               = 0x3b;
    allowfullpagebitstream = no;

    ocdrev              = 4;

    memory "eeprom"
        paged           = no; /* leave this "no" */
        page_size       = 8;  /* for parallel programming */
        size            = 4096;
        min_write_delay = 9000;
        max_write_delay = 9000;
        readback_p1     = 0x00;
        readback_p2     = 0x00;
        read            = "  1   0   1   0      0   0   0   0",
                          "  x   x   x   x    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

        write           = "  1   1   0   0      0   0   0   0",
                          "  x   x   x   x    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0", 
                          "  i   i   i   i      i   i   i   i";

	loadpage_lo	= "  1   1   0   0      0   0   0   1",
			  "  0   0   0   0      0   0   0   0",
			  "  0   0   0   0      0  a2  a1  a0",
			  "  i   i   i   i      i   i   i   i";

	writepage	= "  1   1   0   0      0   0   1   0",
			  "  0   0   x   x    a11 a10  a9  a8",
			  " a7  a6  a5  a4     a3   0   0   0",
			  "  x   x   x   x      x   x   x   x";

	mode		= 0x41;
	delay		= 10;
	blocksize	= 8;
	readsize	= 256;
      ;

    memory "flash"
        paged           = yes;
        size            = 262144;
        page_size       = 256;
        num_pages       = 1024;
        min_write_delay = 4500;
        max_write_delay = 4500;
        readback_p1     = 0x00;
        readback_p2     = 0x00;
        read_lo         = "  0   0   1   0      0   0   0   0",
                          "a15 a14 a13 a12    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

        read_hi         = "  0   0   1   0      1   0   0   0",
                          "a15 a14 a13 a12    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

        loadpage_lo     = "  0   1   0   0      0   0   0   0",
                          "  x   x   x   x      x   x   x   x",
                          "  x  a6  a5  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

        loadpage_hi     = "  0   1   0   0      1   0   0   0",
                          "  x   x   x   x      x   x   x   x",
                          "  x  a6  a5  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

        writepage       = "  0   1   0   0      1   1   0   0",
                          "a15 a14 a13 a12    a11 a10  a9  a8",
                          " a7   x   x   x      x   x   x   x",
                          "  x   x   x   x      x   x   x   x";

        load_ext_addr   = "  0   1   0   0      1   1   0   1",
                          "  0   0   0   0      0   0   0   0",
                          "  0   0   0   0      0   0   0 a16",
                          "  0   0   0   0      0   0   0   0";

	mode		= 0x41;
	delay		= 10;
	blocksize	= 256;
	readsize	= 256;
      ;

    memory "lfuse"
        size            = 1;
        write           = "1 0 1 0  1 1 0 0  1 0 1 0  0 0 0 0",
                          "x x x x  x x x x  i i i i  i i i i";

        read            = "0 1 0 1  0 0 0 0  0 0 0 0  0 0 0 0",
                          "x x x x  x x x x  o o o o  o o o o";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "hfuse"
        size            = 1;
        write           = "1 0 1 0  1 1 0 0  1 0 1 0  1 0 0 0",
                          "x x x x  x x x x  i i i i  i i i i";

        read            = "0 1 0 1  1 0 0 0  0 0 0 0  1 0 0 0",
                          "x x x x  x x x x  o o o o  o o o o";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "efuse"
        size            = 1;
        write           = "1 0 1 0  1 1 0 0  1 0 1 0  0 1 0 0",
                          "x x x x  x x x x  x x x x  x i i i";

        read            = "0 1 0 1  0 0 0 0  0 0 0 0  1 0 0 0",
                          "x x x x  x x x x  o o o o  o o o o";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "lock"
        size            = 1;
        read            = "0 1 0 1  1 0 0 0   0 0 0 0  0 0 0 0",
                          "x x x x  x x x x   x x o o  o o o o";

        write           = "1 0 1 0  1 1 0 0   1 1 1 x  x x x x",
                          "x x x x  x x x x   1 1 i i  i i i i";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "calibration"
        size            = 1;
        read            = "0 0 1 1  1 0 0 0    x x x x  x x x x",
                          "0 0 0 0  0 0 0 0    o o o o  o o o o";
      ;

    memory "signature"
        size            = 3;
        read            = "0  0  1  1   0  0  0  0   x  x  x  x   x  x  x  x",
                          "x  x  x  x   x  x a1 a0   o  o  o  o   o  o  o  o";
      ;
  ;

#------------------------------------------------------------
# ATmega2561
#------------------------------------------------------------

part parent "m2560"
    id               = "m2561";
    desc             = "ATmega2561";
    signature        = 0x1e 0x98 0x02;

    ocdrev              = 4;
  ;

#------------------------------------------------------------
# ATmega128RFA1
#------------------------------------------------------------
# Identical to ATmega2561 but half the ROM

part parent "m2561"
    id               = "m128rfa1";
    desc             = "ATmega128RFA1";
    signature        = 0x1e 0xa7 0x01;
    chip_erase_delay = 55000;
    bs2              = 0xE2;

    ocdrev              = 3;

    memory "flash"
        paged           = yes;
        size            = 131072;
        page_size       = 256;
        num_pages       = 512;
        min_write_delay = 50000;
        max_write_delay = 50000;
        readback_p1     = 0x00;
        readback_p2     = 0x00;
        read_lo         = "  0   0   1   0      0   0   0   0",
                          "a15 a14 a13 a12    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

        read_hi         = "  0   0   1   0      1   0   0   0",
                          "a15 a14 a13 a12    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

        loadpage_lo     = "  0   1   0   0      0   0   0   0",
                          "  x   x   x   x      x   x   x   x",
                          "  x  a6  a5  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

        loadpage_hi     = "  0   1   0   0      1   0   0   0",
                          "  x   x   x   x      x   x   x   x",
                          "  x  a6  a5  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

        writepage       = "  0   1   0   0      1   1   0   0",
                          "a15 a14 a13 a12    a11 a10  a9  a8",
                          " a7   x   x   x      x   x   x   x",
                          "  x   x   x   x      x   x   x   x";

	mode		= 0x41;
	delay		= 20;
	blocksize	= 256;
	readsize	= 256;
      ;
  ;

#------------------------------------------------------------
# ATmega256RFR2
#------------------------------------------------------------

part parent "m2561"
    id               = "m256rfr2";
    desc             = "ATmega256RFR2";
    signature        = 0x1e 0xa8 0x02;
    chip_erase_delay = 18500;
    bs2              = 0xE2;

    memory "eeprom"
        paged           = no; /* leave this "no" */
        page_size       = 8;  /* for parallel programming */
        size            = 8192;
        min_write_delay = 13000;
        max_write_delay = 13000;
        readback_p1     = 0x00;
        readback_p2     = 0x00;
        read            = "  1   0   1   0      0   0   0   0",
                          "  x   x   x a12    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

        write           = "  1   1   0   0      0   0   0   0",
                          "  x   x   x a12    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

	loadpage_lo	= "  1   1   0   0      0   0   0   1",
			  "  0   0   0   0      0   0   0   0",
			  "  0   0   0   0      0  a2  a1  a0",
			  "  i   i   i   i      i   i   i   i";

	writepage	= "  1   1   0   0      0   0   1   0",
			  "  0   0   x a12    a11 a10  a9  a8",
			  " a7  a6  a5  a4     a3   0   0   0",
			  "  x   x   x   x      x   x   x   x";

	mode		= 0x41;
	delay		= 10;
	blocksize	= 8;
	readsize	= 256;
      ;


    ocdrev              = 4;
  ;

#------------------------------------------------------------
# ATmega128RFR2
#------------------------------------------------------------

part parent "m128rfa1"
    id               = "m128rfr2";
    desc             = "ATmega128RFR2";
    signature        = 0x1e 0xa7 0x02;


    ocdrev              = 3;
  ;

#------------------------------------------------------------
# ATmega64RFR2
#------------------------------------------------------------

part parent "m128rfa1"
    id               = "m64rfr2";
    desc             = "ATmega64RFR2";
    signature        = 0x1e 0xa6 0x02;


    ocdrev              = 3;

    memory "flash"
        paged           = yes;
        size            = 65536;
        page_size       = 256;
        num_pages       = 256;
        min_write_delay = 50000;
        max_write_delay = 50000;
        readback_p1     = 0x00;
        readback_p2     = 0x00;
        read_lo         = "  0   0   1   0      0   0   0   0",
                          "  0 a14 a13 a12    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

        read_hi         = "  0   0   1   0      1   0   0   0",
                          "  0 a14 a13 a12    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

        loadpage_lo     = "  0   1   0   0      0   0   0   0",
                          "  x   x   x   x      x   x   x   x",
                          "  x  a6  a5  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

        loadpage_hi     = "  0   1   0   0      1   0   0   0",
                          "  x   x   x   x      x   x   x   x",
                          "  x  a6  a5  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

        writepage       = "  0   1   0   0      1   1   0   0",
                          "  0 a14 a13 a12    a11 a10  a9  a8",
                          " a7   x   x   x      x   x   x   x",
                          "  x   x   x   x      x   x   x   x";

	mode		= 0x41;
	delay		= 20;
	blocksize	= 256;
	readsize	= 256;
      ;

    memory "eeprom"
        paged           = no; /* leave this "no" */
        page_size       = 8;  /* for parallel programming */
        size            = 2048;
        min_write_delay = 13000;
        max_write_delay = 13000;
        readback_p1     = 0x00;
        readback_p2     = 0x00;
        read            = "  1   0   1   0      0   0   0   0",
                          "  x   x   x   x      x a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

        write           = "  1   1   0   0      0   0   0   0",
                          "  x   x   x   x      x a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

	loadpage_lo	= "  1   1   0   0      0   0   0   1",
			  "  0   0   0   0      0   0   0   0",
			  "  0   0   0   0      0  a2  a1  a0",
			  "  i   i   i   i      i   i   i   i";

	writepage	= "  1   1   0   0      0   0   1   0",
			  "  0   0   x   x      x a10  a9  a8",
			  " a7  a6  a5  a4     a3   0   0   0",
			  "  x   x   x   x      x   x   x   x";

	mode		= 0x41;
	delay		= 10;
	blocksize	= 8;
	readsize	= 256;
      ;


  ;

#------------------------------------------------------------
# ATmega2564RFR2
#------------------------------------------------------------

part parent "m256rfr2"
    id               = "m2564rfr2";
    desc             = "ATmega2564RFR2";
    signature        = 0x1e 0xa8 0x03;
  ;

#------------------------------------------------------------
# ATmega1284RFR2
#------------------------------------------------------------

part parent "m128rfr2"
    id               = "m1284rfr2";
    desc             = "ATmega1284RFR2";
    signature        = 0x1e 0xa7 0x03;
  ;

#------------------------------------------------------------
# ATmega644RFR2
#------------------------------------------------------------

part parent "m64rfr2"
    id               = "m644rfr2";
    desc             = "ATmega644RFR2";
    signature        = 0x1e 0xa6 0x03;
  ;

#------------------------------------------------------------
# ATtiny24
#------------------------------------------------------------

part
     id            = "t24";
     desc          = "ATtiny24";
     has_debugwire = yes;
     flash_instr   = 0xB4, 0x07, 0x17;
     eeprom_instr  = 0xBB, 0xFF, 0xBB, 0xEE, 0xBB, 0xCC, 0xB2, 0x0D,
	             0xBC, 0x07, 0xB4, 0x07, 0xBA, 0x0D, 0xBB, 0xBC,
	             0x99, 0xE1, 0xBB, 0xAC;
## no STK500 devcode in XML file, use the ATtiny45 one
     stk500_devcode   = 0x14;
##  avr910_devcode   = ?;
##  Try the AT90S2313 devcode:
     avr910_devcode   = 0x20;
     signature        = 0x1e 0x91 0x0b;
     reset            = io;
     chip_erase_delay = 4500;

     pgm_enable       = "1 0 1 0  1 1 0 0    0 1 0 1  0 0 1 1",
                        "x x x x  x x x x    x x x x  x x x x";

     chip_erase       = "1 0 1 0  1 1 0 0    1 0 0 x  x x x x",
                        "x x x x  x x x x    x x x x  x x x x";

    timeout		= 200;
    stabdelay		= 100;
    cmdexedelay		= 25;
    synchloops		= 32;
    bytedelay		= 0;
    pollindex		= 3;
    pollvalue		= 0x53;
    predelay		= 1;
    postdelay		= 1;
    pollmethod		= 1;

    hvsp_controlstack   =
        0x4C, 0x0C, 0x1C, 0x2C, 0x3C, 0x64, 0x74, 0x66,
        0x68, 0x78, 0x68, 0x68, 0x7A, 0x6A, 0x68, 0x78,
        0x78, 0x7D, 0x6D, 0x0C, 0x80, 0x40, 0x20, 0x10,
        0x11, 0x08, 0x04, 0x02, 0x03, 0x08, 0x04, 0x0F;
    hventerstabdelay    = 100;
    hvspcmdexedelay     = 0;
    synchcycles         = 6;
    latchcycles         = 1;
    togglevtg           = 1;
    poweroffdelay       = 25;
    resetdelayms        = 0;
    resetdelayus        = 70;
    hvleavestabdelay    = 100;
    resetdelay          = 25;
    chiperasepolltimeout = 40;
    chiperasetime       = 0;
    programfusepolltimeout = 25;
    programlockpolltimeout = 25;

    ocdrev              = 1;

     memory "eeprom"
         size            = 128;
        paged           = no;
        page_size       = 4;
         min_write_delay = 4000;
         max_write_delay = 4500;
         readback_p1     = 0xff;
         readback_p2     = 0xff;
         read            = "1  0  1  0   0  0  0  0   0 0 0 x  x x x x",
                           "x a6 a5 a4  a3 a2 a1 a0   o o o o  o o o o";

         write           = "1  1  0  0   0  0  0  0   0 0 0 x  x x x x",
                           "x a6 a5 a4  a3 a2 a1 a0   i i i i  i i i i";

	loadpage_lo	= "  1   1   0   0      0   0   0   1",
			  "  0   0   0   0      0   0   0   0",
			  "  0   0   0   0      0   0  a1  a0",
			  "  i   i   i   i      i   i   i   i";

	writepage	= "  1   1   0   0      0   0   1   0",
			  "  0   0   x   x      x   x   x   x",
			  "  x  a6  a5  a4     a3  a2   0   0",
			  "  x   x   x   x      x   x   x   x";

	mode		= 0x41;
	delay		= 6;
	blocksize	= 4;
	readsize	= 256;
       ;
     memory "flash"
         paged           = yes;
         size            = 2048;
         page_size       = 32;
         num_pages       = 64;
         min_write_delay = 4500;
         max_write_delay = 4500;
         readback_p1     = 0xff;
         readback_p2     = 0xff;
         read_lo         = "  0   0   1   0    0   0   0   0",
                           "  0   0   0   0    0   0  a9  a8",
                           " a7  a6  a5  a4   a3  a2  a1  a0",
                           "  o   o   o   o    o   o   o   o";

         read_hi         = "  0   0   1   0    1   0   0   0",
                           "  0   0   0   0    0   0  a9  a8",
                           " a7  a6  a5  a4   a3  a2  a1  a0",
                           "  o   o   o   o    o   o   o   o";

         loadpage_lo     = "  0   1   0   0    0   0   0   0",
                           "  0   0   0   x    x   x   x   x",
                           "  x   x   x   x   a3  a2  a1  a0",
                           "  i   i   i   i    i   i   i   i";

         loadpage_hi     = "  0   1   0   0    1   0   0   0",
                           "  0   0   0   x    x   x   x   x",
                           "  x   x   x   x   a3  a2  a1  a0",
                           "  i   i   i   i    i   i   i   i";

         writepage       = "  0  1  0  0   1  1  0  0",
                           "  0  0  0  0   0  0 a9 a8",
                           " a7 a6 a5 a4   x  x  x  x",
                           "  x  x  x  x   x  x  x  x";

	mode		= 0x41;
	delay		= 6;
	blocksize	= 32;
	readsize	= 256;
       ;
#   ATtiny24 has Signature Bytes: 0x1E 0x91 0x0B.
     memory "signature"
         size            = 3;
         read            = "0  0  1  1   0  0  0  0   0  0  0  x   x  x  x  x",
                           "x  x  x  x   x  x a1 a0   o  o  o  o   o  o  o  o";
       ;
     memory "lock"
         size            = 1;
         write           = "1 0 1 0  1 1 0 0  1 1 1 x  x x x x",
                           "x x x x  x x x x  x x x x  x x i i";
         read            = "0 1 0 1  1 0 0 0  0 0 0 0  0 0 0 0",
                           "0 0 0 0  0 0 0 0  o o o o  o o o o";
        min_write_delay = 9000;
        max_write_delay = 9000;
       ;

     memory "lfuse"
         size            = 1;
         write           = "1 0 1 0  1 1 0 0  1 0 1 0  0 0 0 0",
                           "x x x x  x x x x  i i i i  i i i i";

         read            = "0 1 0 1  0 0 0 0  0 0 0 0  0 0 0 0",
                           "x x x x  x x x x  o o o o  o o o o";
        min_write_delay = 9000;
        max_write_delay = 9000;
       ;

     memory "hfuse"
         size            = 1;
         write           = "1 0 1 0  1 1 0 0  1 0 1 0  1 0 0 0",
                           "x x x x  x x x x  i i i i  i i i i";

         read            = "0 1 0 1  1 0 0 0  0 0 0 0  1 0 0 0",
                           "x x x x  x x x x  o o o o  o o o o";
        min_write_delay = 9000;
        max_write_delay = 9000;
       ;

     memory "efuse"
         size            = 1;
         write           = "1 0 1 0  1 1 0 0  1 0 1 0  0 1 0 0",
                           "x x x x  x x x x  x x x x  x x x i";

         read            = "0 1 0 1  0 0 0 0  0 0 0 0  1 0 0 0",
                           "x x x x  x x x x  o o o o  o o o o";
        min_write_delay = 9000;
        max_write_delay = 9000;
     ;

     memory "calibration"
         size            = 1;
         read            = "0  0  1  1   1  0  0  0    0 0 0 x  x x x x",
                           "0  0  0  0   0  0  0  a0   o o o o  o o o o";
     ;
  ;

#------------------------------------------------------------
# ATtiny44
#------------------------------------------------------------

part
     id            = "t44";
     desc          = "ATtiny44";
     has_debugwire = yes;
     flash_instr   = 0xB4, 0x07, 0x17;
     eeprom_instr  = 0xBB, 0xFF, 0xBB, 0xEE, 0xBB, 0xCC, 0xB2, 0x0D,
                     0xBC, 0x07, 0xB4, 0x07, 0xBA, 0x0D, 0xBB, 0xBC,
                     0x99, 0xE1, 0xBB, 0xAC;
## no STK500 devcode in XML file, use the ATtiny45 one
     stk500_devcode   = 0x14;
##  avr910_devcode   = ?;
##  Try the AT90S2313 devcode:
     avr910_devcode   = 0x20;
     signature        = 0x1e 0x92 0x07;
     reset            = io;
     chip_erase_delay = 4500;

     pgm_enable       = "1 0 1 0  1 1 0 0    0 1 0 1  0 0 1 1",
                        "x x x x  x x x x    x x x x  x x x x";

     chip_erase       = "1 0 1 0  1 1 0 0    1 0 0 x  x x x x",
                        "x x x x  x x x x    x x x x  x x x x";

    timeout		= 200;
    stabdelay		= 100;
    cmdexedelay		= 25;
    synchloops		= 32;
    bytedelay		= 0;
    pollindex		= 3;
    pollvalue		= 0x53;
    predelay		= 1;
    postdelay		= 1;
    pollmethod		= 1;

    hvsp_controlstack   =
        0x4C, 0x0C, 0x1C, 0x2C, 0x3C, 0x64, 0x74, 0x66,
        0x68, 0x78, 0x68, 0x68, 0x7A, 0x6A, 0x68, 0x78,
        0x78, 0x7D, 0x6D, 0x0C, 0x80, 0x40, 0x20, 0x10,
        0x11, 0x08, 0x04, 0x02, 0x03, 0x08, 0x04, 0x0F;
    hventerstabdelay    = 100;
    hvspcmdexedelay     = 0;
    synchcycles         = 6;
    latchcycles         = 1;
    togglevtg           = 1;
    poweroffdelay       = 25;
    resetdelayms        = 0;
    resetdelayus        = 70;
    hvleavestabdelay    = 100;
    resetdelay          = 25;
    chiperasepolltimeout = 40;
    chiperasetime       = 0;
    programfusepolltimeout = 25;
    programlockpolltimeout = 25;

    ocdrev              = 1;

     memory "eeprom"
         size            = 256;
        paged           = no;
        page_size       = 4;
         min_write_delay = 4000;
         max_write_delay = 4500;
         readback_p1     = 0xff;
         readback_p2     = 0xff;
         read            = "1  0  1  0   0  0  0  0    0 0 0 x  x x x x",
                           "a7 a6 a5 a4  a3 a2 a1 a0   o o o o  o o o o";

         write           = "1  1  0  0   0  0  0  0    0 0 0 x  x x x x",
                           "a7 a6 a5 a4  a3 a2 a1 a0   i i i i  i i i i";

	loadpage_lo	= "  1   1   0   0      0   0   0   1",
			  "  0   0   0   0      0   0   0   0",
			  "  0   0   0   0      0   0  a1  a0",
			  "  i   i   i   i      i   i   i   i";

	writepage	= "  1   1   0   0      0   0   1   0",
			  "  0   0   x   x      x   x   x   x",
			  "  x  a6  a5  a4     a3  a2   0   0",
			  "  x   x   x   x      x   x   x   x";

	mode		= 0x41;
	delay		= 6;
	blocksize	= 4;
	readsize	= 256;
       ;
     memory "flash"
         paged           = yes;
         size            = 4096;
         page_size       = 64;
         num_pages       = 64;
         min_write_delay = 4500;
         max_write_delay = 4500;
         readback_p1     = 0xff;
         readback_p2     = 0xff;
         read_lo         = "  0   0   1   0    0   0   0   0",
                           "  0   0   0   0    0  a10 a9  a8",
                           " a7  a6  a5  a4   a3  a2  a1  a0",
                           "  o   o   o   o    o   o   o   o";

         read_hi         = "  0   0   1   0    1   0   0   0",
                           "  0   0   0   0    0  a10 a9  a8",
                           " a7  a6  a5  a4   a3  a2  a1  a0",
                           "  o   o   o   o    o   o   o   o";

         loadpage_lo     = "  0   1   0   0    0   0   0   0",
                           "  0   0   0   x    x   x   x   x",
                           "  x   x   x  a4   a3  a2  a1  a0",
                           "  i   i   i   i    i   i   i   i";

         loadpage_hi     = "  0   1   0   0    1   0   0   0",
                           "  0   0   0   x    x   x   x   x",
                           "  x   x   x  a4   a3  a2  a1  a0",
                           "  i   i   i   i    i   i   i   i";

         writepage       = "  0  1  0  0   1  1  0  0",
                           "  0  0  0  0   0 a10 a9 a8",
                           " a7 a6 a5  x   x  x  x  x",
                           "  x  x  x  x   x  x  x  x";

	mode		= 0x41;
	delay		= 6;
	blocksize	= 32;
	readsize	= 256;
       ;
#   ATtiny44 has Signature Bytes: 0x1E 0x92 0x07.
     memory "signature"
         size            = 3;
         read            = "0  0  1  1   0  0  0  0   0  0  0  x   x  x  x  x",
                           "x  x  x  x   x  x a1 a0   o  o  o  o   o  o  o  o";
       ;
     memory "lock"
         size            = 1;
         write           = "1 0 1 0  1 1 0 0  1 1 1 x  x x x x",
                           "x x x x  x x x x  x x x x  x x i i";
         read            = "0 1 0 1  1 0 0 0  0 0 0 0  0 0 0 0",
                           "0 0 0 0  0 0 0 0  o o o o  o o o o";
        min_write_delay = 9000;
        max_write_delay = 9000;
       ;

     memory "lfuse"
         size            = 1;
         write           = "1 0 1 0  1 1 0 0  1 0 1 0  0 0 0 0",
                           "x x x x  x x x x  i i i i  i i i i";

         read            = "0 1 0 1  0 0 0 0  0 0 0 0  0 0 0 0",
                           "x x x x  x x x x  o o o o  o o o o";
        min_write_delay = 9000;
        max_write_delay = 9000;
       ;

     memory "hfuse"
         size            = 1;
         write           = "1 0 1 0  1 1 0 0  1 0 1 0  1 0 0 0",
                           "x x x x  x x x x  i i i i  i i i i";

         read            = "0 1 0 1  1 0 0 0  0 0 0 0  1 0 0 0",
                           "x x x x  x x x x  o o o o  o o o o";
        min_write_delay = 9000;
        max_write_delay = 9000;
       ;

     memory "efuse"
         size            = 1;
         write           = "1 0 1 0  1 1 0 0  1 0 1 0  0 1 0 0",
                           "x x x x  x x x x  x x x x  x x x i";

         read            = "0 1 0 1  0 0 0 0  0 0 0 0  1 0 0 0",
                           "x x x x  x x x x  o o o o  o o o o";
        min_write_delay = 9000;
        max_write_delay = 9000;
     ;

     memory "calibration"
         size            = 1;
         read            = "0  0  1  1   1  0  0  0    0 0 0 x  x x x x",
                           "0  0  0  0   0  0  0  a0   o o o o  o o o o";
     ;
  ;

#------------------------------------------------------------
# ATtiny84
#------------------------------------------------------------

part
     id            = "t84";
     desc          = "ATtiny84";
     has_debugwire = yes;
     flash_instr   = 0xB4, 0x07, 0x17;
     eeprom_instr  = 0xBB, 0xFF, 0xBB, 0xEE, 0xBB, 0xCC, 0xB2, 0x0D,
	             0xBC, 0x07, 0xB4, 0x07, 0xBA, 0x0D, 0xBB, 0xBC,
	             0x99, 0xE1, 0xBB, 0xAC;
## no STK500 devcode in XML file, use the ATtiny45 one
     stk500_devcode   = 0x14;
##  avr910_devcode   = ?;
##  Try the AT90S2313 devcode:
     avr910_devcode   = 0x20;
     signature        = 0x1e 0x93 0x0c;
     reset            = io;
     chip_erase_delay = 4500;

     pgm_enable       = "1 0 1 0  1 1 0 0    0 1 0 1  0 0 1 1",
                        "x x x x  x x x x    x x x x  x x x x";

     chip_erase       = "1 0 1 0  1 1 0 0    1 0 0 x  x x x x",
                        "x x x x  x x x x    x x x x  x x x x";

    timeout		= 200;
    stabdelay		= 100;
    cmdexedelay		= 25;
    synchloops		= 32;
    bytedelay		= 0;
    pollindex		= 3;
    pollvalue		= 0x53;
    predelay		= 1;
    postdelay		= 1;
    pollmethod		= 1;

    hvsp_controlstack   =
        0x4C, 0x0C, 0x1C, 0x2C, 0x3C, 0x64, 0x74, 0x66,
        0x68, 0x78, 0x68, 0x68, 0x7A, 0x6A, 0x68, 0x78,
        0x78, 0x7D, 0x6D, 0x0C, 0x80, 0x40, 0x20, 0x10,
        0x11, 0x08, 0x04, 0x02, 0x03, 0x08, 0x04, 0x0F;
    hventerstabdelay    = 100;
    hvspcmdexedelay     = 0;
    synchcycles         = 6;
    latchcycles         = 1;
    togglevtg           = 1;
    poweroffdelay       = 25;
    resetdelayms        = 0;
    resetdelayus        = 70;
    hvleavestabdelay    = 100;
    resetdelay          = 25;
    chiperasepolltimeout = 40;
    chiperasetime       = 0;
    programfusepolltimeout = 25;
    programlockpolltimeout = 25;

    ocdrev              = 1;

     memory "eeprom"
         size            = 512;
        paged           = no;
        page_size       = 4;
         min_write_delay = 4000;
         max_write_delay = 4500;
         readback_p1     = 0xff;
         readback_p2     = 0xff;
         read            = "1  0  1  0   0  0  0  0    0 0 0 x  x x x a8",
                           "a7 a6 a5 a4  a3 a2 a1 a0   o o o o  o o o o";

         write           = "1  1  0  0   0  0  0  0    0 0 0 x  x x x a8",
                           "a7 a6 a5 a4  a3 a2 a1 a0   i i i i  i i i i";

	loadpage_lo	= "  1   1   0   0      0   0   0   1",
			  "  0   0   0   0      0   0   0   0",
			  "  0   0   0   0      0   0  a1  a0",
			  "  i   i   i   i      i   i   i   i";

	writepage	= "  1   1   0   0      0   0   1   0",
			  "  0   0   x   x      x   x   x   x",
			  "  x  a6  a5  a4     a3  a2   0   0",
			  "  x   x   x   x      x   x   x   x";

	mode		= 0x41;
	delay		= 6;
	blocksize	= 4;
	readsize	= 256;
       ;
     memory "flash"
         paged           = yes;
         size            = 8192;
         page_size       = 64;
         num_pages       = 128;
         min_write_delay = 4500;
         max_write_delay = 4500;
         readback_p1     = 0xff;
         readback_p2     = 0xff;
         read_lo         = "  0   0   1   0    0   0   0   0",
                           "  0   0   0   0  a11 a10  a9  a8",
                           " a7  a6  a5  a4   a3  a2  a1  a0",
                           "  o   o   o   o    o   o   o   o";

         read_hi         = "  0   0   1   0    1   0   0   0",
                           "  0   0   0   0  a11 a10  a9  a8",
                           " a7  a6  a5  a4   a3  a2  a1  a0",
                           "  o   o   o   o    o   o   o   o";

         loadpage_lo     = "  0   1   0   0    0   0   0   0",
                           "  0   0   0   x    x   x   x   x",
                           "  x   x   x  a4   a3  a2  a1  a0",
                           "  i   i   i   i    i   i   i   i";

         loadpage_hi     = "  0   1   0   0    1   0   0   0",
                           "  0   0   0   x    x   x   x   x",
                           "  x   x   x  a4   a3  a2  a1  a0",
                           "  i   i   i   i    i   i   i   i";

         writepage       = "  0  1  0  0   1   1   0  0",
                           "  0  0  0  0  a11 a10 a9 a8",
                           " a7 a6 a5  x   x  x  x  x",
                           "  x  x  x  x   x  x  x  x";

	mode		= 0x41;
	delay		= 6;
	blocksize	= 32;
	readsize	= 256;
       ;
#   ATtiny84 has Signature Bytes: 0x1E 0x93 0x0C.
     memory "signature"
         size            = 3;
         read            = "0  0  1  1   0  0  0  0   0  0  0  x   x  x  x  x",
                           "x  x  x  x   x  x a1 a0   o  o  o  o   o  o  o  o";
       ;

     memory "lock"
         size            = 1;
         write           = "1 0 1 0  1 1 0 0  1 1 1 x  x x x x",
                           "x x x x  x x x x  x x x x  x x i i";
         read            = "0 1 0 1  1 0 0 0  0 0 0 0  0 0 0 0",
                           "0 0 0 0  0 0 0 0  o o o o  o o o o";
        min_write_delay = 9000;
        max_write_delay = 9000;
       ;

     memory "lfuse"
         size            = 1;
         write           = "1 0 1 0  1 1 0 0  1 0 1 0  0 0 0 0",
                           "x x x x  x x x x  i i i i  i i i i";

         read            = "0 1 0 1  0 0 0 0  0 0 0 0  0 0 0 0",
                           "x x x x  x x x x  o o o o  o o o o";
        min_write_delay = 9000;
        max_write_delay = 9000;
       ;

     memory "hfuse"
         size            = 1;
         write           = "1 0 1 0  1 1 0 0  1 0 1 0  1 0 0 0",
                           "x x x x  x x x x  i i i i  i i i i";

         read            = "0 1 0 1  1 0 0 0  0 0 0 0  1 0 0 0",
                           "x x x x  x x x x  o o o o  o o o o";
        min_write_delay = 9000;
        max_write_delay = 9000;
       ;

     memory "efuse"
         size            = 1;
         write           = "1 0 1 0  1 1 0 0  1 0 1 0  0 1 0 0",
                           "x x x x  x x x x  x x x x  x x x i";

         read            = "0 1 0 1  0 0 0 0  0 0 0 0  1 0 0 0",
                           "x x x x  x x x x  o o o o  o o o o";
        min_write_delay = 9000;
        max_write_delay = 9000;
     ;

     memory "calibration"
         size            = 1;
         read            = "0  0  1  1   1  0  0  0    0 0 0 x  x x x x",
                           "0  0  0  0   0  0  0  a0   o o o o  o o o o";
     ;
  ;

#------------------------------------------------------------
# ATtiny43U
#------------------------------------------------------------

part
    id            = "t43u";
    desc          = "ATtiny43u";
    has_debugwire = yes;
    flash_instr   = 0xB4, 0x07, 0x17;
    eeprom_instr  = 0xBB, 0xFF, 0xBB, 0xEE, 0xBB, 0xCC, 0xB2, 0x0D,
                         0xBC, 0x07, 0xB4, 0x07, 0xBA, 0x0D, 0xBB, 0xBC,
                         0x99, 0xE1, 0xBB, 0xAC;
    stk500_devcode   = 0x14;
##  avr910_devcode   = ?;
##  Try the AT90S2313 devcode:
    avr910_devcode   = 0x20;
    signature        = 0x1e 0x92 0x0C;
    reset            = io;
    chip_erase_delay = 1000;

    pgm_enable       = "1 0 1 0  1 1 0 0    0 1 0 1  0 0 1 1",
                        "x x x x  x x x x    x x x x  x x x x";

    chip_erase       = "1 0 1 0  1 1 0 0    1 0 0 x  x x x x",
                        "x x x x  x x x x    x x x x  x x x x";

    timeout                     = 200;
    stabdelay           = 100;
    cmdexedelay         = 25;
    synchloops          = 32;
    bytedelay           = 0;
    pollindex           = 3;
    pollvalue           = 0x53;
    predelay            = 1;
    postdelay           = 1;
    pollmethod          = 1;
        pp_controlstack = 0x0E, 0x1E, 0x0E, 0x1E, 0x2E, 0x3E, 0x2E, 0x3E, 0x4E, 0x5E,
                                         0x4E, 0x5E, 0x6E, 0x7E, 0x6E, 0x7E, 0x06, 0x16, 0x46, 0x56,
                                         0x0A, 0x1A, 0x4A, 0x5A, 0x1E, 0x7C, 0x00, 0x01, 0x00, 0x00,
                                         0x00, 0x00;
    hventerstabdelay    = 100;
    progmodedelay       = 0;
    hvspcmdexedelay     = 0;
    latchcycles         = 5;
    togglevtg           = 1;
    poweroffdelay       = 20;
    resetdelayms        = 1;
    resetdelayus        = 0;
    hvleavestabdelay    = 15;
    resetdelay          = 15;
    chiperasepulsewidth = 0;
    chiperasepolltimeout = 10;
    programfusepulsewidth = 0;
    programfusepolltimeout = 5;
    programlockpulsewidth = 0;
    programlockpolltimeout = 5;
    memory "eeprom"
                size            = 64;
                paged                   = yes;
                page_size       = 4;
                num_pages               = 16;
                min_write_delay = 4000;
                max_write_delay = 4500;
                readback_p1     = 0xff;
                readback_p2     = 0xff;
                read            = "1  0  1  0   0  0  0  0    0 0 0 x  x x x x",
                                   "0  0 a4  a3 a2 a1 a0   o o o o  o o o o";

                write           = "1  1  0  0   0  0  0  0    0 0 0 x  x x x x",
                                   "0  0 a5 a4  a3 a2 a1 a0   i i i i  i i i i";

                loadpage_lo     = "  1   1   0   0      0   0   0   1",
                                  "  0   0   0   0      0   0   0   0",
                                  "  0   0   0   0      0   0  a1  a0",
                                  "  i   i   i   i      i   i   i   i";

                writepage       = "  1   1   0   0      0   0   1   0",
                                  "  0   0   x   x      x   x   x   x",
                                  "  0   0  a5  a4     a3  a2   0   0",
                                  "  x   x   x   x      x   x   x   x";

                mode            = 0x41;
                delay           = 5;
                blocksize       = 4;
                readsize        = 256;
        ;
    memory "flash"
        paged           = yes;
        size            = 4096;
        page_size       = 64;
        num_pages       = 64;
        min_write_delay = 4500;
        max_write_delay = 4500;
        readback_p1     = 0xff;
        readback_p2     = 0xff;

        read_lo         = "  0   0   1   0    0   0   0   0",
                          "  0   0   0   0    0  a10 a9  a8",
                          " a7  a6  a5  a4   a3  a2  a1  a0",
                          "  o   o   o   o    o   o   o   o";

        read_hi         = "  0   0   1   0    1   0   0   0",
                          "  0   0   0   0    0  a10 a9  a8",
                          " a7  a6  a5  a4   a3  a2  a1  a0",
                          "  o   o   o   o    o   o   o   o";

        loadpage_lo     = "  0   1   0   0    0   0   0   0",
                          "  0   0   0   x    x   x   x   x",
                          "  x   x   x  a4   a3  a2  a1  a0",
                          "  i   i   i   i    i   i   i   i";

        loadpage_hi     = "  0   1   0   0    1   0   0   0",
                          "  0   0   0   x    x   x   x   x",
                          "  x   x   x  a4   a3  a2  a1  a0",
                          "  i   i   i   i    i   i   i   i";

        writepage       = "  0  1  0  0   1  1  0  0",
                          "  0  0  0  0   0 a10 a9 a8",
                          " a7 a6 a5  x   x  x  x  x",
                          "  x  x  x  x   x  x  x  x";

                mode            = 0x41;
                delay           = 10;
                blocksize       = 64;
                readsize        = 256;
       ;
    memory "signature"
        size            = 3;
        read            = "0  0  1  1   0  0  0  0   0  0  0  x   x  x  x  x",
                          "x  x  x  x   x  x a1 a0   o  o  o  o   o  o  o  o";
    ;
    memory "lock"
        size            = 1;
        write           = "1 0 1 0  1 1 0 0  1 1 1 x  x x x x",
                          "x x x x  x x x x  1 1 i i  i i i i";
        min_write_delay = 4500;
        max_write_delay = 4500;
    ;

    memory "lfuse"
        size            = 1;
        write           = "1 0 1 0  1 1 0 0  1 0 1 0  0 0 0 0",
                          "x x x x  x x x x  i i i i  i i i i";

        read            = "0 1 0 1  0 0 0 0  0 0 0 0  0 0 0 0",
                          "x x x x  x x x x  o o o o  o o o o";
        min_write_delay = 4500;
        max_write_delay = 4500;
        ;

    memory "hfuse"
        size            = 1;
        write           = "1 0 1 0  1 1 0 0  1 0 1 0  1 0 0 0",
                          "x x x x  x x x x  i i i i  i i i i";

        read            = "0 1 0 1  1 0 0 0  0 0 0 0  1 0 0 0",
                          "x x x x  x x x x  o o o o  o o o o";
        min_write_delay = 4500;
        max_write_delay = 4500;
        ;

    memory "efuse"
        size            = 1;
        write           = "1 0 1 0  1 1 0 0  1 0 1 0  0 1 0 0",
                          "x x x x  x x x x  x x x x  x x x i";

        read            = "0 1 0 1  0 0 0 0  0 0 0 0  1 0 0 0",
                          "x x x x  x x x x  o o o o  o o o o";
        min_write_delay = 4500;
        max_write_delay = 4500;
    ;

    memory "calibration"
        size            = 2;
        read            = "0  0  1  1   1  0  0  0    0 0 0 x  x x x x",
                          "0  0  0  0   0  0  0  a0   o o o o  o o o o";
    ;
;

#------------------------------------------------------------
# ATmega32u4
#------------------------------------------------------------

part
    id               = "m32u4";
    desc             = "ATmega32U4";
    signature        = 0x1e 0x95 0x87;
    usbpid           = 0x2ff4;
    has_jtag         = yes;
#    stk500_devcode   = 0xB2;
#    avr910_devcode   = 0x43;
    chip_erase_delay = 9000;
    pagel            = 0xD7;
    bs2              = 0xA0;
    reset            = dedicated;
    pgm_enable       = "1 0 1 0  1 1 0 0    0 1 0 1  0 0 1 1",
                       "x x x x  x x x x    x x x x  x x x x";

    chip_erase       = "1 0 1 0  1 1 0 0    1 0 0 0  0 0 0 0",
                       "x x x x  x x x x    x x x x  x x x x";

    timeout		= 200;
    stabdelay		= 100;
    cmdexedelay		= 25;
    synchloops		= 32;
    bytedelay		= 0;
    pollindex		= 3;
    pollvalue		= 0x53;
    predelay		= 1;
    postdelay		= 1;
    pollmethod		= 1;

    pp_controlstack     =
        0x0E, 0x1E, 0x0F, 0x1F, 0x2E, 0x3E, 0x2F, 0x3F,
        0x4E, 0x5E, 0x4F, 0x5F, 0x6E, 0x7E, 0x6F, 0x7F,
        0x66, 0x76, 0x67, 0x77, 0x6A, 0x7A, 0x6B, 0x7B,
        0xBE, 0xFD, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00;
    hventerstabdelay    = 100;
    progmodedelay       = 0;
    latchcycles         = 5;
    togglevtg           = 1;
    poweroffdelay       = 15;
    resetdelayms        = 1;
    resetdelayus        = 0;
    hvleavestabdelay    = 15;
    chiperasepulsewidth = 0;
    chiperasepolltimeout = 10;
    programfusepulsewidth = 0;
    programfusepolltimeout = 5;
    programlockpulsewidth = 0;
    programlockpolltimeout = 5;

    idr                 = 0x31;
    spmcr               = 0x57;
    rampz               = 0x3b;
    allowfullpagebitstream = no;

    ocdrev              = 3;

    memory "eeprom"
        paged           = no; /* leave this "no" */
        page_size       = 4;  /* for parallel programming */
        size            = 1024;
        min_write_delay = 9000;
        max_write_delay = 9000;
        readback_p1     = 0x00;
        readback_p2     = 0x00;
        read            = "  1   0   1   0      0   0   0   0",
                          "  x   x   x   x      x a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

        write           = "  1   1   0   0      0   0   0   0",
                          "  x   x   x   x      x a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0", 
                          "  i   i   i   i      i   i   i   i";

	loadpage_lo	= "  1   1   0   0      0   0   0   1",
			  "  0   0   0   0      0   0   0   0",
			  "  0   0   0   0      0  a2  a1  a0",
			  "  i   i   i   i      i   i   i   i";

	writepage	= "  1   1   0   0      0   0   1   0",
			  "  0   0   x   x      x a10  a9  a8",
			  " a7  a6  a5  a4     a3   0   0   0",
			  "  x   x   x   x      x   x   x   x";

	mode		= 0x41;
	delay		= 20;
	blocksize	= 4;
	readsize	= 256;
      ;

    memory "flash"
        paged           = yes;
        size            = 32768;
        page_size       = 128;
        num_pages       = 256;
        min_write_delay = 4500;
        max_write_delay = 4500;
        readback_p1     = 0x00;
        readback_p2     = 0x00;
        read_lo         = "  0   0   1   0      0   0   0   0",
                          "  0 a14 a13 a12    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

        read_hi         = "  0   0   1   0      1   0   0   0",
                          "  0 a14 a13 a12    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

        loadpage_lo     = "  0   1   0   0      0   0   0   0",
                          "  x   x   x   x      x   x   x   x",
                          "  x   x  a5  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

        loadpage_hi     = "  0   1   0   0      1   0   0   0",
                          "  x   x   x   x      x   x   x   x",
                          "  x   x  a5  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

        writepage       = "  0   1   0   0      1   1   0   0",
                          " a15 a14 a13 a12    a11 a10  a9  a8",
                          " a7  a6   x   x      x   x   x   x",
                          "  x   x   x   x      x   x   x   x";

	mode		= 0x41;
	delay		= 6;
	blocksize	= 128;
	readsize	= 256;
      ;

    memory "lfuse"
        size            = 1;
        write           = "1 0 1 0  1 1 0 0  1 0 1 0  0 0 0 0",
                          "x x x x  x x x x  i i i i  i i i i";

        read            = "0 1 0 1  0 0 0 0  0 0 0 0  0 0 0 0",
                          "x x x x  x x x x  o o o o  o o o o";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "hfuse"
        size            = 1;
        write           = "1 0 1 0  1 1 0 0  1 0 1 0  1 0 0 0",
                          "x x x x  x x x x  i i i i  i i i i";

        read            = "0 1 0 1  1 0 0 0  0 0 0 0  1 0 0 0",
                          "x x x x  x x x x  o o o o  o o o o";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "efuse"
        size            = 1;
        write           = "1 0 1 0  1 1 0 0  1 0 1 0  0 1 0 0",
                          "x x x x  x x x x  x x x x  i i i i";

        read            = "0 1 0 1  0 0 0 0  0 0 0 0  1 0 0 0",
                          "x x x x  x x x x  o o o o  o o o o";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "lock"
        size            = 1;
        read            = "0 1 0 1  1 0 0 0   0 0 0 0  0 0 0 0",
                          "x x x x  x x x x   x x o o  o o o o";

        write           = "1 0 1 0  1 1 0 0   1 1 1 x  x x x x",
                          "x x x x  x x x x   1 1 i i  i i i i";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "calibration"
        size            = 1;
        read            = "0 0 1 1  1 0 0 0    x x x x  x x x x",
                          "0 0 0 0  0 0 0 0    o o o o  o o o o";
      ;

    memory "signature"
        size            = 3;
        read            = "0  0  1  1   0  0  0  0   x  x  x  x   x  x  x  x",
                          "x  x  x  x   x  x a1 a0   o  o  o  o   o  o  o  o";
      ;
  ;

#------------------------------------------------------------
# AT90USB646
#------------------------------------------------------------

part
    id               = "usb646";
    desc             = "AT90USB646";
    signature        = 0x1e 0x96 0x82;
    usbpid           = 0x2ff9;
    has_jtag         = yes;
#    stk500_devcode   = 0xB2;
#    avr910_devcode   = 0x43;
    chip_erase_delay = 9000;
    pagel            = 0xD7;
    bs2              = 0xA0;
    reset            = dedicated;
    pgm_enable       = "1 0 1 0  1 1 0 0    0 1 0 1  0 0 1 1",
                       "x x x x  x x x x    x x x x  x x x x";

    chip_erase       = "1 0 1 0  1 1 0 0    1 0 0 0  0 0 0 0",
                       "x x x x  x x x x    x x x x  x x x x";

    timeout		= 200;
    stabdelay		= 100;
    cmdexedelay		= 25;
    synchloops		= 32;
    bytedelay		= 0;
    pollindex		= 3;
    pollvalue		= 0x53;
    predelay		= 1;
    postdelay		= 1;
    pollmethod		= 1;

    pp_controlstack     =
        0x0E, 0x1E, 0x0F, 0x1F, 0x2E, 0x3E, 0x2F, 0x3F,
        0x4E, 0x5E, 0x4F, 0x5F, 0x6E, 0x7E, 0x6F, 0x7F,
        0x66, 0x76, 0x67, 0x77, 0x6A, 0x7A, 0x6B, 0x7B,
        0xBE, 0xFD, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00;
    hventerstabdelay    = 100;
    progmodedelay       = 0;
    latchcycles         = 5;
    togglevtg           = 1;
    poweroffdelay       = 15;
    resetdelayms        = 1;
    resetdelayus        = 0;
    hvleavestabdelay    = 15;
    chiperasepulsewidth = 0;
    chiperasepolltimeout = 10;
    programfusepulsewidth = 0;
    programfusepolltimeout = 5;
    programlockpulsewidth = 0;
    programlockpolltimeout = 5;

    idr                 = 0x31;
    spmcr               = 0x57;
    rampz               = 0x3b;
    allowfullpagebitstream = no;

    ocdrev              = 3;

    memory "eeprom"
        paged           = no; /* leave this "no" */
        page_size       = 8;  /* for parallel programming */
        size            = 2048;
        min_write_delay = 9000;
        max_write_delay = 9000;
        readback_p1     = 0x00;
        readback_p2     = 0x00;
        read            = "  1   0   1   0      0   0   0   0",
                          "  x   x   x   x      x a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

        write           = "  1   1   0   0      0   0   0   0",
                          "  x   x   x   x      x a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0", 
                          "  i   i   i   i      i   i   i   i";

	loadpage_lo	= "  1   1   0   0      0   0   0   1",
			  "  0   0   0   0      0   0   0   0",
			  "  0   0   0   0      0  a2  a1  a0",
			  "  i   i   i   i      i   i   i   i";

	writepage	= "  1   1   0   0      0   0   1   0",
			  "  0   0   x   x      x a10  a9  a8",
			  " a7  a6  a5  a4     a3   0   0   0",
			  "  x   x   x   x      x   x   x   x";

	mode		= 0x41;
	delay		= 10;
	blocksize	= 8;
	readsize	= 256;
      ;

    memory "flash"
        paged           = yes;
        size            = 65536;
        page_size       = 256;
        num_pages       = 256;
        min_write_delay = 4500;
        max_write_delay = 4500;
        readback_p1     = 0x00;
        readback_p2     = 0x00;
        read_lo         = "  0   0   1   0      0   0   0   0",
                          "  0 a14 a13 a12    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

        read_hi         = "  0   0   1   0      1   0   0   0",
                          "  0 a14 a13 a12    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

        loadpage_lo     = "  0   1   0   0      0   0   0   0",
                          "  x   x   x   x      x   x   x   x",
                          "  x  a6  a5  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

        loadpage_hi     = "  0   1   0   0      1   0   0   0",
                          "  x   x   x   x      x   x   x   x",
                          "  x  a6  a5  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

        writepage       = "  0   1   0   0      1   1   0   0",
                          "  0 a14 a13 a12    a11 a10  a9  a8",
                          " a7   x   x   x      x   x   x   x",
                          "  x   x   x   x      x   x   x   x";

	mode		= 0x41;
	delay		= 6;
	blocksize	= 256;
	readsize	= 256;
      ;

    memory "lfuse"
        size            = 1;
        write           = "1 0 1 0  1 1 0 0  1 0 1 0  0 0 0 0",
                          "x x x x  x x x x  i i i i  i i i i";

        read            = "0 1 0 1  0 0 0 0  0 0 0 0  0 0 0 0",
                          "x x x x  x x x x  o o o o  o o o o";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "hfuse"
        size            = 1;
        write           = "1 0 1 0  1 1 0 0  1 0 1 0  1 0 0 0",
                          "x x x x  x x x x  i i i i  i i i i";

        read            = "0 1 0 1  1 0 0 0  0 0 0 0  1 0 0 0",
                          "x x x x  x x x x  o o o o  o o o o";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "efuse"
        size            = 1;
        write           = "1 0 1 0  1 1 0 0  1 0 1 0  0 1 0 0",
                          "x x x x  x x x x  x x x x  i i i i";

        read            = "0 1 0 1  0 0 0 0  0 0 0 0  1 0 0 0",
                          "x x x x  x x x x  o o o o  o o o o";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "lock"
        size            = 1;
        read            = "0 1 0 1  1 0 0 0   0 0 0 0  0 0 0 0",
                          "x x x x  x x x x   x x o o  o o o o";

        write           = "1 0 1 0  1 1 0 0   1 1 1 x  x x x x",
                          "x x x x  x x x x   1 1 i i  i i i i";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "calibration"
        size            = 1;
        read            = "0 0 1 1  1 0 0 0    x x x x  x x x x",
                          "0 0 0 0  0 0 0 0    o o o o  o o o o";
      ;

    memory "signature"
        size            = 3;
        read            = "0  0  1  1   0  0  0  0   x  x  x  x   x  x  x  x",
                          "x  x  x  x   x  x a1 a0   o  o  o  o   o  o  o  o";
      ;
  ;

#------------------------------------------------------------
# AT90USB647
#------------------------------------------------------------
# identical to AT90USB646

part parent "usb646"
    id               = "usb647";
    desc             = "AT90USB647";
    signature        = 0x1e 0x96 0x82;

    ocdrev              = 3;
  ;

#------------------------------------------------------------
# AT90USB1286
#------------------------------------------------------------

part
    id               = "usb1286";
    desc             = "AT90USB1286";
    signature        = 0x1e 0x97 0x82;
    usbpid           = 0x2ffb;
    has_jtag         = yes;
#    stk500_devcode   = 0xB2;
#    avr910_devcode   = 0x43;
    chip_erase_delay = 9000;
    pagel            = 0xD7;
    bs2              = 0xA0;
    reset            = dedicated;
    pgm_enable       = "1 0 1 0  1 1 0 0    0 1 0 1  0 0 1 1",
                       "x x x x  x x x x    x x x x  x x x x";

    chip_erase       = "1 0 1 0  1 1 0 0    1 0 0 0  0 0 0 0",
                       "x x x x  x x x x    x x x x  x x x x";

    timeout		= 200;
    stabdelay		= 100;
    cmdexedelay		= 25;
    synchloops		= 32;
    bytedelay		= 0;
    pollindex		= 3;
    pollvalue		= 0x53;
    predelay		= 1;
    postdelay		= 1;
    pollmethod		= 1;

    pp_controlstack     =
        0x0E, 0x1E, 0x0F, 0x1F, 0x2E, 0x3E, 0x2F, 0x3F,
        0x4E, 0x5E, 0x4F, 0x5F, 0x6E, 0x7E, 0x6F, 0x7F,
        0x66, 0x76, 0x67, 0x77, 0x6A, 0x7A, 0x6B, 0x7B,
        0xBE, 0xFD, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00;
    hventerstabdelay    = 100;
    progmodedelay       = 0;
    latchcycles         = 5;
    togglevtg           = 1;
    poweroffdelay       = 15;
    resetdelayms        = 1;
    resetdelayus        = 0;
    hvleavestabdelay    = 15;
    chiperasepulsewidth = 0;
    chiperasepolltimeout = 10;
    programfusepulsewidth = 0;
    programfusepolltimeout = 5;
    programlockpulsewidth = 0;
    programlockpolltimeout = 5;

    idr                 = 0x31;
    spmcr               = 0x57;
    rampz               = 0x3b;
    allowfullpagebitstream = no;

    ocdrev              = 3;

    memory "eeprom"
        paged           = no; /* leave this "no" */
        page_size       = 8;  /* for parallel programming */
        size            = 4096;
        min_write_delay = 9000;
        max_write_delay = 9000;
        readback_p1     = 0x00;
        readback_p2     = 0x00;
        read            = "  1   0   1   0      0   0   0   0",
                          "  x   x   x   x    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

        write           = "  1   1   0   0      0   0   0   0",
                          "  x   x   x   x    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0", 
                          "  i   i   i   i      i   i   i   i";

	loadpage_lo	= "  1   1   0   0      0   0   0   1",
			  "  0   0   0   0      0   0   0   0",
			  "  0   0   0   0      0  a2  a1  a0",
			  "  i   i   i   i      i   i   i   i";

	writepage	= "  1   1   0   0      0   0   1   0",
			  "  0   0   x   x      x a10  a9  a8",
			  " a7  a6  a5  a4     a3   0   0   0",
			  "  x   x   x   x      x   x   x   x";

	mode		= 0x41;
	delay		= 10;
	blocksize	= 8;
	readsize	= 256;
      ;

    memory "flash"
        paged           = yes;
        size            = 131072;
        page_size       = 256;
        num_pages       = 512;
        min_write_delay = 4500;
        max_write_delay = 4500;
        readback_p1     = 0x00;
        readback_p2     = 0x00;
        read_lo         = "  0   0   1   0      0   0   0   0",
                          "a15 a14 a13 a12    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

        read_hi         = "  0   0   1   0      1   0   0   0",
                          "a15 a14 a13 a12    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

        loadpage_lo     = "  0   1   0   0      0   0   0   0",
                          "  x   x   x   x      x   x   x   x",
                          "  x  a6  a5  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

        loadpage_hi     = "  0   1   0   0      1   0   0   0",
                          "  x   x   x   x      x   x   x   x",
                          "  x  a6  a5  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

        writepage       = "  0   1   0   0      1   1   0   0",
                          "a15 a14 a13 a12    a11 a10  a9  a8",
                          " a7   x   x   x      x   x   x   x",
                          "  x   x   x   x      x   x   x   x";

	mode		= 0x41;
	delay		= 6;
	blocksize	= 256;
	readsize	= 256;
      ;

    memory "lfuse"
        size            = 1;
        write           = "1 0 1 0  1 1 0 0  1 0 1 0  0 0 0 0",
                          "x x x x  x x x x  i i i i  i i i i";

        read            = "0 1 0 1  0 0 0 0  0 0 0 0  0 0 0 0",
                          "x x x x  x x x x  o o o o  o o o o";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "hfuse"
        size            = 1;
        write           = "1 0 1 0  1 1 0 0  1 0 1 0  1 0 0 0",
                          "x x x x  x x x x  i i i i  i i i i";

        read            = "0 1 0 1  1 0 0 0  0 0 0 0  1 0 0 0",
                          "x x x x  x x x x  o o o o  o o o o";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "efuse"
        size            = 1;
        write           = "1 0 1 0  1 1 0 0  1 0 1 0  0 1 0 0",
                          "x x x x  x x x x  x x x x  i i i i";

        read            = "0 1 0 1  0 0 0 0  0 0 0 0  1 0 0 0",
                          "x x x x  x x x x  o o o o  o o o o";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "lock"
        size            = 1;
        read            = "0 1 0 1  1 0 0 0   0 0 0 0  0 0 0 0",
                          "x x x x  x x x x   x x o o  o o o o";

        write           = "1 0 1 0  1 1 0 0   1 1 1 x  x x x x",
                          "x x x x  x x x x   1 1 i i  i i i i";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "calibration"
        size            = 1;
        read            = "0 0 1 1  1 0 0 0    x x x x  x x x x",
                          "0 0 0 0  0 0 0 0    o o o o  o o o o";
      ;

    memory "signature"
        size            = 3;
        read            = "0  0  1  1   0  0  0  0   x  x  x  x   x  x  x  x",
                          "x  x  x  x   x  x a1 a0   o  o  o  o   o  o  o  o";
      ;
  ;

#------------------------------------------------------------
# AT90USB1287
#------------------------------------------------------------
# identical to AT90USB1286

part parent "usb1286"
    id               = "usb1287";
    desc             = "AT90USB1287";
    signature        = 0x1e 0x97 0x82;

    ocdrev              = 3;
  ;

#------------------------------------------------------------
# AT90USB162
#------------------------------------------------------------

part
    id               = "usb162";
    desc             = "AT90USB162";
    has_jtag         = no;
    has_debugwire    = yes;
    signature        = 0x1e 0x94 0x82;
    usbpid           = 0x2ffa;
    chip_erase_delay = 9000;
    reset            = io;
    pgm_enable       = "1 0 1 0  1 1 0 0    0 1 0 1  0 0 1 1",
                       "x x x x  x x x x    x x x x  x x x x";
    chip_erase       = "1 0 1 0  1 1 0 0    1 0 0 x  x x x x",
                       "x x x x  x x x x    x x x x  x x x x";
    pagel            = 0xD7;
    bs2              = 0xC6;

    timeout		= 200;
    stabdelay		= 100;
    cmdexedelay		= 25;
    synchloops		= 32;
    bytedelay		= 0;
    pollindex		= 3;
    pollvalue		= 0x53;
    predelay		= 1;
    postdelay		= 1;
    pollmethod		= 1;
    pp_controlstack     =
        0x0E, 0x1E, 0x0F, 0x1F, 0x2E, 0x3E, 0x2F, 0x3F,
        0x4E, 0x5E, 0x4F, 0x5F, 0x6E, 0x7E, 0x6F, 0x7F,
        0x66, 0x76, 0x67, 0x77, 0x6A, 0x7A, 0x6B, 0x7B,
        0xBE, 0xFD, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00;
    hventerstabdelay    = 100;
    progmodedelay       = 0;
    latchcycles         = 5;
    togglevtg           = 1;
    poweroffdelay       = 15;
    resetdelayms        = 1;
    resetdelayus        = 0;
    hvleavestabdelay    = 15;
    chiperasepulsewidth = 0;
    chiperasepolltimeout = 10;
    programfusepulsewidth = 0;
    programfusepolltimeout = 5;
    programlockpulsewidth = 0;
    programlockpolltimeout = 5;

    ocdrev              = 1;

    memory "eeprom"
        paged           = no; /* leave this "no" */
        page_size       = 4;  /* for parallel programming */
        size            = 512;
        num_pages       = 128;
        min_write_delay = 9000;
        max_write_delay = 9000;
        readback_p1     = 0x00;
        readback_p2     = 0x00;
        read            = "  1   0   1   0      0   0   0   0",
                          "  0   0   0   0    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

        write           = "  1   1   0   0      0   0   0   0",
                          "  0   0   0   0    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

	loadpage_lo	= "  1   1   0   0      0   0   0   1",
			  "  0   0   0   0      0   0   0   0",
			  "  0   0   0   0      0   0  a1  a0",
			  "  i   i   i   i      i   i   i   i";

	writepage	= "  1   1   0   0      0   0   1   0",
                          "  0   0   0   0    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2   0   0",
			  "  x   x   x   x      x   x   x   x";

	mode		= 0x41;
	delay		= 20;
	blocksize	= 4;
	readsize	= 256;
      ;

    memory "flash"
        paged           = yes;
        size            = 16384;
        page_size       = 128;
        num_pages       = 128;
        min_write_delay = 4500;
        max_write_delay = 4500;
        readback_p1     = 0x00;
        readback_p2     = 0x00;
        read_lo         = "  0   0   1   0      0   0   0   0",
                          "a15 a14 a13 a12    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

        read_hi         = "  0   0   1   0      1   0   0   0",
                          "a15 a14 a13 a12    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

        loadpage_lo     = "  0   1   0   0      0   0   0   0",
                          "  x   x   x   x      x   x   x   x",
                          "  x   x  a5  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

        loadpage_hi     = "  0   1   0   0      1   0   0   0",
                          "  x   x   x   x      x   x   x   x",
                          "  x   x  a5  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

        writepage       = "  0   1   0   0      1   1   0   0",
                          "a15 a14 a13 a12    a11 a10  a9  a8",
                          " a7  a6   x   x      x   x   x   x",
                          "  x   x   x   x      x   x   x   x";

	mode		= 0x41;
	delay		= 6;
	blocksize	= 128;
	readsize	= 256;
      ;

    memory "lfuse"
        size            = 1;
        write           = "1 0 1 0  1 1 0 0  1 0 1 0  0 0 0 0",
                          "x x x x  x x x x  i i i i  i i i i";

        read            = "0 1 0 1  0 0 0 0  0 0 0 0  0 0 0 0",
                          "x x x x  x x x x  o o o o  o o o o";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "hfuse"
        size            = 1;
        write           = "1 0 1 0  1 1 0 0  1 0 1 0  1 0 0 0",
                          "x x x x  x x x x  i i i i  i i i i";

        read            = "0 1 0 1  1 0 0 0  0 0 0 0  1 0 0 0",
                          "x x x x  x x x x  o o o o  o o o o";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "efuse"
        size            = 1;
        write           = "1 0 1 0  1 1 0 0  1 0 1 0  0 1 0 0",
                          "x x x x  x x x x  i i i i  i i i i";

        read            = "0 1 0 1  0 0 0 0  0 0 0 0  1 0 0 0",
                          "x x x x  x x x x  o o o o  o o o o";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "lock"
        size            = 1;
        read            = "0 1 0 1  1 0 0 0   0 0 0 0  0 0 0 0",
                          "x x x x  x x x x   x x o o  o o o o";

        write           = "1 0 1 0  1 1 0 0   1 1 1 x  x x x x",
                          "x x x x  x x x x   1 1 i i  i i i i";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "calibration"
        size            = 1;
        read            = "0 0 1 1  1 0 0 0    0 0 0 x  x x x x",
                          "0 0 0 0  0 0 0 0    o o o o  o o o o";
      ;
    memory "signature"
        size            = 3;
        read            = "0  0  1  1   0  0  0  0   0  0  0  x   x  x  x  x",
                          "x  x  x  x   x  x a1 a0   o  o  o  o   o  o  o  o";
      ;
  ;

#------------------------------------------------------------
# AT90USB82
#------------------------------------------------------------
# Changes against AT90USB162 (beside IDs)
#    memory "flash"
#        size            = 8192;
#        num_pages       = 64;

part
    id               = "usb82";
    desc             = "AT90USB82";
    has_jtag         = no;
    has_debugwire    = yes;
    signature        = 0x1e 0x93 0x82;
    usbpid           = 0x2ff7;
    chip_erase_delay = 9000;
    reset            = io;
    pgm_enable       = "1 0 1 0  1 1 0 0    0 1 0 1  0 0 1 1",
                       "x x x x  x x x x    x x x x  x x x x";
    chip_erase       = "1 0 1 0  1 1 0 0    1 0 0 x  x x x x",
                       "x x x x  x x x x    x x x x  x x x x";
    pagel            = 0xD7;
    bs2              = 0xC6;

    timeout		= 200;
    stabdelay		= 100;
    cmdexedelay		= 25;
    synchloops		= 32;
    bytedelay		= 0;
    pollindex		= 3;
    pollvalue		= 0x53;
    predelay		= 1;
    postdelay		= 1;
    pollmethod		= 1;
    pp_controlstack     =
        0x0E, 0x1E, 0x0F, 0x1F, 0x2E, 0x3E, 0x2F, 0x3F,
        0x4E, 0x5E, 0x4F, 0x5F, 0x6E, 0x7E, 0x6F, 0x7F,
        0x66, 0x76, 0x67, 0x77, 0x6A, 0x7A, 0x6B, 0x7B,
        0xBE, 0xFD, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00;
    hventerstabdelay    = 100;
    progmodedelay       = 0;
    latchcycles         = 5;
    togglevtg           = 1;
    poweroffdelay       = 15;
    resetdelayms        = 1;
    resetdelayus        = 0;
    hvleavestabdelay    = 15;
    chiperasepulsewidth = 0;
    chiperasepolltimeout = 10;
    programfusepulsewidth = 0;
    programfusepolltimeout = 5;
    programlockpulsewidth = 0;
    programlockpolltimeout = 5;

    ocdrev              = 1;

    memory "eeprom"
        paged           = no; /* leave this "no" */
        page_size       = 4;  /* for parallel programming */
        size            = 512;
        num_pages       = 128;
        min_write_delay = 9000;
        max_write_delay = 9000;
        readback_p1     = 0x00;
        readback_p2     = 0x00;
        read            = "  1   0   1   0      0   0   0   0",
                          "  0   0   0   0    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

        write           = "  1   1   0   0      0   0   0   0",
                          "  0   0   0   0    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

	loadpage_lo	= "  1   1   0   0      0   0   0   1",
			  "  0   0   0   0      0   0   0   0",
			  "  0   0   0   0      0   0  a1  a0",
			  "  i   i   i   i      i   i   i   i";

	writepage	= "  1   1   0   0      0   0   1   0",
                          "  0   0   0   0    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2   0   0",
			  "  x   x   x   x      x   x   x   x";

	mode		= 0x41;
	delay		= 20;
	blocksize	= 4;
	readsize	= 256;
      ;

    memory "flash"
        paged           = yes;
        size            = 8192;
        page_size       = 128;
        num_pages       = 64;
        min_write_delay = 4500;
        max_write_delay = 4500;
        readback_p1     = 0x00;
        readback_p2     = 0x00;
        read_lo         = "  0   0   1   0      0   0   0   0",
                          "a15 a14 a13 a12    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

        read_hi         = "  0   0   1   0      1   0   0   0",
                          "a15 a14 a13 a12    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

        loadpage_lo     = "  0   1   0   0      0   0   0   0",
                          "  x   x   x   x      x   x   x   x",
                          "  x   x  a5  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

        loadpage_hi     = "  0   1   0   0      1   0   0   0",
                          "  x   x   x   x      x   x   x   x",
                          "  x   x  a5  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

        writepage       = "  0   1   0   0      1   1   0   0",
                          "a15 a14 a13 a12    a11 a10  a9  a8",
                          " a7  a6   x   x      x   x   x   x",
                          "  x   x   x   x      x   x   x   x";

	mode		= 0x41;
	delay		= 6;
	blocksize	= 128;
	readsize	= 256;
      ;

    memory "lfuse"
        size            = 1;
        write           = "1 0 1 0  1 1 0 0  1 0 1 0  0 0 0 0",
                          "x x x x  x x x x  i i i i  i i i i";

        read            = "0 1 0 1  0 0 0 0  0 0 0 0  0 0 0 0",
                          "x x x x  x x x x  o o o o  o o o o";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "hfuse"
        size            = 1;
        write           = "1 0 1 0  1 1 0 0  1 0 1 0  1 0 0 0",
                          "x x x x  x x x x  i i i i  i i i i";

        read            = "0 1 0 1  1 0 0 0  0 0 0 0  1 0 0 0",
                          "x x x x  x x x x  o o o o  o o o o";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "efuse"
        size            = 1;
        write           = "1 0 1 0  1 1 0 0  1 0 1 0  0 1 0 0",
                          "x x x x  x x x x  i i i i  i i i i";

        read            = "0 1 0 1  0 0 0 0  0 0 0 0  1 0 0 0",
                          "x x x x  x x x x  o o o o  o o o o";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "lock"
        size            = 1;
        read            = "0 1 0 1  1 0 0 0   0 0 0 0  0 0 0 0",
                          "x x x x  x x x x   x x o o  o o o o";

        write           = "1 0 1 0  1 1 0 0   1 1 1 x  x x x x",
                          "x x x x  x x x x   1 1 i i  i i i i";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "calibration"
        size            = 1;
        read            = "0 0 1 1  1 0 0 0    0 0 0 x  x x x x",
                          "0 0 0 0  0 0 0 0    o o o o  o o o o";
      ;
    memory "signature"
        size            = 3;
        read            = "0  0  1  1   0  0  0  0   0  0  0  x   x  x  x  x",
                          "x  x  x  x   x  x a1 a0   o  o  o  o   o  o  o  o";
      ;
  ;

#------------------------------------------------------------
# ATmega32U2
#------------------------------------------------------------
# Changes against AT90USB162 (beside IDs)
#    memory "flash"
#        size            = 32768;
#        num_pages       = 256;
#    memory "eeprom"
#        size            = 1024;
#        num_pages       = 256;
part
    id               = "m32u2";
    desc             = "ATmega32U2";
    has_jtag         = no;
    has_debugwire    = yes;
    signature        = 0x1e 0x95 0x8a;
    usbpid           = 0x2ff0;
    chip_erase_delay = 9000;
    reset            = io;
    pgm_enable       = "1 0 1 0  1 1 0 0    0 1 0 1  0 0 1 1",
                       "x x x x  x x x x    x x x x  x x x x";
    chip_erase       = "1 0 1 0  1 1 0 0    1 0 0 x  x x x x",
                       "x x x x  x x x x    x x x x  x x x x";
    pagel            = 0xD7;
    bs2              = 0xC6;

    timeout		= 200;
    stabdelay		= 100;
    cmdexedelay		= 25;
    synchloops		= 32;
    bytedelay		= 0;
    pollindex		= 3;
    pollvalue		= 0x53;
    predelay		= 1;
    postdelay		= 1;
    pollmethod		= 1;
    pp_controlstack     =
        0x0E, 0x1E, 0x0F, 0x1F, 0x2E, 0x3E, 0x2F, 0x3F,
        0x4E, 0x5E, 0x4F, 0x5F, 0x6E, 0x7E, 0x6F, 0x7F,
        0x66, 0x76, 0x67, 0x77, 0x6A, 0x7A, 0x6B, 0x7B,
        0xBE, 0xFD, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00;
    hventerstabdelay    = 100;
    progmodedelay       = 0;
    latchcycles         = 5;
    togglevtg           = 1;
    poweroffdelay       = 15;
    resetdelayms        = 1;
    resetdelayus        = 0;
    hvleavestabdelay    = 15;
    chiperasepulsewidth = 0;
    chiperasepolltimeout = 10;
    programfusepulsewidth = 0;
    programfusepolltimeout = 5;
    programlockpulsewidth = 0;
    programlockpolltimeout = 5;

    ocdrev              = 1;

    memory "eeprom"
        paged           = no; /* leave this "no" */
        page_size       = 4;  /* for parallel programming */
        size            = 1024;
        num_pages       = 256;
        min_write_delay = 9000;
        max_write_delay = 9000;
        readback_p1     = 0x00;
        readback_p2     = 0x00;
        read            = "  1   0   1   0      0   0   0   0",
                          "  0   0   0   0    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

        write           = "  1   1   0   0      0   0   0   0",
                          "  0   0   0   0    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

	loadpage_lo	= "  1   1   0   0      0   0   0   1",
			  "  0   0   0   0      0   0   0   0",
			  "  0   0   0   0      0   0  a1  a0",
			  "  i   i   i   i      i   i   i   i";

	writepage	= "  1   1   0   0      0   0   1   0",
                          "  0   0   0   0    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2   0   0",
			  "  x   x   x   x      x   x   x   x";

	mode		= 0x41;
	delay		= 20;
	blocksize	= 4;
	readsize	= 256;
      ;

    memory "flash"
        paged           = yes;
        size            = 32768;
        page_size       = 128;
        num_pages       = 256;
        min_write_delay = 4500;
        max_write_delay = 4500;
        readback_p1     = 0x00;
        readback_p2     = 0x00;
        read_lo         = "  0   0   1   0      0   0   0   0",
                          "a15 a14 a13 a12    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

        read_hi         = "  0   0   1   0      1   0   0   0",
                          "a15 a14 a13 a12    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

        loadpage_lo     = "  0   1   0   0      0   0   0   0",
                          "  x   x   x   x      x   x   x   x",
                          "  x   x  a5  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

        loadpage_hi     = "  0   1   0   0      1   0   0   0",
                          "  x   x   x   x      x   x   x   x",
                          "  x   x  a5  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

        writepage       = "  0   1   0   0      1   1   0   0",
                          "a15 a14 a13 a12    a11 a10  a9  a8",
                          " a7  a6   x   x      x   x   x   x",
                          "  x   x   x   x      x   x   x   x";

	mode		= 0x41;
	delay		= 6;
	blocksize	= 128;
	readsize	= 256;
      ;

    memory "lfuse"
        size            = 1;
        write           = "1 0 1 0  1 1 0 0  1 0 1 0  0 0 0 0",
                          "x x x x  x x x x  i i i i  i i i i";

        read            = "0 1 0 1  0 0 0 0  0 0 0 0  0 0 0 0",
                          "x x x x  x x x x  o o o o  o o o o";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "hfuse"
        size            = 1;
        write           = "1 0 1 0  1 1 0 0  1 0 1 0  1 0 0 0",
                          "x x x x  x x x x  i i i i  i i i i";

        read            = "0 1 0 1  1 0 0 0  0 0 0 0  1 0 0 0",
                          "x x x x  x x x x  o o o o  o o o o";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "efuse"
        size            = 1;
        write           = "1 0 1 0  1 1 0 0  1 0 1 0  0 1 0 0",
                          "x x x x  x x x x  i i i i  i i i i";

        read            = "0 1 0 1  0 0 0 0  0 0 0 0  1 0 0 0",
                          "x x x x  x x x x  o o o o  o o o o";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "lock"
        size            = 1;
        read            = "0 1 0 1  1 0 0 0   0 0 0 0  0 0 0 0",
                          "x x x x  x x x x   x x o o  o o o o";

        write           = "1 0 1 0  1 1 0 0   1 1 1 x  x x x x",
                          "x x x x  x x x x   1 1 i i  i i i i";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "calibration"
        size            = 1;
        read            = "0 0 1 1  1 0 0 0    0 0 0 x  x x x x",
                          "0 0 0 0  0 0 0 0    o o o o  o o o o";
      ;
    memory "signature"
        size            = 3;
        read            = "0  0  1  1   0  0  0  0   0  0  0  x   x  x  x  x",
                          "x  x  x  x   x  x a1 a0   o  o  o  o   o  o  o  o";
      ;
  ;
#------------------------------------------------------------
# ATmega16U2
#------------------------------------------------------------
# Changes against ATmega32U2 (beside IDs)
#    memory "flash"
#        size            = 16384;
#        num_pages       = 128;
#    memory "eeprom"
#        size            = 512;
#        num_pages       = 128;
part
    id               = "m16u2";
    desc             = "ATmega16U2";
    has_jtag         = no;
    has_debugwire    = yes;
    signature        = 0x1e 0x94 0x89;
    usbpid           = 0x2fef;
    chip_erase_delay = 9000;
    reset            = io;
    pgm_enable       = "1 0 1 0  1 1 0 0    0 1 0 1  0 0 1 1",
                       "x x x x  x x x x    x x x x  x x x x";
    chip_erase       = "1 0 1 0  1 1 0 0    1 0 0 x  x x x x",
                       "x x x x  x x x x    x x x x  x x x x";
    pagel            = 0xD7;
    bs2              = 0xC6;

    timeout		= 200;
    stabdelay		= 100;
    cmdexedelay		= 25;
    synchloops		= 32;
    bytedelay		= 0;
    pollindex		= 3;
    pollvalue		= 0x53;
    predelay		= 1;
    postdelay		= 1;
    pollmethod		= 1;
    pp_controlstack     =
        0x0E, 0x1E, 0x0F, 0x1F, 0x2E, 0x3E, 0x2F, 0x3F,
        0x4E, 0x5E, 0x4F, 0x5F, 0x6E, 0x7E, 0x6F, 0x7F,
        0x66, 0x76, 0x67, 0x77, 0x6A, 0x7A, 0x6B, 0x7B,
        0xBE, 0xFD, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00;
    hventerstabdelay    = 100;
    progmodedelay       = 0;
    latchcycles         = 5;
    togglevtg           = 1;
    poweroffdelay       = 15;
    resetdelayms        = 1;
    resetdelayus        = 0;
    hvleavestabdelay    = 15;
    chiperasepulsewidth = 0;
    chiperasepolltimeout = 10;
    programfusepulsewidth = 0;
    programfusepolltimeout = 5;
    programlockpulsewidth = 0;
    programlockpolltimeout = 5;

    ocdrev              = 1;

    memory "eeprom"
        paged           = no; /* leave this "no" */
        page_size       = 4;  /* for parallel programming */
        size            = 512;
        num_pages       = 128;
        min_write_delay = 9000;
        max_write_delay = 9000;
        readback_p1     = 0x00;
        readback_p2     = 0x00;
        read            = "  1   0   1   0      0   0   0   0",
                          "  0   0   0   0    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

        write           = "  1   1   0   0      0   0   0   0",
                          "  0   0   0   0    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

	loadpage_lo	= "  1   1   0   0      0   0   0   1",
			  "  0   0   0   0      0   0   0   0",
			  "  0   0   0   0      0   0  a1  a0",
			  "  i   i   i   i      i   i   i   i";

	writepage	= "  1   1   0   0      0   0   1   0",
                          "  0   0   0   0    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2   0   0",
			  "  x   x   x   x      x   x   x   x";

	mode		= 0x41;
	delay		= 20;
	blocksize	= 4;
	readsize	= 256;
      ;

    memory "flash"
        paged           = yes;
        size            = 16384;
        page_size       = 128;
        num_pages       = 128;
        min_write_delay = 4500;
        max_write_delay = 4500;
        readback_p1     = 0x00;
        readback_p2     = 0x00;
        read_lo         = "  0   0   1   0      0   0   0   0",
                          "a15 a14 a13 a12    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

        read_hi         = "  0   0   1   0      1   0   0   0",
                          "a15 a14 a13 a12    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

        loadpage_lo     = "  0   1   0   0      0   0   0   0",
                          "  x   x   x   x      x   x   x   x",
                          "  x   x  a5  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

        loadpage_hi     = "  0   1   0   0      1   0   0   0",
                          "  x   x   x   x      x   x   x   x",
                          "  x   x  a5  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

        writepage       = "  0   1   0   0      1   1   0   0",
                          "a15 a14 a13 a12    a11 a10  a9  a8",
                          " a7  a6   x   x      x   x   x   x",
                          "  x   x   x   x      x   x   x   x";

	mode		= 0x41;
	delay		= 6;
	blocksize	= 128;
	readsize	= 256;
      ;

    memory "lfuse"
        size            = 1;
        write           = "1 0 1 0  1 1 0 0  1 0 1 0  0 0 0 0",
                          "x x x x  x x x x  i i i i  i i i i";

        read            = "0 1 0 1  0 0 0 0  0 0 0 0  0 0 0 0",
                          "x x x x  x x x x  o o o o  o o o o";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "hfuse"
        size            = 1;
        write           = "1 0 1 0  1 1 0 0  1 0 1 0  1 0 0 0",
                          "x x x x  x x x x  i i i i  i i i i";

        read            = "0 1 0 1  1 0 0 0  0 0 0 0  1 0 0 0",
                          "x x x x  x x x x  o o o o  o o o o";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "efuse"
        size            = 1;
        write           = "1 0 1 0  1 1 0 0  1 0 1 0  0 1 0 0",
                          "x x x x  x x x x  i i i i  i i i i";

        read            = "0 1 0 1  0 0 0 0  0 0 0 0  1 0 0 0",
                          "x x x x  x x x x  o o o o  o o o o";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "lock"
        size            = 1;
        read            = "0 1 0 1  1 0 0 0   0 0 0 0  0 0 0 0",
                          "x x x x  x x x x   x x o o  o o o o";

        write           = "1 0 1 0  1 1 0 0   1 1 1 x  x x x x",
                          "x x x x  x x x x   1 1 i i  i i i i";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "calibration"
        size            = 1;
        read            = "0 0 1 1  1 0 0 0    0 0 0 x  x x x x",
                          "0 0 0 0  0 0 0 0    o o o o  o o o o";
      ;
    memory "signature"
        size            = 3;
        read            = "0  0  1  1   0  0  0  0   0  0  0  x   x  x  x  x",
                          "x  x  x  x   x  x a1 a0   o  o  o  o   o  o  o  o";
      ;
  ;

#------------------------------------------------------------
# ATmega8U2
#------------------------------------------------------------
# Changes against ATmega16U2 (beside IDs)
#    memory "flash"
#        size            = 8192;
#        page_size       = 64;
#        blocksize       = 64;

part
    id               = "m8u2";
    desc             = "ATmega8U2";
    has_jtag         = no;
    has_debugwire    = yes;
    signature        = 0x1e 0x93 0x89;
    usbpid           = 0x2fee;
    chip_erase_delay = 9000;
    reset            = io;
    pgm_enable       = "1 0 1 0  1 1 0 0    0 1 0 1  0 0 1 1",
                       "x x x x  x x x x    x x x x  x x x x";
    chip_erase       = "1 0 1 0  1 1 0 0    1 0 0 x  x x x x",
                       "x x x x  x x x x    x x x x  x x x x";
    pagel            = 0xD7;
    bs2              = 0xC6;

    timeout		= 200;
    stabdelay		= 100;
    cmdexedelay		= 25;
    synchloops		= 32;
    bytedelay		= 0;
    pollindex		= 3;
    pollvalue		= 0x53;
    predelay		= 1;
    postdelay		= 1;
    pollmethod		= 1;
    pp_controlstack     =
        0x0E, 0x1E, 0x0F, 0x1F, 0x2E, 0x3E, 0x2F, 0x3F,
        0x4E, 0x5E, 0x4F, 0x5F, 0x6E, 0x7E, 0x6F, 0x7F,
        0x66, 0x76, 0x67, 0x77, 0x6A, 0x7A, 0x6B, 0x7B,
        0xBE, 0xFD, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00;
    hventerstabdelay    = 100;
    progmodedelay       = 0;
    latchcycles         = 5;
    togglevtg           = 1;
    poweroffdelay       = 15;
    resetdelayms        = 1;
    resetdelayus        = 0;
    hvleavestabdelay    = 15;
    chiperasepulsewidth = 0;
    chiperasepolltimeout = 10;
    programfusepulsewidth = 0;
    programfusepolltimeout = 5;
    programlockpulsewidth = 0;
    programlockpolltimeout = 5;

    ocdrev              = 1;

    memory "eeprom"
        paged           = no; /* leave this "no" */
        page_size       = 4;  /* for parallel programming */
        size            = 512;
        num_pages       = 128;
        min_write_delay = 9000;
        max_write_delay = 9000;
        readback_p1     = 0x00;
        readback_p2     = 0x00;
        read            = "  1   0   1   0      0   0   0   0",
                          "  0   0   0   0    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

        write           = "  1   1   0   0      0   0   0   0",
                          "  0   0   0   0    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

	loadpage_lo	= "  1   1   0   0      0   0   0   1",
			  "  0   0   0   0      0   0   0   0",
			  "  0   0   0   0      0   0  a1  a0",
			  "  i   i   i   i      i   i   i   i";

	writepage	= "  1   1   0   0      0   0   1   0",
                          "  0   0   0   0    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2   0   0",
			  "  x   x   x   x      x   x   x   x";

	mode		= 0x41;
	delay		= 20;
	blocksize	= 4;
	readsize	= 256;
      ;

    memory "flash"
        paged           = yes;
        size            = 8192;
        page_size       = 128;
        num_pages       = 64;
        min_write_delay = 4500;
        max_write_delay = 4500;
        readback_p1     = 0x00;
        readback_p2     = 0x00;
        read_lo         = "  0   0   1   0      0   0   0   0",
                          "a15 a14 a13 a12    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

        read_hi         = "  0   0   1   0      1   0   0   0",
                          "a15 a14 a13 a12    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

        loadpage_lo     = "  0   1   0   0      0   0   0   0",
                          "  x   x   x   x      x   x   x   x",
                          "  x   x  a5  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

        loadpage_hi     = "  0   1   0   0      1   0   0   0",
                          "  x   x   x   x      x   x   x   x",
                          "  x   x  a5  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

        writepage       = "  0   1   0   0      1   1   0   0",
                          "a15 a14 a13 a12    a11 a10  a9  a8",
                          " a7  a6   x   x      x   x   x   x",
                          "  x   x   x   x      x   x   x   x";

	mode		= 0x41;
	delay		= 6;
	blocksize	= 128;
	readsize	= 256;
      ;

    memory "lfuse"
        size            = 1;
        write           = "1 0 1 0  1 1 0 0  1 0 1 0  0 0 0 0",
                          "x x x x  x x x x  i i i i  i i i i";

        read            = "0 1 0 1  0 0 0 0  0 0 0 0  0 0 0 0",
                          "x x x x  x x x x  o o o o  o o o o";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "hfuse"
        size            = 1;
        write           = "1 0 1 0  1 1 0 0  1 0 1 0  1 0 0 0",
                          "x x x x  x x x x  i i i i  i i i i";

        read            = "0 1 0 1  1 0 0 0  0 0 0 0  1 0 0 0",
                          "x x x x  x x x x  o o o o  o o o o";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "efuse"
        size            = 1;
        write           = "1 0 1 0  1 1 0 0  1 0 1 0  0 1 0 0",
                          "x x x x  x x x x  i i i i  i i i i";

        read            = "0 1 0 1  0 0 0 0  0 0 0 0  1 0 0 0",
                          "x x x x  x x x x  o o o o  o o o o";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "lock"
        size            = 1;
        read            = "0 1 0 1  1 0 0 0   0 0 0 0  0 0 0 0",
                          "x x x x  x x x x   x x o o  o o o o";

        write           = "1 0 1 0  1 1 0 0   1 1 1 x  x x x x",
                          "x x x x  x x x x   1 1 i i  i i i i";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "calibration"
        size            = 1;
        read            = "0 0 1 1  1 0 0 0    0 0 0 x  x x x x",
                          "0 0 0 0  0 0 0 0    o o o o  o o o o";
      ;
    memory "signature"
        size            = 3;
        read            = "0  0  1  1   0  0  0  0   0  0  0  x   x  x  x  x",
                          "x  x  x  x   x  x a1 a0   o  o  o  o   o  o  o  o";
      ;
  ;
#------------------------------------------------------------
# ATmega325
#------------------------------------------------------------

part
    id               = "m325";
    desc             = "ATmega325";
    signature        = 0x1e 0x95 0x05;
    has_jtag         = yes;
#   stk500_devcode   = 0x??; # No STK500v1 support?
#   avr910_devcode   = 0x??; # Try the ATmega16 one
    avr910_devcode   = 0x74;
    pagel            = 0xd7;
    bs2              = 0xa0;
    chip_erase_delay = 9000;
    pgm_enable       = "1 0 1 0  1 1 0 0    0 1 0 1  0 0 1 1",
                       "0 0 0 0  0 0 0 0    0 0 0 0  0 0 0 0";

    chip_erase       = "1 0 1 0  1 1 0 0    1 0 0 0  0 0 0 0",
                       "0 0 0 0  0 0 0 0    0 0 0 0  0 0 0 0";

    timeout             = 200;
    stabdelay           = 100;
    cmdexedelay         = 25;
    synchloops          = 32;
    bytedelay           = 0;
    pollindex           = 3;
    pollvalue           = 0x53;
    predelay            = 1;
    postdelay           = 1;
    pollmethod          = 1;

    pp_controlstack     =
        0x0E, 0x1E, 0x0F, 0x1F, 0x2E, 0x3E, 0x2F, 0x3F,
        0x4E, 0x5E, 0x4F, 0x5F, 0x6E, 0x7E, 0x6F, 0x7F,
        0x66, 0x76, 0x67, 0x77, 0x6A, 0x7A, 0x6B, 0x7B,
        0xBE, 0xFD, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00;
    hventerstabdelay    = 100;
    progmodedelay       = 0;
    latchcycles         = 5;
    togglevtg           = 1;
    poweroffdelay       = 15;
    resetdelayms        = 1;
    resetdelayus        = 0;
    hvleavestabdelay    = 15;
    chiperasepulsewidth = 0;
    chiperasepolltimeout = 10;
    programfusepulsewidth = 0;
    programfusepolltimeout = 5;
    programlockpulsewidth = 0;
    programlockpolltimeout = 5;

    idr                 = 0x31;
    spmcr               = 0x57;
    allowfullpagebitstream = no;

    ocdrev              = 3;

    memory "eeprom"
        paged           = no; /* leave this "no" */
        page_size       = 4;  /* for parallel programming */
        size            = 1024;
        min_write_delay = 9000;
        max_write_delay = 9000;
        readback_p1     = 0xff;
        readback_p2     = 0xff;
        read            = "  1   0   1   0      0   0   0   0",
                          "  0   0   0   0      0   0  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

        write           = "  1   1   0   0      0   0   0   0",
                          "  0   0   0   0      0   0  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

        loadpage_lo     = "  1   1   0   0      0   0   0   1",
                          "  0   0   0   0      0   0   0   0",
                          "  0   0   0   0      0   0  a1  a0",
                          "  i   i   i   i      i   i   i   i";

        writepage       = "  1   1   0   0      0   0   1   0",
                          "  0   0   0   0      0   0  a9  a8",
                          " a7  a6  a5  a4     a3  a2   0   0",
                          "  x   x   x   x      x   x   x   x";

        mode            = 0x41;
        delay           = 10;
        blocksize       = 4;
        readsize        = 256;
      ;

    memory "flash"
        paged           = yes;
        size            = 32768;
        page_size       = 128;
        num_pages       = 256;
        min_write_delay = 4500;
        max_write_delay = 4500;
        readback_p1     = 0xff;
        readback_p2     = 0xff;
        read_lo         = "  0   0   1   0      0   0   0   0",
                          "  0 a14 a13 a12    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

        read_hi         = "  0   0   1   0      1   0   0   0",
                          "  0 a14 a13 a12    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

        loadpage_lo     = "  0   1   0   0      0   0   0   0",
                          "  0   0   0   0      0   0   0   0",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

        loadpage_hi     = "  0   1   0   0      1   0   0   0",
                          "  0   0   0   0      0   0   0   0",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

        writepage       = "  0   1   0   0      1   1   0   0",
                          "  0 a14 a13 a12    a11 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  x   x   x   x      x   x   x   x";

        mode            = 0x41;
        delay           = 10;
        blocksize       = 128;
        readsize        = 256;
      ;

    memory "lock"
        size            = 1;
        read            = "0 1 0 1  1 0 0 0   0 0 0 0  0 0 0 0",
                          "x x x x  x x x x   x x o o  o o o o";

        write           = "1 0 1 0  1 1 0 0   1 1 1 0  0 0 0 0",
                          "0 0 0 0  0 0 0 0   1 1 i i  i i i i";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "lfuse"
        size            = 1;
        read            = "0 1 0 1  0 0 0 0   0 0 0 0  0 0 0 0",
                          "0 0 0 0  0 0 0 0   o o o o  o o o o";

        write           = "1 0 1 0  1 1 0 0   1 0 1 0  0 0 0 0",
                          "0 0 0 0  0 0 0 0   i i i i  i i i i";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "hfuse"
        size            = 1;
        read            = "0 1 0 1  1 0 0 0   0 0 0 0  1 0 0 0",
                          "0 0 0 0  0 0 0 0   o o o o  o o o o";

        write           = "1 0 1 0  1 1 0 0   1 0 1 0  1 0 0 0",
                          "0 0 0 0  0 0 0 0   i i i i  i i i i";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "efuse"
        size            = 1;

        read            = "0 1 0 1  0 0 0 0  0 0 0 0  1 0 0 0",
                          "0 0 0 0  0 0 0 0  o o o o  o o o o";

        write           = "1 0 1 0  1 1 0 0  1 0 1 0  0 1 0 0",
                          "0 0 0 0  0 0 0 0  1 1 1 1  1 i i i";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "signature"
        size            = 3;
        read            = "0  0  1  1   0  0  0  0   0  0  0  0   0  0  0  0",
                          "0  0  0  0   0  0 a1 a0   o  o  o  o   o  o  o  o";
      ;

    memory "calibration"
        size            = 1;

        read            = "0 0 1 1  1 0 0 0   0 0 0 0  0 0 0 0",
                          "0 0 0 0  0 0 0 0   o o o o  o o o o";
        ;
  ;

#------------------------------------------------------------
# ATmega645
#------------------------------------------------------------

part
    id               = "m645";
    desc             = "ATmega645";
    signature        = 0x1E 0x96 0x05;
    has_jtag         = yes;
#   stk500_devcode   = 0x??; # No STK500v1 support?
#   avr910_devcode   = 0x??; # Try the ATmega16 one
    avr910_devcode   = 0x74;
    pagel            = 0xd7;
    bs2              = 0xa0;
    chip_erase_delay = 9000;
    pgm_enable       = "1 0 1 0  1 1 0 0    0 1 0 1  0 0 1 1",
                       "0 0 0 0  0 0 0 0    0 0 0 0  0 0 0 0";

    chip_erase       = "1 0 1 0  1 1 0 0    1 0 0 0  0 0 0 0",
                       "0 0 0 0  0 0 0 0    0 0 0 0  0 0 0 0";

    timeout             = 200;
    stabdelay           = 100;
    cmdexedelay         = 25;
    synchloops          = 32;
    bytedelay           = 0;
    pollindex           = 3;
    pollvalue           = 0x53;
    predelay            = 1;
    postdelay           = 1;
    pollmethod          = 1;

    pp_controlstack     =
        0x0E, 0x1E, 0x0F, 0x1F, 0x2E, 0x3E, 0x2F, 0x3F,
        0x4E, 0x5E, 0x4F, 0x5F, 0x6E, 0x7E, 0x6F, 0x7F,
        0x66, 0x76, 0x67, 0x77, 0x6A, 0x7A, 0x6B, 0x7B,
        0xBE, 0xFD, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00;
    hventerstabdelay    = 100;
    progmodedelay       = 0;
    latchcycles         = 5;
    togglevtg           = 1;
    poweroffdelay       = 15;
    resetdelayms        = 1;
    resetdelayus        = 0;
    hvleavestabdelay    = 15;
    chiperasepulsewidth = 0;
    chiperasepolltimeout = 10;
    programfusepulsewidth = 0;
    programfusepolltimeout = 5;
    programlockpulsewidth = 0;
    programlockpolltimeout = 5;

    idr                 = 0x31;
    spmcr               = 0x57;
    allowfullpagebitstream = no;

    ocdrev              = 3;

    memory "eeprom"
        paged           = no; /* leave this "no" */
        page_size       = 8;  /* for parallel programming */
        size            = 2048;
        min_write_delay = 9000;
        max_write_delay = 9000;
        readback_p1     = 0xff;
        readback_p2     = 0xff;
        read            = "  1   0   1   0      0   0   0   0",
                          "  0   0   0   0      0 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  o   o   o   o      o   o   o   o";

        write           = "  1   1   0   0      0   0   0   0",
                          "  0   0   0   0      0 a10  a9  a8",
                          " a7  a6  a5  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

        loadpage_lo     = "  1   1   0   0      0   0   0   1",
                          "  0   0   0   0      0   0   0   0",
                          "  0   0   0   0      0  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

        writepage       = "  1   1   0   0      0   0   1   0",
                          "  0   0   0   0      0 a10  a9  a8",
                          " a7  a6  a5  a4     a3   0   0   0",
                          "  x   x   x   x      x   x   x   x";

        mode            = 0x41;
        delay           = 10;
        blocksize       = 8;
        readsize        = 256;
      ;

    memory "flash"
        paged           = yes;
        size            = 65536;
        page_size       = 256;
        num_pages       = 256;
        min_write_delay = 4500;
        max_write_delay = 4500;
        readback_p1     = 0xff;
        readback_p2     = 0xff;
        read_lo         = "   0   0   1   0      0   0   0   0",
                          " a15 a14 a13 a12    a11 a10  a9  a8",
                          "  a7  a6  a5  a4     a3  a2  a1  a0",
                          "   o   o   o   o      o   o   o   o";

        read_hi         = "   0   0   1   0      1   0   0   0",
                          " a15 a14 a13 a12    a11 a10  a9  a8",
                          "  a7  a6  a5  a4     a3  a2  a1  a0",
                          "   o   o   o   o      o   o   o   o";

        loadpage_lo     = "  0   1   0   0      0   0   0   0",
                          "  0   0   0   0      0   0   0   0",
                          "  a7 a6  a5  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

        loadpage_hi     = "  0   1   0   0      1   0   0   0",
                          "  0   0   0   0      0   0   0   0",
                          "  a7 a6  a5  a4     a3  a2  a1  a0",
                          "  i   i   i   i      i   i   i   i";

        writepage       = "   0   1   0   0      1   1   0   0",
                          " a15 a14 a13 a12    a11 a10  a9  a8",
                          "  a7  a6  a5  a4     a3  a2  a1  a0",
                          "   0   0   0   0      0   0   0   0";

        mode            = 0x41;
        delay           = 10;
        blocksize       = 128;
        readsize        = 256;
      ;

    memory "lock"
        size            = 1;
        read            = "0 1 0 1  1 0 0 0   0 0 0 0  0 0 0 0",
                          "x x x x  x x x x   x x o o  o o o o";

        write           = "1 0 1 0  1 1 0 0   1 1 1 0  0 0 0 0",
                          "0 0 0 0  0 0 0 0   1 1 i i  i i i i";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "lfuse"
        size            = 1;
        read            = "0 1 0 1  0 0 0 0   0 0 0 0  0 0 0 0",
                          "0 0 0 0  0 0 0 0   o o o o  o o o o";

        write           = "1 0 1 0  1 1 0 0   1 0 1 0  0 0 0 0",
                          "0 0 0 0  0 0 0 0   i i i i  i i i i";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "hfuse"
        size            = 1;
        read            = "0 1 0 1  1 0 0 0   0 0 0 0  1 0 0 0",
                          "0 0 0 0  0 0 0 0   o o o o  o o o o";

        write           = "1 0 1 0  1 1 0 0   1 0 1 0  1 0 0 0",
                          "0 0 0 0  0 0 0 0   i i i i  i i i i";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "efuse"
        size            = 1;

        read            = "0 1 0 1  0 0 0 0  0 0 0 0  1 0 0 0",
                          "0 0 0 0  0 0 0 0  o o o o  o o o o";

        write           = "1 0 1 0  1 1 0 0  1 0 1 0  0 1 0 0",
                          "0 0 0 0  0 0 0 0  1 1 1 1  1 i i i";
        min_write_delay = 9000;
        max_write_delay = 9000;
      ;

    memory "signature"
        size            = 3;
        read            = "0  0  1  1   0  0  0  0   0  0  0  0   0  0  0  0",
                          "0  0  0  0   0  0 a1 a0   o  o  o  o   o  o  o  o";
      ;

    memory "calibration"
        size            = 1;

        read            = "0 0 1 1  1 0 0 0   0 0 0 0  0 0 0 0",
                          "0 0 0 0  0 0 0 0   o o o o  o o o o";
        ;
  ;

#------------------------------------------------------------
# ATmega3250
#------------------------------------------------------------

part parent "m325"
    id               = "m3250";
    desc             = "ATmega3250";
    signature        = 0x1E 0x95 0x06;

    ocdrev              = 3;
  ;

#------------------------------------------------------------
# ATmega6450
#------------------------------------------------------------

part parent "m645"
    id               = "m6450";
    desc             = "ATmega6450";
    signature        = 0x1E 0x96 0x06;

    ocdrev              = 3;
  ;

#------------------------------------------------------------
# AVR XMEGA family common values
#------------------------------------------------------------

part
    id		= ".xmega";
    desc	= "AVR XMEGA family common values";
    has_pdi	= yes;
    nvm_base	= 0x01c0;
    mcu_base	= 0x0090;

    memory "signature"
        size		= 3;
        offset		= 0x1000090;
    ;

    memory "prodsig"
        size		= 0x32;
        offset		= 0x8e0200;
        page_size	= 0x32;
        readsize	= 0x32;
    ;

    memory "fuse1"
        size		= 1;
        offset		= 0x8f0021;
    ;

    memory "fuse2"
        size		= 1;
        offset		= 0x8f0022;
    ;

    memory "fuse4"
        size		= 1;
        offset		= 0x8f0024;
    ;

    memory "fuse5"
        size		= 1;
        offset		= 0x8f0025;
    ;

    memory "lock"
        size		= 1;
        offset		= 0x8f0027;
    ;

    memory "data"
        # SRAM, only used to supply the offset
        offset		= 0x1000000;
    ;
;

#------------------------------------------------------------
# ATxmega16A4U
#------------------------------------------------------------

part parent ".xmega"
    id		= "x16a4u";
    desc	= "ATxmega16A4U";
    signature	= 0x1e 0x94 0x41;
    usbpid	= 0x2fe3;

    memory "eeprom"
        size		= 0x400;
        offset		= 0x8c0000;
        page_size	= 0x20;
        readsize	= 0x100;
    ;

    memory "application"
        size		= 0x4000;
        offset		= 0x800000;
        page_size	= 0x100;
        readsize	= 0x100;
    ;

    memory "apptable"
        size		= 0x1000;
        offset		= 0x803000;
        page_size	= 0x100;
        readsize	= 0x100;
    ;

    memory "boot"
        size		= 0x1000;
        offset		= 0x804000;
        page_size	= 0x100;
        readsize	= 0x100;
    ;

    memory "flash"
        size		= 0x5000;
        offset		= 0x800000;
        page_size	= 0x100;
        readsize	= 0x100;
    ;

    memory "usersig"
        size		= 0x100;
        offset		= 0x8e0400;
        page_size	= 0x100;
        readsize	= 0x100;
    ;
;

#------------------------------------------------------------
# ATxmega16C4
#------------------------------------------------------------

part parent "x16a4u"
    id		= "x16c4";
    desc	= "ATxmega16C4";
    signature	= 0x1e 0x95 0x44;
;

#------------------------------------------------------------
# ATxmega16D4
#------------------------------------------------------------

part parent "x16a4u"
    id		= "x16d4";
    desc	= "ATxmega16D4";
    signature	= 0x1e 0x94 0x42;
;

#------------------------------------------------------------
# ATxmega16A4
#------------------------------------------------------------

part parent "x16a4u"
    id		= "x16a4";
    desc	= "ATxmega16A4";
    signature	= 0x1e 0x94 0x41;
    has_jtag	= yes;

    memory "fuse0"
        size		= 1;
        offset		= 0x8f0020;
    ;
;

#------------------------------------------------------------
# ATxmega32A4U
#------------------------------------------------------------

part parent ".xmega"
    id		= "x32a4u";
    desc	= "ATxmega32A4U";
    signature	= 0x1e 0x95 0x41;
    usbpid	= 0x2fe4;

    memory "eeprom"
        size		= 0x400;
        offset		= 0x8c0000;
        page_size	= 0x20;
        readsize	= 0x100;
    ;

    memory "application"
        size		= 0x8000;
        offset		= 0x800000;
        page_size	= 0x100;
        readsize	= 0x100;
    ;

    memory "apptable"
        size		= 0x1000;
        offset		= 0x807000;
        page_size	= 0x100;
        readsize	= 0x100;
    ;

    memory "boot"
        size		= 0x1000;
        offset		= 0x808000;
        page_size	= 0x100;
        readsize	= 0x100;
    ;

    memory "flash"
        size		= 0x9000;
        offset		= 0x800000;
        page_size	= 0x100;
        readsize	= 0x100;
    ;

    memory "usersig"
        size		= 0x100;
        offset		= 0x8e0400;
        page_size	= 0x100;
        readsize	= 0x100;
    ;
;

#------------------------------------------------------------
# ATxmega32C4
#------------------------------------------------------------

part parent "x32a4u"
    id		= "x32c4";
    desc	= "ATxmega32C4";
    signature	= 0x1e 0x94 0x43;
;

#------------------------------------------------------------
# ATxmega32D4
#------------------------------------------------------------

part parent "x32a4u"
    id		= "x32d4";
    desc	= "ATxmega32D4";
    signature	= 0x1e 0x95 0x42;
;

#------------------------------------------------------------
# ATxmega32A4
#------------------------------------------------------------

part parent "x32a4u"
    id		= "x32a4";
    desc	= "ATxmega32A4";
    signature	= 0x1e 0x95 0x41;
    has_jtag	= yes;

    memory "fuse0"
        size		= 1;
        offset		= 0x8f0020;
    ;
;

#------------------------------------------------------------
# ATxmega64A4U
#------------------------------------------------------------

part parent ".xmega"
    id		= "x64a4u";
    desc	= "ATxmega64A4U";
    signature	= 0x1e 0x96 0x46;
    usbpid	= 0x2fe5;

    memory "eeprom"
        size		= 0x800;
        offset		= 0x8c0000;
        page_size	= 0x20;
        readsize	= 0x100;
    ;

    memory "application"
        size		= 0x10000;
        offset		= 0x800000;
        page_size	= 0x100;
        readsize	= 0x100;
    ;

    memory "apptable"
        size		= 0x1000;
        offset		= 0x80f000;
        page_size	= 0x100;
        readsize	= 0x100;
    ;

    memory "boot"
        size		= 0x1000;
        offset		= 0x810000;
        page_size	= 0x100;
        readsize	= 0x100;
    ;

    memory "flash"
        size		= 0x11000;
        offset		= 0x800000;
        page_size	= 0x100;
        readsize	= 0x100;
    ;

    memory "usersig"
        size		= 0x100;
        offset		= 0x8e0400;
        page_size	= 0x100;
        readsize	= 0x100;
    ;
;

#------------------------------------------------------------
# ATxmega64C3
#------------------------------------------------------------

part parent "x64a4u"
    id		= "x64c3";
    desc	= "ATxmega64C3";
    signature	= 0x1e 0x96 0x49;
    usbpid	= 0x2fd6;
;

#------------------------------------------------------------
# ATxmega64D3
#------------------------------------------------------------

part parent "x64a4u"
    id		= "x64d3";
    desc	= "ATxmega64D3";
    signature	= 0x1e 0x96 0x4a;
;

#------------------------------------------------------------
# ATxmega64D4
#------------------------------------------------------------

part parent "x64a4u"
    id		= "x64d4";
    desc	= "ATxmega64D4";
    signature	= 0x1e 0x96 0x47;
;

#------------------------------------------------------------
# ATxmega64A1
#------------------------------------------------------------

part parent "x64a4u"
    id		= "x64a1";
    desc	= "ATxmega64A1";
    signature	= 0x1e 0x96 0x4e;
    has_jtag	= yes;

    memory "fuse0"
        size		= 1;
        offset		= 0x8f0020;
    ;
;

#------------------------------------------------------------
# ATxmega64A1U
#------------------------------------------------------------

part parent "x64a1"
    id		= "x64a1u";
    desc	= "ATxmega64A1U";
    signature	= 0x1e 0x96 0x4e;
    usbpid	= 0x2fe8;
;

#------------------------------------------------------------
# ATxmega64A3
#------------------------------------------------------------

part parent "x64a1"
    id		= "x64a3";
    desc	= "ATxmega64A3";
    signature	= 0x1e 0x96 0x42;
;

#------------------------------------------------------------
# ATxmega64A3U
#------------------------------------------------------------

part parent "x64a1"
    id		= "x64a3u";
    desc	= "ATxmega64A3U";
    signature	= 0x1e 0x96 0x42;
    usbpid	= 0x2fe5;
;

#------------------------------------------------------------
# ATxmega64A4
#------------------------------------------------------------

part parent "x64a1"
    id		= "x64a4";
    desc	= "ATxmega64A4";
    signature	= 0x1e 0x96 0x46;
;

#------------------------------------------------------------
# ATxmega64B1
#------------------------------------------------------------

part parent "x64a1"
    id		= "x64b1";
    desc	= "ATxmega64B1";
    signature	= 0x1e 0x96 0x52;
    usbpid	= 0x2fe1;
;

#------------------------------------------------------------
# ATxmega64B3
#------------------------------------------------------------

part parent "x64a1"
    id		= "x64b3";
    desc	= "ATxmega64B3";
    signature	= 0x1e 0x96 0x51;
    usbpid	= 0x2fdf;
;

#------------------------------------------------------------
# ATxmega128C3
#------------------------------------------------------------

part parent ".xmega"
    id		= "x128c3";
    desc	= "ATxmega128C3";
    signature	= 0x1e 0x97 0x52;
    usbpid	= 0x2fd7;

    memory "eeprom"
        size		= 0x800;
        offset		= 0x8c0000;
        page_size	= 0x20;
        readsize	= 0x100;
    ;

    memory "application"
        size		= 0x20000;
        offset		= 0x800000;
        page_size	= 0x200;
        readsize	= 0x100;
    ;

    memory "apptable"
        size		= 0x2000;
        offset		= 0x81e000;
        page_size	= 0x200;
        readsize	= 0x100;
    ;

    memory "boot"
        size		= 0x2000;
        offset		= 0x820000;
        page_size	= 0x200;
        readsize	= 0x100;
    ;

    memory "flash"
        size		= 0x22000;
        offset		= 0x800000;
        page_size	= 0x200;
        readsize	= 0x100;
    ;

    memory "usersig"
        size		= 0x200;
        offset		= 0x8e0400;
        page_size	= 0x200;
        readsize	= 0x100;
    ;
;

#------------------------------------------------------------
# ATxmega128D3
#------------------------------------------------------------

part parent "x128c3"
    id		= "x128d3";
    desc	= "ATxmega128D3";
    signature	= 0x1e 0x97 0x48;
;

#------------------------------------------------------------
# ATxmega128D4
#------------------------------------------------------------

part parent "x128c3"
    id		= "x128d4";
    desc	= "ATxmega128D4";
    signature	= 0x1e 0x97 0x47;
;

#------------------------------------------------------------
# ATxmega128A1
#------------------------------------------------------------

part parent "x128c3"
    id		= "x128a1";
    desc	= "ATxmega128A1";
    signature	= 0x1e 0x97 0x4c;
    has_jtag	= yes;

    memory "fuse0"
        size		= 1;
        offset		= 0x8f0020;
    ;
;

#------------------------------------------------------------
# ATxmega128A1 revision D
#------------------------------------------------------------

part parent "x128a1"
    id		= "x128a1d";
    desc	= "ATxmega128A1revD";
    signature	= 0x1e 0x97 0x41;
;

#------------------------------------------------------------
# ATxmega128A1U
#------------------------------------------------------------

part parent "x128a1"
    id		= "x128a1u";
    desc	= "ATxmega128A1U";
    signature	= 0x1e 0x97 0x4c;
    usbpid	= 0x2fed;
;

#------------------------------------------------------------
# ATxmega128A3
#------------------------------------------------------------

part parent "x128a1"
    id		= "x128a3";
    desc	= "ATxmega128A3";
    signature	= 0x1e 0x97 0x42;
;

#------------------------------------------------------------
# ATxmega128A3U
#------------------------------------------------------------

part parent "x128a1"
    id		= "x128a3u";
    desc	= "ATxmega128A3U";
    signature	= 0x1e 0x97 0x42;
    usbpid	= 0x2fe6;
;

#------------------------------------------------------------
# ATxmega128A4
#------------------------------------------------------------

part parent ".xmega"
    id		= "x128a4";
    desc	= "ATxmega128A4";
    signature	= 0x1e 0x97 0x46;
    has_jtag	= yes;

    memory "eeprom"
        size		= 0x800;
        offset		= 0x8c0000;
        page_size	= 0x20;
        readsize	= 0x100;
    ;

    memory "application"
        size		= 0x20000;
        offset		= 0x800000;
        page_size	= 0x200;
        readsize	= 0x100;
    ;

    memory "apptable"
        size		= 0x1000;
        offset		= 0x81f000;
        page_size	= 0x200;
        readsize	= 0x100;
    ;

    memory "boot"
        size		= 0x2000;
        offset		= 0x820000;
        page_size	= 0x200;
        readsize	= 0x100;
    ;

    memory "flash"
        size		= 0x22000;
        offset		= 0x800000;
        page_size	= 0x200;
        readsize	= 0x100;
    ;

    memory "usersig"
        size		= 0x200;
        offset		= 0x8e0400;
        page_size	= 0x200;
        readsize	= 0x100;
    ;

    memory "fuse0"
        size		= 1;
        offset		= 0x8f0020;
    ;
;

#------------------------------------------------------------
# ATxmega128A4U
#------------------------------------------------------------

part parent ".xmega"
    id		= "x128a4u";
    desc	= "ATxmega128A4U";
    signature	= 0x1e 0x97 0x46;
    usbpid	= 0x2fde;

    memory "eeprom"
        size		= 0x800;
        offset		= 0x8c0000;
        page_size	= 0x20;
        readsize	= 0x100;
    ;

    memory "application"
        size		= 0x20000;
        offset		= 0x800000;
        page_size	= 0x100;
        readsize	= 0x100;
    ;

    memory "apptable"
        size		= 0x1000;
        offset		= 0x81f000;
        page_size	= 0x100;
        readsize	= 0x100;
    ;

    memory "boot"
        size		= 0x2000;
        offset		= 0x820000;
        page_size	= 0x100;
        readsize	= 0x100;
    ;

    memory "flash"
        size		= 0x22000;
        offset		= 0x800000;
        page_size	= 0x100;
        readsize	= 0x100;
    ;

    memory "usersig"
        size		= 0x100;
        offset		= 0x8e0400;
        page_size	= 0x100;
        readsize	= 0x100;
    ;
;

#------------------------------------------------------------
# ATxmega128B1
#------------------------------------------------------------

part parent ".xmega"
    id		= "x128b1";
    desc	= "ATxmega128B1";
    signature	= 0x1e 0x97 0x4d;
    usbpid	= 0x2fea;
    has_jtag	= yes;

    memory "eeprom"
        size		= 0x800;
        offset		= 0x8c0000;
        page_size	= 0x20;
        readsize	= 0x100;
    ;

    memory "application"
        size		= 0x20000;
        offset		= 0x800000;
        page_size	= 0x100;
        readsize	= 0x100;
    ;

    memory "apptable"
        size		= 0x2000;
        offset		= 0x81e000;
        page_size	= 0x100;
        readsize	= 0x100;
    ;

    memory "boot"
        size		= 0x2000;
        offset		= 0x820000;
        page_size	= 0x100;
        readsize	= 0x100;
    ;

    memory "flash"
        size		= 0x22000;
        offset		= 0x800000;
        page_size	= 0x100;
        readsize	= 0x100;
    ;

    memory "usersig"
        size		= 0x100;
        offset		= 0x8e0400;
        page_size	= 0x100;
        readsize	= 0x100;
    ;

    memory "fuse0"
        size		= 1;
        offset		= 0x8f0020;
    ;
;

#------------------------------------------------------------
# ATxmega128B3
#------------------------------------------------------------

part parent "x128b1"
    id		= "x128b3";
    desc	= "ATxmega128B3";
    signature	= 0x1e 0x97 0x4b;
    usbpid	= 0x2fe0;
;

#------------------------------------------------------------
# ATxmega192C3
#------------------------------------------------------------

part parent ".xmega"
    id		= "x192c3";
    desc	= "ATxmega192C3";
    signature	= 0x1e 0x97 0x51;
    # usbpid	= 0x2f??;

    memory "eeprom"
        size		= 0x800;
        offset		= 0x8c0000;
        page_size	= 0x20;
        readsize	= 0x100;
    ;

    memory "application"
        size		= 0x30000;
        offset		= 0x800000;
        page_size	= 0x200;
        readsize	= 0x100;
    ;

    memory "apptable"
        size		= 0x2000;
        offset		= 0x82e000;
        page_size	= 0x200;
        readsize	= 0x100;
    ;

    memory "boot"
        size		= 0x2000;
        offset		= 0x830000;
        page_size	= 0x200;
        readsize	= 0x100;
    ;

    memory "flash"
        size		= 0x32000;
        offset		= 0x800000;
        page_size	= 0x200;
        readsize	= 0x100;
    ;

    memory "usersig"
        size		= 0x200;
        offset		= 0x8e0400;
        page_size	= 0x200;
        readsize	= 0x100;
    ;
;

#------------------------------------------------------------
# ATxmega192D3
#------------------------------------------------------------

part parent "x192c3"
    id		= "x192d3";
    desc	= "ATxmega192D3";
    signature	= 0x1e 0x97 0x49;
;

#------------------------------------------------------------
# ATxmega192A1
#------------------------------------------------------------

part parent "x192c3"
    id		= "x192a1";
    desc	= "ATxmega192A1";
    signature	= 0x1e 0x97 0x4e;
    has_jtag	= yes;

    memory "fuse0"
        size		= 1;
        offset		= 0x8f0020;
    ;
;

#------------------------------------------------------------
# ATxmega192A3
#------------------------------------------------------------

part parent "x192a1"
    id		= "x192a3";
    desc	= "ATxmega192A3";
    signature	= 0x1e 0x97 0x44;
;

#------------------------------------------------------------
# ATxmega192A3U
#------------------------------------------------------------

part parent "x192a1"
    id		= "x192a3u";
    desc	= "ATxmega192A3U";
    signature	= 0x1e 0x97 0x44;
    usbpid	= 0x2fe7;
;

#------------------------------------------------------------
# ATxmega256C3
#------------------------------------------------------------

part parent ".xmega"
    id		= "x256c3";
    desc	= "ATxmega256C3";
    signature	= 0x1e 0x98 0x46;
    usbpid	= 0x2fda;

    memory "eeprom"
        size		= 0x1000;
        offset		= 0x8c0000;
        page_size	= 0x20;
        readsize	= 0x100;
    ;

    memory "application"
        size		= 0x40000;
        offset		= 0x800000;
        page_size	= 0x200;
        readsize	= 0x100;
    ;

    memory "apptable"
        size		= 0x2000;
        offset		= 0x83e000;
        page_size	= 0x200;
        readsize	= 0x100;
    ;

    memory "boot"
        size		= 0x2000;
        offset		= 0x840000;
        page_size	= 0x200;
        readsize	= 0x100;
    ;

    memory "flash"
        size		= 0x42000;
        offset		= 0x800000;
        page_size	= 0x200;
        readsize	= 0x100;
    ;

    memory "usersig"
        size		= 0x200;
        offset		= 0x8e0400;
        page_size	= 0x200;
        readsize	= 0x100;
    ;
;

#------------------------------------------------------------
# ATxmega256D3
#------------------------------------------------------------

part parent "x256c3"
    id		= "x256d3";
    desc	= "ATxmega256D3";
    signature	= 0x1e 0x98 0x44;
;

#------------------------------------------------------------
# ATxmega256A1
#------------------------------------------------------------

part parent "x256c3"
    id		= "x256a1";
    desc	= "ATxmega256A1";
    signature	= 0x1e 0x98 0x46;
    has_jtag	= yes;

    memory "fuse0"
        size		= 1;
        offset		= 0x8f0020;
    ;
;

#------------------------------------------------------------
# ATxmega256A3
#------------------------------------------------------------

part parent "x256a1"
    id		= "x256a3";
    desc	= "ATxmega256A3";
    signature	= 0x1e 0x98 0x42;
;

#------------------------------------------------------------
# ATxmega256A3U
#------------------------------------------------------------

part parent "x256a1"
    id		= "x256a3u";
    desc	= "ATxmega256A3U";
    signature	= 0x1e 0x98 0x42;
    usbpid	= 0x2fec;
;

#------------------------------------------------------------
# ATxmega256A3B
#------------------------------------------------------------

part parent "x256a1"
    id		= "x256a3b";
    desc	= "ATxmega256A3B";
    signature	= 0x1e 0x98 0x43;
;

#------------------------------------------------------------
# ATxmega256A3BU
#------------------------------------------------------------

part parent "x256a1"
    id		= "x256a3bu";
    desc	= "ATxmega256A3BU";
    signature	= 0x1e 0x98 0x43;
    usbpid	= 0x2fe2;
;

#------------------------------------------------------------
# ATxmega384C3
#------------------------------------------------------------

part parent ".xmega"
    id		= "x384c3";
    desc	= "ATxmega384C3";
    signature	= 0x1e 0x98 0x45;
    usbpid	= 0x2fdb;

    memory "eeprom"
        size		= 0x1000;
        offset		= 0x8c0000;
        page_size	= 0x20;
        readsize	= 0x100;
    ;

    memory "application"
        size		= 0x60000;
        offset		= 0x800000;
        page_size	= 0x200;
        readsize	= 0x100;
    ;

    memory "apptable"
        size		= 0x2000;
        offset		= 0x85e000;
        page_size	= 0x200;
        readsize	= 0x100;
    ;

    memory "boot"
        size		= 0x2000;
        offset		= 0x860000;
        page_size	= 0x200;
        readsize	= 0x100;
    ;

    memory "flash"
        size		= 0x62000;
        offset		= 0x800000;
        page_size	= 0x200;
        readsize	= 0x100;
    ;

    memory "usersig"
        size		= 0x200;
        offset		= 0x8e0400;
        page_size	= 0x200;
        readsize	= 0x100;
    ;
;

#------------------------------------------------------------
# ATxmega384D3
#------------------------------------------------------------

part parent "x384c3"
    id		= "x384d3";
    desc	= "ATxmega384D3";
    signature	= 0x1e 0x98 0x47;
;

#------------------------------------------------------------
# ATxmega8E5
#------------------------------------------------------------

part parent ".xmega"
    id		= "x8e5";
    desc	= "ATxmega8E5";
    signature	= 0x1e 0x93 0x41;

    memory "eeprom"
        size		= 0x0200;
        offset		= 0x08c0000;
        page_size	= 0x20;
        readsize	= 0x100;
    ;

    memory "application"
        size		= 0x2000;
        offset		= 0x0800000;
        page_size	= 0x80;
        readsize	= 0x100;
    ;

    memory "apptable"
        size		= 0x800;
        offset		= 0x00801800;
        page_size	= 0x80;
        readsize	= 0x100;
    ;

    memory "boot"
        size		= 0x800;
        offset		= 0x00802000;
        page_size	= 0x80;
        readsize	= 0x100;
    ;

    memory "flash"
        size		= 0x2800;
        offset		= 0x0800000;
        page_size	= 0x80;
        readsize	= 0x100;
    ;

    memory "usersig"
        size            = 0x80;
        offset          = 0x8e0400;
        page_size       = 0x80;
        readsize	= 0x100;
    ;
;

#------------------------------------------------------------
# ATxmega16E5
#------------------------------------------------------------

part parent ".xmega"
    id		= "x16e5";
    desc	= "ATxmega16E5";
    signature	= 0x1e 0x94 0x45;

    memory "eeprom"
        size		= 0x0200;
        offset		= 0x08c0000;
        page_size	= 0x20;
        readsize	= 0x100;
    ;

    memory "application"
        size		= 0x4000;
        offset		= 0x0800000;
        page_size	= 0x80;
        readsize	= 0x100;
    ;

    memory "apptable"
        size		= 0x1000;
        offset		= 0x00803000;
        page_size	= 0x80;
        readsize	= 0x100;
    ;

    memory "boot"
        size		= 0x1000;
        offset		= 0x00804000;
        page_size	= 0x80;
        readsize	= 0x100;
    ;

    memory "flash"
        size		= 0x5000;
        offset		= 0x0800000;
        page_size	= 0x80;
        readsize	= 0x100;
    ;

    memory "usersig"
        size            = 0x80;
        offset          = 0x8e0400;
        page_size       = 0x80;
        readsize	= 0x100;
    ;
;

#------------------------------------------------------------
# ATxmega32E5
#------------------------------------------------------------

part parent ".xmega"
    id		= "x32e5";
    desc	= "ATxmega32E5";
    signature	= 0x1e 0x95 0x4c;

    memory "eeprom"
        size		= 0x0400;
        offset		= 0x08c0000;
        page_size	= 0x20;
        readsize	= 0x100;
    ;

    memory "application"
        size		= 0x8000;
        offset		= 0x0800000;
        page_size	= 0x80;
        readsize	= 0x100;
    ;

    memory "apptable"
        size		= 0x1000;
        offset		= 0x00807000;
        page_size	= 0x80;
        readsize	= 0x100;
    ;

    memory "boot"
        size		= 0x1000;
        offset		= 0x00808000;
        page_size	= 0x80;
        readsize	= 0x100;
    ;

    memory "flash"
        size		= 0x9000;
        offset		= 0x0800000;
        page_size	= 0x80;
        readsize	= 0x100;
    ;

    memory "usersig"
        size            = 0x80;
        offset          = 0x8e0400;
        page_size       = 0x80;
        readsize	= 0x100;
    ;
;

#------------------------------------------------------------
# AVR32UC3A0512
#------------------------------------------------------------

part
    id		= "uc3a0512";
    desc	= "AT32UC3A0512";
    signature	= 0xED 0xC0 0x3F;
    has_jtag	= yes;
    is_avr32	= yes;

    memory "flash"
        paged           = yes;
        page_size       = 512;           # bytes
        readsize        = 512;           # bytes
        num_pages       = 1024;          # could be set dynamicly
        size            = 0x00080000;    # could be set dynamicly
        offset          = 0x80000000;
    ;
;

part parent "uc3a0512"
    id		= "ucr2";
    desc	= "deprecated, use 'uc3a0512'";
;

#------------------------------------------------------------
# ATtiny1634.
#------------------------------------------------------------

part
    id              = "t1634";
    desc            = "ATtiny1634";
     has_debugwire = yes;
     flash_instr   = 0xB6, 0x01, 0x11;
     eeprom_instr  = 0xBD, 0xF2, 0xBD, 0xE1, 0xBB, 0xCF, 0xB4, 0x00,
                0xBE, 0x01, 0xB6, 0x01, 0xBC, 0x00, 0xBB, 0xBF,
                0x99, 0xF9, 0xBB, 0xAF;
    stk500_devcode  = 0x86;
    # avr910_devcode = 0x;
    signature       = 0x1e 0x94 0x12;
    pagel           = 0xB3;
    bs2             = 0xB1;
    reset	    = io;
    chip_erase_delay = 9000;
    pgm_enable       = "1 0 1 0 1 1 0 0 0 1 0 1 0 0 1 1",
                       "x x x x x x x x x x x x x x x x";

    chip_erase       = "1 0 1 0 1 1 0 0 1 0 0 x x x x x",
                       "x x x x x x x x x x x x x x x x";

    timeout         = 200;
    stabdelay       = 100;
    cmdexedelay     = 25;
    synchloops      = 32;
    bytedelay       = 0;
    pollindex       = 3;
    pollvalue       = 0x53;
    predelay        = 1;
    postdelay       = 1;
    pollmethod      = 1;

    pp_controlstack     =
        0x0E, 0x1E, 0x0E, 0x1E, 0x2E, 0x3E, 0x2E, 0x3E,
        0x4E, 0x5E, 0x4E, 0x5E, 0x6E, 0x7E, 0x6E, 0x7E,
        0x26, 0x36, 0x66, 0x76, 0x2A, 0x3A, 0x6A, 0x7A,
        0x2E, 0xFD, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00;
    hventerstabdelay    = 100;
    progmodedelay       = 0;
    latchcycles         = 0;
    togglevtg           = 1;
    poweroffdelay       = 15;
    resetdelayms        = 1;
    resetdelayus        = 0;
    hvleavestabdelay    = 15;
    resetdelay          = 15;
    chiperasepulsewidth = 0;
    chiperasepolltimeout = 10;
    programfusepulsewidth = 0;
    programfusepolltimeout = 5;
    programlockpulsewidth = 0;
    programlockpolltimeout = 5;

    memory "eeprom"
        paged           = no;
        page_size       = 4;
        size            = 256;
        min_write_delay = 3600;
        max_write_delay = 3600;
        readback_p1     = 0xff;
        readback_p2     = 0xff;
        read            = " 1 0 1 0 0 0 0 0",
                          " 0 0 0 x x x x a8",
                          " a7 a6 a5 a4 a3 a2 a1 a0",
                          " o o o o o o o o";

        write           = " 1 1 0 0 0 0 0 0",
                          " 0 0 0 x x x x a8",
                          " a7 a6 a5 a4 a3 a2 a1 a0",
                          " i i i i i i i i";

   loadpage_lo   = "  1   1   0   0      0   0   0   1",
           "  0   0   0   0      0   0   0   0",
           "  0   0   0   0      0   0  a1  a0",
           "  i   i   i   i      i   i   i   i";

   writepage   = "  1   1   0   0      0   0   1   0",
           "  0   0   x   x      x   x   x  a8",
           " a7  a6  a5  a4     a3  a2   0   0",
           "  x   x   x   x      x   x   x   x";

   mode      = 0x41;
   delay      = 5;
   blocksize   = 4;
   readsize   = 256;
        ;

    memory "flash"
        paged           = yes;
        size            = 16384;
        page_size       = 32;
        num_pages       = 512;
        min_write_delay = 4500;
        max_write_delay = 4500;
        readback_p1     = 0xff;
        readback_p2     = 0xff;
        read_lo         = " 0 0 1 0 0 0 0 0",
                          " 0 0 0 a12 a11 a10 a9 a8",
                          " a7 a6 a5 a4 a3 a2 a1 a0",
                          " o o o o o o o o";

        read_hi          = " 0 0 1 0 1 0 0 0",
                           " 0 0 0 a12 a11 a10 a9 a8",
                           " a7 a6 a5 a4 a3 a2 a1 a0",
                           " o o o o o o o o";

        loadpage_lo     = " 0 1 0 0 0 0 0 0",
                          " 0 0 0 x x x x x",
                          " x x a5 a4 a3 a2 a1 a0",
                          " i i i i i i i i";

        loadpage_hi     = " 0 1 0 0 1 0 0 0",
                          " 0 0 0 x x x x x",
                          " x x a5 a4 a3 a2 a1 a0",
                          " i i i i i i i i";

        writepage       = " 0 1 0 0 1 1 0 0",
                          " 0 0 0 a12 a11 a10 a9 a8",
                          " a7 a6 x x x x x x",
                          " x x x x x x x x";

        mode        = 0x41;
        delay       = 6;
        blocksize   = 128;
        readsize    = 256;

        ;

    memory "lfuse"
        size            = 1;
        min_write_delay = 4500;
        max_write_delay = 4500;
        read            = "0 1 0 1 0 0 0 0 0 0 0 0 0 0 0 0",
                          "x x x x x x x x o o o o o o o o";

        write           = "1 0 1 0 1 1 0 0 1 0 1 0 0 0 0 0",
                          "x x x x x x x x i i i i i i i i";
        ;

    memory "hfuse"
        size            = 1;
        min_write_delay = 4500;
        max_write_delay = 4500;
        read            = "0 1 0 1 1 0 0 0 0 0 0 0 1 0 0 0",
                          "x x x x x x x x o o o o o o o o";

        write           = "1 0 1 0 1 1 0 0 1 0 1 0 1 0 0 0",
                          "x x x x x x x x i i i i i i i i";
        ;

    memory "efuse"
        size            = 1;
        min_write_delay = 4500;
        max_write_delay = 4500;
        read            = "0 1 0 1 0 0 0 0 0 0 0 0 1 0 0 0",
                          "x x x x x x x x o o o o o o o o";

        write           = "1 0 1 0 1 1 0 0 1 0 1 0 0 1 0 0",
                          "x x x x x x x x x x x i i i i i";
        ;

    memory "lock"
        size            = 1;
        min_write_delay = 4500;
        max_write_delay = 4500;
        read            = "0 1 0 1 1 0 0 0 0 0 0 0 0 0 0 0",
                          "x x x x x x x x x x x x x x o o";

        write           = "1 0 1 0 1 1 0 0 1 1 1 x x x x x",
                          "x x x x x x x x 1 1 1 1 1 1 i i";
        ;

    memory "calibration"
        size            = 1;
        read            = "0 0 1 1 1 0 0 0 0 0 0 x x x x x",
                          "0 0 0 0 0 0 0 0 o o o o o o o o";
        ;

    memory "signature"
        size            = 3;
        read            = "0 0 1 1 0 0 0 0 0 0 0 x x x x x",
                          "x x x x x x a1 a0 o o o o o o o o";
        ;
;

#------------------------------------------------------------
# Common values for reduced core tinys (4/5/9/10/20/40)
#------------------------------------------------------------

part
    id		= ".reduced_core_tiny";
    desc	= "Common values for reduced core tinys";
    has_tpi	= yes;

    memory "signature"
        size		= 3;
        offset		= 0x3fc0;
        page_size	= 16;
    ;

    memory "fuse"
        size		= 1;
        offset		= 0x3f40;
        page_size	= 16;
	blocksize	= 4;
    ;

    memory "calibration"
        size		= 1;
        offset		= 0x3f80;
        page_size	= 16;
    ;

    memory "lockbits"
        size		= 1;
        offset		= 0x3f00;
        page_size	= 16;
    ;
;

#------------------------------------------------------------
# ATtiny4
#------------------------------------------------------------

part parent ".reduced_core_tiny"
    id		= "t4";
    desc	= "ATtiny4";
    signature	= 0x1e 0x8f 0x0a;

    memory "flash"
        size		= 512;
        offset		= 0x4000;
        page_size	= 16;
        blocksize	= 128;
    ;
;

#------------------------------------------------------------
# ATtiny5
#------------------------------------------------------------

part parent "t4"
    id		= "t5";
    desc	= "ATtiny5";
    signature	= 0x1e 0x8f 0x09;
;

#------------------------------------------------------------
# ATtiny9
#------------------------------------------------------------

part parent ".reduced_core_tiny"
    id		= "t9";
    desc	= "ATtiny9";
    signature	= 0x1e 0x90 0x08;

    memory "flash"
        size		= 1024;
        offset		= 0x4000;
        page_size	= 16;
        blocksize	= 128;
    ;
;

#------------------------------------------------------------
# ATtiny10
#------------------------------------------------------------

part parent "t9"
    id		= "t10";
    desc	= "ATtiny10";
    signature	= 0x1e 0x90 0x03;
;

#------------------------------------------------------------
# ATtiny20
#------------------------------------------------------------

part parent ".reduced_core_tiny"
    id          = "t20";
    desc        = "ATtiny20";
    signature   = 0x1e 0x91 0x0F;

    memory "flash"
        size            = 2048;
        offset          = 0x4000;
        page_size       = 16;
        blocksize       = 128;
    ;
;

#------------------------------------------------------------
# ATtiny40
#------------------------------------------------------------

part parent ".reduced_core_tiny"
    id		= "t40";
    desc	= "ATtiny40";
    signature	= 0x1e 0x92 0x0E;

    memory "flash"
        size		= 4096;
        offset		= 0x4000;
        page_size	= 64;
        blocksize	= 128;
    ;
;

#------------------------------------------------------------
# ATmega406
#------------------------------------------------------------

part
    id				= "m406";
    desc			= "ATMEGA406";
    has_jtag			= yes;
    signature			= 0x1e 0x95 0x07;

    # STK500 parameters (parallel programming IO lines)
    pagel			= 0xa7;
    bs2				= 0xa0;
    serial			= no;
    parallel			= yes;

    # STK500v2 HV programming parameters, from XML
    pp_controlstack		= 0x0e, 0x1e, 0x0f, 0x1f, 0x2e, 0x3e, 0x2f, 0x3f,
				  0x4e, 0x5e, 0x4f, 0x5f, 0x6e, 0x7e, 0x6f, 0x7f,
				  0x66, 0x76, 0x67, 0x77, 0x6a, 0x7a, 0x6b, 0x7b,
				  0xbe, 0xfd, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00;

    # JTAG ICE mkII parameters, also from XML files
    allowfullpagebitstream	= no;
    enablepageprogramming	= yes;
    idr				= 0x51;
    rampz			= 0x00;
    spmcr			= 0x57;
    eecr			= 0x3f;

    memory "eeprom"
        paged		= no;
        size		= 512;
        page_size	= 4;
        blocksize	= 4;
	readsize	= 4;
        num_pages	= 128;
    ;

    memory "flash"
        paged		= yes;
        size		= 40960;
        page_size	= 128;
        blocksize	= 128;
	readsize	= 128;
        num_pages	= 320;
    ;

    memory "hfuse"
        size            = 1;
    ;

    memory "lfuse"
        size            = 1;
    ;

    memory "lockbits"
        size		= 1;
    ;

    memory "signature"
        size            = 3;
    ;
;


