var indexerLanguage="en";
//Auto generated index for searching by xsl-webhelpindexer for DocBook Webhelp.# <PERSON><PERSON>, University of Moratuwa
w["-"]="0*2,1*2,2*2,3*2,4*2,5*2,6*2,7*2,8*2,9*2,10*2,11*2,12*2,13*2,14*2,15*2,16*2,17*2,18*2,19*2,20*2,21*2,22*2,23*2,24*2,25*2,26*2,27*2,28*2,29*2,30*2,31*2,32*2,33*2,34*2,35*2,36*2,37*2,38*2,39*2,40*2,41*2,42*2,43*2,44*2,45*2,46*2,47*2,48*2,49*2,50*2,51*2,52*2,53*2,54*2,55*2,56*2,57*2,58*2,59*2,60*2,61*2,62*2,63*2,64*2,65*2,66*2,67*2,68*2,69*2,70*3,71*2,72*2,73*2,74*2,75*2,76*2,77*2,78*2,79*2,80*2,81*2,82*2,83*2,84*2,85*2,86*2,87*2,88*2,89*2,90*2,91*2,92*2,93*2,94*2,95*2,96*2,97*2,98*2,99*2,100*2,101*2,102*2,103*2,104*2,105*2,106*2,107*2,108*2,109*2,110*2,111*4,112*2,113*2,114*2,115*2,116*2,117*2,118*2,119*2,120*2,121*2,122*2,123*2,124*2,125*2,126*2,127*2,128*2,129*2,130*2,131*8,132*12,133*7,134*2,135*2,136*2,137*2,138*2,139*2,140*2,141*2,142*2,143*2,144*2,145*2,146*2,147*2,148*2,149*2,150*2,151*2,152*2,153*2,154*2,155*2,156*2,157*2,158*2,159*2,160*2,161*2,162*2,163*2,164*2,165*2,166*3,167*2,168*2,169*2,170*2,171*1,173*2,174*2,175*2,176*2,177*2,178*2,179*2,180*2,181*2,182*2,183*2,184*2,185*2,186*2,187*2,188*2,189*2,190*2,191*2,192*2,193*2";
w["-board"]="172*1";
w["-chip"]="1*2";
w["."]="22*1,30*1,37*3,52*1,62*1,77*2,96*1,109*1,119*1,120*1,131*4,132*3,133*12,135*1,139*2,148*2,171*1,172*1,174*1,175*1,180*1";
w[".e."]="173*2";
w["0"]="21*1,34*5,49*1,51*1,56*6,85*5,91*3,114*1,119*1,120*1,126*5,132*1,140*3,145*1,147*1,148*3,169*1,173*1,181*1,182*1,185*1,186*1";
w["0-6"]="148*1";
w["0."]="132*1";
w["00"]="136*2";
w["01"]="78*1,170*1";
w["05"]="136*1";
w["09"]="136*2,170*1";
w["09.10.2013"]="170*1";
w["0:"]="18*1";
w["0x00"]="6*1,9*1,10*1,13*4,14*2,16*1,17*1,23*1,24*2,27*2,28*1,29*1,30*3,32*1,33*1,34*1,35*1,37*6,38*1,40*2,41*1,42*1,43*3,45*2,46*1,47*1,49*1,50*1,51*1,52*1,54*1,55*1,56*1,57*1,59*2,60*2,61*2,63*10,65*2,66*1,67*1,68*2,69*1,71*1,72*2,73*1,74*1,75*1,76*1,77*1,78*1,79*1,81*1,82*1,83*1,84*1,85*2,86*1,91*8,93*2,94*1,95*1,96*2,97*1,98*1,99*2,100*1,101*1,102*1,103*1,104*1,105*2,106*1,107*1,108*1,109*1,110*1,111*2,112*2,113*1,114*1,115*2,116*1,117*1,118*1,119*1,120*1,121*1,123*1,124*1,125*1,126*1,127*1,129*2,130*1,131*1,133*1,140*14,160*1,182*2,185*4,186*10,188*1";
w["0x00."]="131*1";
w["0x0000"]="25*1";
w["0x000000"]="133*12";
w["0x00:"]="6*1,9*1,10*1,13*2,14*2,16*1,17*1,23*1,24*1,30*3,68*1,72*1,96*1,105*1";
w["0x01"]="6*1,13*1,14*2,23*1,24*1,28*1,30*3,34*1,38*1,46*1,56*1,59*1,60*1,61*1,63*6,66*1,68*1,72*1,85*1,91*5,94*1,96*1,99*1,101*1,102*1,105*2,111*1,112*1,115*1,117*1,126*1,129*1,140*10,169*1,182*3,185*1,186*7";
w["0x01:"]="6*1,13*1,23*1,24*1,30*3,68*1,72*1,96*1,101*1,102*1,105*2,117*1,182*2";
w["0x02"]="14*1,15*2,29*1,47*1,63*2,67*1,91*4,95*1,101*1,102*1,105*2,111*1,112*1,132*2,140*5,169*1,185*2,186*5";
w["0x02:"]="14*1,101*1,102*1,105*2";
w["0x03"]="16*2,63*1,91*4,111*1,112*1,117*1,132*1,140*4,169*2,186*3";
w["0x03:"]="117*1";
w["0x04"]="17*2,91*5,111*1,140*4,169*2,182*1,185*1,186*2";
w["0x05"]="91*3,111*1,140*3,169*1,186*1";
w["0x06"]="91*3,111*1,140*2,160*1,185*2,186*1";
w["0x07"]="91*3,111*1,140*2,169*1,182*1,186*1";
w["0x08"]="18*2,91*2,140*1,185*1,186*2";
w["0x09"]="19*2,91*1,186*2";
w["0x0a"]="20*2,91*2,185*2,186*2";
w["0x0b"]="91*2,186*1";
w["0x0c"]="91*2,185*1,186*1";
w["0x0d"]="91*1,186*1";
w["0x0e"]="37*3,91*1,185*2,186*1";
w["0x0f"]="21*2,91*1,186*2";
w["0x10"]="43*1,63*4,91*3,140*3,160*1,185*3,186*5";
w["0x11"]="38*1,63*4,91*2,140*2,160*1,186*3";
w["0x12"]="38*1,63*2,91*2,140*2,160*1,185*2,186*2";
w["0x13"]="38*1,63*1,91*2,140*2,160*1,185*2,186*2";
w["0x14"]="38*1,91*2,140*2,160*1,185*1,186*2";
w["0x15"]="91*2,140*2,160*1,186*2";
w["0x16"]="91*2,140*2,160*1,186*2";
w["0x17"]="91*2,140*2,160*1,186*2";
w["0x18"]="91*2,140*1,160*1,185*3,186*1";
w["0x19"]="91*2,140*1,160*1,185*2,186*1";
w["0x1a"]="91*1,140*1,160*1,185*2";
w["0x1b"]="91*1,140*1,160*1,185*2";
w["0x1c"]="160*1,185*3";
w["0x1d"]="91*1,160*1,185*2";
w["0x1e"]="91*1,131*1,160*1,185*2";
w["0x1e."]="131*1";
w["0x1f"]="91*1";
w["0x20"]="38*1,63*1,91*1,140*4,185*1,186*3";
w["0x21"]="63*1,91*1,140*3";
w["0x2111"]="1*1";
w["0x2140"]="1*1";
w["0x2141"]="1*1";
w["0x2145"]="1*1";
w["0x22"]="63*1,91*1,140*3";
w["0x23"]="63*1,91*1,140*2";
w["0x24"]="91*2,140*1,185*1";
w["0x25"]="91*1";
w["0x26"]="91*1,185*1";
w["0x28"]="185*1";
w["0x29"]="91*1";
w["0x2a"]="91*1,185*1";
w["0x2b"]="91*1,185*1";
w["0x2c"]="91*1";
w["0x2d"]="91*1,185*1";
w["0x2e"]="91*1";
w["0x2f"]="91*1";
w["0x30"]="63*2,91*1,140*2,186*1";
w["0x31"]="63*2,91*1,140*3";
w["0x32"]="63*1,91*1,140*2";
w["0x33"]="63*1,91*1,140*2";
w["0x34"]="140*2";
w["0x35"]="140*2";
w["0x36"]="140*2";
w["0x37"]="140*1";
w["0x38"]="63*1,140*1";
w["0x39"]="140*1";
w["0x3a"]="140*1";
w["0x3b"]="140*1";
w["0x3c"]="140*1";
w["0x3d"]="140*1";
w["0x40"]="63*1,140*3";
w["0x41"]="63*1,140*3";
w["0x42"]="63*1,140*1";
w["0x43"]="63*1,140*2";
w["0x44"]="140*2";
w["0x45"]="140*1";
w["0x50"]="63*1,140*2";
w["0x53"]="145*1";
w["0x60"]="140*1";
w["0x61"]="140*1";
w["0x70"]="140*1";
w["0x71"]="140*1";
w["0x72"]="140*1";
w["0x80"]="3*1,4*1,23*2,43*1,63*1,91*3,140*2,160*1,186*1";
w["0x81"]="3*1,4*1,24*2,43*2,63*1,91*2,140*2,160*1,186*1";
w["0x82"]="3*1,4*1,25*2,43*1,91*3";
w["0x83"]="3*1,9*2,91*2,140*1";
w["0x84"]="3*1,10*2,63*1,91*2,140*1,186*1";
w["0x85"]="3*1,91*1";
w["0x86"]="3*1,6*2";
w["0x87"]="3*1,13*2,14*2,15*2,16*2,17*2,18*2,19*2,20*2,21*2,192*1";
w["0x88"]="3*1";
w["0x89"]="3*1";
w["0x90"]="91*1,140*1";
w["0x91"]="91*1,140*1";
w["0x92"]="91*1";
w["0x93"]="91*1";
w["0xa0"]="43*1,63*1,91*1,140*2,186*1";
w["0xa1"]="63*1";
w["0xb0"]="140*1";
w["0xb1"]="140*1";
w["0xb2"]="140*1";
w["0xb3"]="140*1";
w["0xb4"]="140*1";
w["0xb5"]="140*1";
w["0xb8"]="140*1";
w["0xc0"]="140*1,160*1";
w["0xc1"]="140*1";
w["0xc2"]="140*1";
w["0xc3"]="140*1";
w["0xc4"]="140*1";
w["0xc5"]="140*1";
w["0xc6"]="140*1";
w["0xc9"]="160*1";
w["0xcb"]="160*1";
w["0xcc"]="160*1";
w["0xcd"]="160*1";
w["0xe0"]="43*1";
w["0xfe"]="91*1";
w["0xff"]="13*1,14*1,16*1,17*1,35*1,42*1,43*1,57*1,86*1,91*1,127*1,140*2,186*1";
w["0xff:"]="13*1,14*1,16*1,17*1";
w["1"]="6*3,9*8,10*5,13*6,14*6,15*4,16*5,17*5,18*4,19*6,20*4,21*4,23*4,24*3,25*2,27*3,28*5,29*5,30*3,32*2,33*2,34*3,35*3,37*8,40*3,41*2,42*3,45*3,46*5,47*5,49*3,50*2,51*4,52*2,54*2,55*2,56*3,57*3,59*3,60*3,61*3,65*3,66*5,67*5,68*3,69*2,70*2,71*2,72*3,73*3,74*2,75*3,76*3,77*4,78*2,79*4,81*2,82*2,83*2,84*2,85*10,86*3,93*3,94*5,95*5,96*3,97*2,98*2,99*3,100*2,101*3,102*3,103*2,104*2,105*4,106*2,107*2,108*2,109*2,110*2,111*3,112*3,113*3,114*5,115*4,116*2,117*6,118*4,119*2,120*2,121*2,123*2,124*2,125*2,126*3,127*3,129*3,130*3,142*3,143*3,144*3,145*15,146*5,147*11,148*15,149*7,152*8,153*11,162*3,163*3,164*5,165*4,166*6,167*6,169*3,181*1,182*7,185*27,186*1,188*2,193*3";
w["1-4"]="164*1";
w["1."]="149*1";
w["10"]="170*1,171*1,185*1,186*1";
w["100"]="185*1";
w["100."]="185*1";
w["1024"]="181*1";
w["11"]="186*1";
w["12"]="186*1";
w["120"]="52*1";
w["13"]="186*1";
w["14"]="18*1,186*2";
w["14:"]="18*1";
w["15"]="18*1,186*2";
w["15:"]="18*1";
w["16"]="91*3,181*1,182*1,185*1";
w["16-bit"]="91*3";
w["1:"]="19*1";
w["1byte"]="185*2";
w["1e"]="136*2";
w["2"]="1*1,9*1,18*1,19*1,23*1,24*1,25*1,37*3,79*1,117*2,118*2,129*1,131*1,143*1,144*1,145*1,147*1,148*4,149*1,152*1,153*1,166*1,167*1,169*2,181*1,182*4,185*15,186*1,193*2";
w["20"]="185*1";
w["2013"]="170*1";
w["2014"]="170*1,171*3";
w["24"]="182*1,185*1";
w["256"]="167*1";
w["28"]="170*1";
w["28.01.2014"]="170*1";
w["2^max_read"]="182*1";
w["2^max_writ"]="182*1";
w["3"]="1*2,21*1,117*2,118*2,131*1,132*4,145*2,147*1,148*2,149*1,152*1,153*1,169*1,186*1";
w["300ms"]="6*1";
w["300ms."]="6*1";
w["32"]="38*1,51*1,52*1,68*1,70*1,173*1";
w["32-bit"]="38*1,51*1,68*1,70*1,173*1";
w["32.120"]="52*1";
w["3f"]="136*2";
w["3rd"]="148*1";
w["4"]="15*2,20*1,23*2,50*1,75*1,76*1,79*1,83*1,84*1,104*1,107*1,111*1,112*2,113*2,114*2,115*2,116*1,117*1,119*1,120*1,125*1,129*1,132*2,142*1,145*1,147*1,148*1,152*1,153*1,164*1,165*1,166*1,167*1,169*2,181*6,182*3,185*17,186*1";
w["4-bit"]="23*1";
w["42197a"]="170*1";
w["42197ax"]="171*1";
w["42197ax-mcu-10"]="171*1";
w["42197b"]="170*1";
w["5"]="75*1,76*1,148*1,169*1,182*1,185*1,186*1";
w["50"]="185*1";
w["508"]="18*1";
w["512"]="166*1";
w["6"]="19*1,148*2";
w["6:"]="19*1";
w["7"]="19*1,148*2,186*1,192*1";
w["74"]="136*2";
w["7:"]="19*1";
w["8"]="18*1,77*1,91*3,114*1,174*1,182*1,185*1,186*1";
w["8-bit"]="91*3,174*1";
w["8..0"]="18*1";
w["8..0:"]="18*1";
w["9"]="18*1,169*1,186*2";
w["94"]="136*1";
w["9:"]="18*1";
w[":"]="23*2,24*1,25*1";
w["_byte"]="185*1";
w["abil"]="139*1";
w["about"]="6*1";
w["acc"]="30*1,181*1,182*1,193*1";
w["access"]="0*3,6*1,7*1,63*3,75*1,76*1,79*1,89*1,91*39,104*1,113*4,114*4,115*4,131*7,132*13,133*12,135*1,136*1,137*1,139*2,181*11,185*1";
w["access."]="89*1,113*3,114*3,115*3,136*1";
w["accessed."]="131*1,132*3,133*2";
w["accord"]="9*1,23*1,111*1,148*2";
w["accur"]="172*1";
w["accuraci"]="171*1";
w["achiev"]="15*1";
w["action"]="49*1,72*1,73*1";
w["activ"]="52*1,62*1,64*1,68*43,69*1,70*1,87*1,88*2,89*44,92*1,96*43,97*1,98*1,134*1,135*2,136*45,138*1,140*6,182*1,185*3";
w["actual"]="15*1,60*1";
w["add"]="139*1";
w["addit"]="156*1,157*1,175*1";
w["addr"]="185*3";
w["address"]="28*2,29*2,46*2,47*2,66*2,67*2,75*2,76*2,79*3,91*9,94*2,95*2,101*1,104*3,106*1,107*1,111*5,112*7,113*3,114*3,115*3,116*3,117*3,119*3,120*3,129*1,131*1,132*5,133*13,139*1,140*9,141*1,142*54,145*1,153*1,160*3,165*4,166*2,167*2,172*1,173*2,175*1,180*1,181*19,182*3,185*38";
w["address."]="75*1,139*1";
w["adjust"]="139*1,185*1";
w["advanc"]="171*100,172*1";
w["advis"]="171*1";
w["after"]="69*1,97*1,99*1,138*4,148*1,159*1,168*1,182*3,185*2";
w["afterward"]="138*1";
w["afterwards."]="138*1";
w["again"]="89*1,136*1";
w["again."]="89*1,136*2";
w["aka"]="62*1";
w["aka:"]="62*1";
w["al"]="136*1";
w["align"]="132*1,140*3,181*1";
w["all"]="1*6,22*1,28*1,29*1,37*3,46*1,47*1,62*2,63*9,66*1,67*1,79*2,91*6,92*1,94*1,95*1,121*47,139*3,140*9,160*3,171*1,172*2,173*1,174*1,175*2,180*1,192*1";
w["all:"]="139*1";
w["alloc"]="117*1";
w["allow"]="131*1,185*1";
w["allowed."]="131*1";
w["along"]="18*1";
w["also"]="1*1,79*1,89*1,91*1,136*1,140*1,160*1,169*1,175*2,180*1,186*1";
w["alter"]="114*1,136*1";
w["altern"]="59*1";
w["although"]="172*1,175*1";
w["alway"]="109*1,131*1";
w["amount"]="131*1";
w["analog"]="63*3";
w["ani"]="0*1,37*1,51*1,52*1,62*2,70*1,89*1,98*1,104*1,111*1,115*1,131*2,132*2,133*10,135*1,136*1,171*5";
w["anoth"]="69*1,97*1";
w["api"]="7*1";
w["app"]="136*1,140*3";
w["appear"]="59*1";
w["appl_base_addr"]="133*2,185*1";
w["appli"]="51*3,61*1,63*6,68*1,89*1,91*3,96*1,112*1,113*1,114*3,115*2,116*1,119*1,120*1,132*1,182*1";
w["applic"]="111*1,112*1,133*2,140*9,165*1,171*2,175*1,185*4";
w["applications."]="185*1";
w["applicaton"]="111*1";
w["appnot"]="52*1";
w["area"]="79*1,131*1,132*1,139*1,165*1";
w["aris"]="171*1";
w["arm"]="0*1,2*1,3*4,5*41,7*1,22*1,171*2,192*1";
w["arm-bas"]="22*1,192*1";
w["arm-target"]="3*1,5*41";
w["arm."]="2*1";
w["armjtag"]="186*3";
w["array"]="135*1";
w["ascend"]="173*1";
w["ase"]="3*1";
w["ask"]="140*6";
w["asleep"]="60*1,63*3";
w["assum"]="139*1,171*1";
w["asynchron"]="22*1,105*1,115*2";
w["asynchronously."]="105*1";
w["ate"]="171*1";
w["atmegaxxxrfr2"]="132*1";
w["atmel"]="0*4,1*59,2*1,3*1,4*1,5*1,6*1,7*1,8*1,9*1,10*1,11*1,12*1,13*1,14*1,15*1,16*1,17*1,18*1,19*1,20*1,21*1,22*1,23*1,24*1,25*1,26*1,27*1,28*1,29*1,30*1,31*1,32*1,33*1,34*1,35*1,36*1,37*1,38*1,39*1,40*1,41*1,42*1,43*1,44*1,45*1,46*1,47*1,48*1,49*1,50*1,51*2,52*1,53*1,54*1,55*1,56*1,57*1,58*1,59*1,60*1,61*1,62*1,63*1,64*1,65*1,66*1,67*1,68*1,69*1,70*1,71*1,72*1,73*1,74*1,75*1,76*1,77*1,78*1,79*1,80*1,81*1,82*1,83*1,84*1,85*1,86*1,87*1,88*1,89*1,90*1,91*1,92*1,93*1,94*1,95*1,96*1,97*1,98*1,99*1,100*1,101*1,102*1,103*1,104*1,105*1,106*1,107*1,108*1,109*1,110*1,111*1,112*1,113*1,114*1,115*1,116*1,117*1,118*1,119*1,120*1,121*1,122*1,123*1,124*1,125*1,126*1,127*1,128*1,129*1,130*1,131*1,132*1,133*1,134*1,135*1,136*1,137*1,138*1,139*1,140*1,141*1,142*1,143*1,144*1,145*1,146*1,147*1,148*1,149*1,150*1,151*1,152*1,153*1,154*1,155*1,156*1,157*1,158*1,159*1,160*1,161*1,162*1,163*1,164*1,165*1,166*1,167*1,168*1,169*1,170*1,171*167,172*5,173*2,174*1,175*2,176*1,177*1,178*1,179*2,180*1,181*1,182*1,183*1,184*1,185*1,186*1,187*1,188*1,189*1,190*1,191*1,192*1,193*1";
w["atmel-ic"]="1*1";
w["atmel."]="51*1";
w["attach"]="92*1,99*42,109*1,136*1,138*2,140*6";
w["attachin"]="89*1";
w["attempt"]="131*1,132*7";
w["author"]="171*1";
w["auto"]="51*1,62*2,140*9";
w["auto-detect"]="62*2";
w["auto-eras"]="140*9";
w["automat"]="109*1,111*1,133*3,148*1,182*1";
w["automot"]="171*1";
w["avail"]="19*1,20*1,24*1,25*1,36*1,37*1,43*3,132*1,140*6,185*1,192*1";
w["avr"]="0*1,1*4,3*7,4*44,22*54,23*44,24*44,25*43,36*1,37*1,38*1,52*1,90*1,111*1,112*1,117*1,118*1,133*53,140*9,145*1,171*3,173*1,174*1,175*53,176*51,177*1,180*1,183*1,185*34";
w["avr-specif"]="4*3";
w["avr-target"]="3*1,4*41";
w["avr053"]="52*1";
w["avr069"]="175*1";
w["avr069:"]="175*1";
w["avr1606"]="52*1";
w["avr32"]="65*1,91*1,171*1,173*51,191*36";
w["avr32_awire_bas"]="91*1,182*1";
w["avr32_cmd_activate_phys"]="68*1,91*1";
w["avr32_cmd_deactivate_phys"]="69*1,91*1";
w["avr32_cmd_eras"]="71*1,91*1";
w["avr32_cmd_erase_sect"]="79*1,91*1";
w["avr32_cmd_get"]="91*1";
w["avr32_cmd_get_id"]="70*1,91*1";
w["avr32_cmd_halt"]="72*1,91*1";
w["avr32_cmd_is_protect"]="78*1,91*1";
w["avr32_cmd_queri"]="91*1";
w["avr32_cmd_read"]="75*1,91*1";
w["avr32_cmd_reset"]="73*1,91*1";
w["avr32_cmd_set"]="91*1";
w["avr32_cmd_step"]="74*1,91*1";
w["avr32_cmd_tap"]="77*1,91*1";
w["avr32_cmd_writ"]="76*1,91*1";
w["avr32_ctxt_devic"]="91*1,182*1";
w["avr32_ctxt_phys"]="91*1,182*1";
w["avr32_ctxt_sess"]="91*1,182*1";
w["avr32_ctxt_usb"]="91*1,182*1";
w["avr32_failure_awire_crc"]="91*1";
w["avr32_failure_awire_error_respons"]="91*1";
w["avr32_failure_awire_overflow"]="91*1";
w["avr32_failure_awire_rw_status"]="91*1";
w["avr32_failure_awire_set_baud_error"]="91*1";
w["avr32_failure_awire_tun"]="91*1";
w["avr32_failure_awm_error"]="91*1";
w["avr32_failure_bad_valu"]="91*1";
w["avr32_failure_config_error"]="91*1";
w["avr32_failure_cpu_debug_mode_timeout"]="91*1";
w["avr32_failure_cpu_dirty_timeout"]="91*1";
w["avr32_failure_cpu_mod"]="91*1";
w["avr32_failure_erase_error"]="91*1";
w["avr32_failure_erase_timeout"]="91*1";
w["avr32_failure_flashready_timeout"]="91*1";
w["avr32_failure_hardware_error"]="91*1";
w["avr32_failure_illegal_st"]="91*1";
w["avr32_failure_internal_response_error"]="91*1";
w["avr32_failure_invalid_address"]="91*1";
w["avr32_failure_invalid_clock_spe"]="91*1";
w["avr32_failure_invalid_data"]="91*1";
w["avr32_failure_invalid_emulator_mod"]="91*1";
w["avr32_failure_invalid_memtyp"]="91*1";
w["avr32_failure_invalid_physical_mod"]="91*1";
w["avr32_failure_invalid_s"]="91*1";
w["avr32_failure_jtagm_error"]="91*1";
w["avr32_failure_jtagm_init_error"]="91*1";
w["avr32_failure_jtagm_timeout"]="91*1";
w["avr32_failure_jtagm_was_busi"]="91*1";
w["avr32_failure_length"]="91*1";
w["avr32_failure_lock"]="91*1";
w["avr32_failure_nack"]="91*1";
w["avr32_failure_no_contact"]="91*1";
w["avr32_failure_no_device_found"]="91*1";
w["avr32_failure_no_target_pow"]="91*1";
w["avr32_failure_not_impl"]="91*1";
w["avr32_failure_not_support"]="91*1";
w["avr32_failure_ok"]="91*1";
w["avr32_failure_prog"]="91*1";
w["avr32_failure_read_busi"]="91*1";
w["avr32_failure_read_error"]="91*1";
w["avr32_failure_read_short"]="91*1";
w["avr32_failure_receive_length"]="91*1";
w["avr32_failure_receive_sync"]="91*1";
w["avr32_failure_receive_timeout"]="91*1";
w["avr32_failure_step_timeout"]="91*1";
w["avr32_failure_transmit_overflow"]="91*1";
w["avr32_failure_transmit_timeout"]="91*1";
w["avr32_failure_unknown"]="91*1";
w["avr32_failure_unsupported_hardwar"]="91*1";
w["avr32_failure_write_busi"]="91*1";
w["avr32_failure_write_error"]="91*1";
w["avr32_flash_ctrl_bas"]="91*1,182*1";
w["avr32_flash_pag"]="91*1,182*1";
w["avr32_flash_pagebyt"]="91*1,182*1";
w["avr32_memtype_block"]="91*1,181*1";
w["avr32_memtype_byt"]="91*1,181*1";
w["avr32_memtype_half_word"]="91*1,181*1";
w["avr32_memtype_internal_flash"]="91*1,181*1";
w["avr32_memtype_memory_servic"]="91*1,181*1";
w["avr32_memtype_nexus"]="91*1,181*1";
w["avr32_memtype_regfil"]="91*1,181*1";
w["avr32_memtype_sab"]="91*1,181*1";
w["avr32_memtype_sysreg"]="91*1,181*1";
w["avr32_memtype_user_pag"]="91*1,181*1";
w["avr32_phy_aw_maxbaud"]="91*1,182*1";
w["avr32_phy_awire_baud"]="91*1";
w["avr32_phy_awire_vers"]="91*1";
w["avr32_phy_daisi"]="91*1,182*1";
w["avr32_phy_ext_reset"]="91*1,182*1";
w["avr32_phy_jtag_clock"]="182*1";
w["avr32_phy_phys"]="91*1,182*1";
w["avr32_phyl_jtag_clock"]="91*1";
w["avr32_physical_interface_awir"]="91*1";
w["avr32_physical_interface_jtag"]="91*1";
w["avr32_physical_interface_non"]="91*1";
w["avr32_query_command"]="91*1,191*1";
w["avr32_query_command_vers"]="91*1";
w["avr32_query_configur"]="91*1,191*1";
w["avr32_query_read_memtyp"]="91*1,191*1";
w["avr32_query_write_memtyp"]="91*1,191*1";
w["avr32_reset_domain"]="91*1,182*1";
w["avr32_rsp_data"]="85*2,91*1";
w["avr32_rsp_fail"]="86*1,91*1";
w["avr32_rsp_id"]="83*1,91*1";
w["avr32_rsp_list"]="82*1,91*1";
w["avr32_rsp_ok"]="81*1,91*1";
w["avr32_rsp_pc"]="84*1,91*1";
w["avr32_sess_run_l"]="91*1,182*1";
w["avr32_tap_dr"]="77*1,91*1";
w["avr32_tap_ir"]="77*1,91*1";
w["avr32_usb_max_read"]="91*1,182*1";
w["avr32_usb_max_writ"]="91*1,182*1";
w["avr32gener"]="38*1,91*46,173*3";
w["avr32genericcommand"]="91*1";
w["avr32genericdevicecontext"]="91*1";
w["avr32genericfailurecod"]="91*1";
w["avr32genericmemorytyp"]="91*1";
w["avr32genericphysicalcontextparamet"]="91*1";
w["avr32genericphysicalinterfac"]="91*1";
w["avr32genericquerycontext"]="91*1";
w["avr32genericrespons"]="91*1";
w["avr32genericsessioncontextparamet"]="91*1";
w["avr32genericsetgetcontext"]="91*1";
w["avr32genericusbcontextparamet"]="91*1";
w["avr32tapcommand"]="91*1";
w["avr8"]="93*1,135*1,140*1,171*1,174*52,184*36";
w["avr8_break_cause_program"]="140*1";
w["avr8_break_cause_unknown"]="140*1";
w["avr8_config_funct"]="140*1,185*1";
w["avr8_config_vari"]="140*1,185*1";
w["avr8_ctxt_config"]="140*1,185*1";
w["avr8_ctxt_devic"]="140*1,185*1";
w["avr8_ctxt_opt"]="140*1,185*1";
w["avr8_ctxt_phys"]="140*1,185*1";
w["avr8_ctxt_sess"]="140*1,185*1";
w["avr8_ctxt_test"]="185*1";
w["avr8_failure_clock_error"]="140*1";
w["avr8_failure_collis"]="140*1";
w["avr8_failure_crc_failur"]="140*1";
w["avr8_failure_cs_error"]="140*1";
w["avr8_failure_dw_phy_error"]="140*1";
w["avr8_failure_eb_error"]="140*1";
w["avr8_failure_illegal_breakpoint"]="140*1";
w["avr8_failure_illegal_id"]="140*1";
w["avr8_failure_illegal_memory_rang"]="131*1,132*1,140*1";
w["avr8_failure_illegal_ocd_status"]="140*1";
w["avr8_failure_illegal_st"]="131*1,132*1,140*1";
w["avr8_failure_illegal_valu"]="140*1";
w["avr8_failure_invalid_address"]="131*4,132*1,140*1";
w["avr8_failure_invalid_align"]="132*1,140*1";
w["avr8_failure_invalid_clock_spe"]="140*1";
w["avr8_failure_invalid_config"]="140*1";
w["avr8_failure_invalid_memtyp"]="140*1";
w["avr8_failure_invalid_physical_st"]="140*1";
w["avr8_failure_invalid_s"]="131*3,132*1,140*1";
w["avr8_failure_jtag_bit_banger_timeout"]="140*1";
w["avr8_failure_jtag_error"]="140*1";
w["avr8_failure_jtagm_error"]="140*1";
w["avr8_failure_jtagm_init_error"]="140*1";
w["avr8_failure_jtagm_timeout"]="140*1";
w["avr8_failure_jtagm_vers"]="140*1";
w["avr8_failure_no_device_found"]="140*1";
w["avr8_failure_no_ocd_control"]="140*1";
w["avr8_failure_no_target_pow"]="140*1";
w["avr8_failure_not_attach"]="140*1";
w["avr8_failure_not_impl"]="140*1";
w["avr8_failure_not_support"]="140*1";
w["avr8_failure_nvm_dis"]="140*1";
w["avr8_failure_nvm_en"]="140*1";
w["avr8_failure_ocd_lock"]="140*1";
w["avr8_failure_ok"]="140*1";
w["avr8_failure_parity_error"]="140*1";
w["avr8_failure_pc_read_fail"]="140*1";
w["avr8_failure_pdi_en"]="140*1";
w["avr8_failure_pdi_timeout"]="140*1";
w["avr8_failure_read_error"]="140*1";
w["avr8_failure_register_read_fail"]="140*1";
w["avr8_failure_timeout"]="140*1";
w["avr8_failure_too_many_breakpoint"]="140*1";
w["avr8_failure_unknown"]="140*1";
w["avr8_failure_write_error"]="140*1";
w["avr8_failure_write_timeout"]="140*1";
w["avr8_func_debug"]="140*1,185*1";
w["avr8_func_non"]="140*1,185*1";
w["avr8_func_program"]="140*1,185*1";
w["avr8_hwbp_prog_bp"]="140*1";
w["avr8_opt_disable_dbp"]="140*1,185*1";
w["avr8_opt_enable_idr"]="140*1,185*1";
w["avr8_opt_poll_int"]="140*1,185*1";
w["avr8_opt_run_tim"]="140*1,185*1";
w["avr8_phy_dw_clk_div"]="140*1,185*1";
w["avr8_phy_interfac"]="140*1";
w["avr8_phy_intf_dw"]="140*1,185*1";
w["avr8_phy_intf_jtag"]="140*1,185*1";
w["avr8_phy_intf_non"]="140*1,185*1";
w["avr8_phy_intf_pdi"]="140*1,185*1";
w["avr8_phy_jtag_daisi"]="140*1,185*1";
w["avr8_phy_mega_dbg_clk"]="140*1,185*1";
w["avr8_phy_mega_prg_clk"]="140*1,185*1";
w["avr8_phy_phys"]="185*1";
w["avr8_phy_xm_jtag_clk"]="140*1,185*1";
w["avr8_phy_xm_pdi_clk"]="140*1,185*1";
w["avr8_query_command"]="140*1,184*1";
w["avr8_query_configur"]="140*1,184*1";
w["avr8_query_read_memtyp"]="140*1,184*1";
w["avr8_query_write_memtyp"]="140*1,184*1";
w["avr8_rsp_list"]="124*1";
w["avr8_sess_main_pc"]="140*1,185*1";
w["avr8_test_tgt_run"]="140*1,185*1";
w["avr8_variant_loopback"]="140*1,185*1";
w["avr8_variant_megaocd"]="140*1,185*1";
w["avr8_variant_non"]="140*1,185*1";
w["avr8_variant_tinyocd"]="140*1,185*1";
w["avr8_variant_xmega"]="140*1,185*1";
w["avr8gener"]="38*1,140*46,174*1";
w["avr8genericcommand"]="140*1";
w["avr8genericconfigcontextparamet"]="140*1";
w["avr8genericconfigtestparamet"]="140*1";
w["avr8genericev"]="140*1";
w["avr8genericfailurecod"]="140*1";
w["avr8genericfunctionvalu"]="140*1";
w["avr8genericmegabreakcaus"]="140*1";
w["avr8genericmegabreakpointtyp"]="140*1";
w["avr8genericmemtyp"]="140*1";
w["avr8genericoptionscontextparamet"]="140*1";
w["avr8genericphysicalcontextparamet"]="140*1";
w["avr8genericphysicalinterfac"]="140*1";
w["avr8genericquerycontext"]="140*1";
w["avr8genericrespons"]="140*1";
w["avr8genericsessioncontextparamet"]="140*1";
w["avr8genericsetgetcontext"]="140*1";
w["avr8genericvariantvalu"]="140*1";
w["avr8genericxmegaerasemod"]="140*1";
w["avr_cmd"]="3*1,4*1,22*1,23*12";
w["avr_evt"]="3*1,4*1,22*1,25*13";
w["avr_rsp"]="3*1,4*1,22*1,23*1,24*13";
w["avrisp"]="38*2,160*1,175*1,180*1";
w["avrjtag"]="186*3";
w["aw"]="186*3";
w["awak"]="60*1";
w["awar"]="136*1";
w["awir"]="1*4,38*1,70*1,78*1,88*3,90*1,91*30,182*3";
w["awire_baud_max"]="88*1";
w["awire_baud_max:"]="88*1";
w["awireid"]="68*1";
w["b"]="3*1,132*5,182*1";
w["back"]="175*1";
w["bad"]="91*3,140*6";
w["banger"]="140*3";
w["base"]="0*1,1*54,2*1,3*1,4*1,5*1,6*1,7*1,8*1,9*1,10*1,11*1,12*1,13*1,14*1,15*1,16*1,17*1,18*1,19*1,20*1,21*1,22*2,23*1,24*1,25*1,26*1,27*1,28*1,29*1,30*1,31*1,32*1,33*1,34*1,35*1,36*2,37*1,38*2,39*1,40*1,41*1,42*1,43*1,44*1,45*1,46*1,47*1,48*1,49*1,50*1,51*1,52*1,53*1,54*1,55*1,56*1,57*1,58*1,59*1,60*1,61*1,62*1,63*1,64*1,65*1,66*1,67*1,68*1,69*1,70*1,71*1,72*1,73*1,74*1,75*1,76*1,77*1,78*1,79*1,80*1,81*1,82*1,83*1,84*1,85*1,86*1,87*1,88*1,89*1,90*1,91*7,92*1,93*1,94*1,95*1,96*1,97*1,98*1,99*1,100*1,101*1,102*1,103*1,104*1,105*1,106*1,107*1,108*1,109*1,110*1,111*1,112*1,113*1,114*1,115*1,116*1,117*1,118*1,119*1,120*1,121*1,122*1,123*1,124*1,125*1,126*1,127*1,128*1,129*1,130*1,131*1,132*1,133*14,134*1,135*1,136*1,137*1,138*1,139*2,140*1,141*1,142*1,143*1,144*1,145*1,146*1,147*1,148*1,149*1,150*1,151*1,152*1,153*1,154*1,155*1,156*1,157*1,158*1,159*1,160*1,161*1,162*1,163*1,164*1,165*1,166*1,167*1,168*1,169*1,170*1,171*51,172*1,173*1,174*1,175*2,176*1,177*1,178*1,179*2,180*2,181*1,182*3,183*1,184*1,185*4,186*1,187*1,188*1,189*1,190*1,191*1,192*2,193*1";
w["basic"]="63*3,186*3";
w["baud"]="15*57,18*1,19*1,88*1,91*9,140*3,141*2,143*53,144*53,159*1,160*9,182*2,192*1";
w["baud-rat"]="15*1";
w["be"]="38*1,131*1,132*1,135*1";
w["becaus"]="148*1";
w["been"]="18*1,19*1,59*1,61*2,105*1,109*1,140*3,148*1,171*1";
w["befor"]="24*1,62*1,79*1,88*2,111*1,131*1,133*3,135*2,182*3,185*3";
w["behaviour"]="37*1,109*1";
w["behaviour."]="37*1";
w["bei"]="37*1";
w["belong"]="2*1";
w["below"]="38*1,185*1";
w["below."]="175*1";
w["beta"]="170*1";
w["between"]="0*1,1*1,145*1";
w["bidirect"]="0*1";
w["big"]="173*1,180*1";
w["bigger"]="148*1";
w["bit"]="18*4,19*3,23*1,38*1,51*1,68*1,70*1,73*1,77*5,78*1,91*15,114*1,140*9,148*9,149*2,154*1,155*1,160*6,165*1,173*1,174*1,182*2,185*3";
w["bit."]="148*1";
w["ble"]="160*1";
w["block"]="91*6,181*1,182*1";
w["board"]="0*1,1*5,3*2,9*1,10*1,172*1,178*1";
w["board."]="1*1";
w["boolean"]="79*1";
w["boot"]="101*1,111*2,112*1,133*2,140*12,185*4";
w["boot_base_addr"]="133*2,185*1";
w["boot_byt"]="185*1";
w["boot_section_start"]="185*2";
w["both"]="77*1";
w["break"]="99*2,101*1,102*1,104*1,105*1,109*1,117*2,119*1,128*1,129*47,138*1,139*1,140*12,185*1";
w["breakpoint"]="92*5,104*3,117*51,118*51,119*48,120*48,121*47,129*1,131*2,132*1,139*6,140*21";
w["breakpoint."]="104*1";
w["breakpoints."]="117*2,118*2,139*1,185*1";
w["bsi"]="147*1,148*3";
w["buffer"]="18*1,19*1,20*54,22*1,91*3,148*1,153*1,192*1";
w["buffer."]="148*1";
w["build"]="15*1,63*3,193*2";
w["bulk"]="0*1";
w["bus"]="91*3";
w["busi"]="91*12,160*3,171*1";
w["but"]="59*1,62*1,70*1,98*1,150*1,151*1,154*1,155*1,156*1,157*1,171*1,182*1";
w["byte"]="6*3,9*12,10*7,13*6,14*6,15*6,16*5,17*5,18*8,19*8,20*5,21*5,23*7,24*6,25*5,27*3,28*8,29*7,30*3,32*2,33*3,34*4,35*3,37*17,40*3,41*3,42*3,43*3,45*3,46*8,47*7,49*2,50*3,51*3,52*2,54*2,55*3,56*4,57*3,59*3,60*3,61*3,65*3,66*8,67*7,68*3,69*2,70*2,71*2,72*3,73*3,74*2,75*7,76*8,77*5,78*2,79*6,81*2,82*3,83*3,84*3,85*7,86*3,91*6,93*3,94*8,95*7,96*3,97*2,98*2,99*3,100*2,101*3,102*3,103*2,104*3,105*4,106*2,107*3,108*2,109*2,110*2,111*5,112*7,113*8,114*10,115*10,116*4,117*6,118*3,119*4,120*4,121*2,123*2,124*3,125*3,126*4,127*3,129*5,130*3,131*7,132*6,133*10,140*6,142*4,143*4,144*4,145*20,146*5,147*13,148*24,149*10,152*11,153*17,156*1,157*2,160*12,162*3,163*3,164*5,165*5,166*9,167*8,173*3,180*1,181*18,182*15,184*4,185*85,187*1,188*2,189*1,190*3,191*4,193*5";
w["byte."]="173*1";
w["byte:"]="145*1,148*1";
w["bytedelay"]="145*2";
w["bytedelay."]="145*1";
w["bytes."]="75*1,76*1";
w["c"]="132*6,171*1";
w["c0"]="136*2";
w["cach"]="70*1,98*1,136*1";
w["cached."]="89*1";
w["calcul"]="112*1,139*1,140*3";
w["calibr"]="48*1,52*38,63*3,133*1,140*6";
w["call"]="7*1,15*1,27*1,28*2,29*2,40*1,45*1,46*2,47*2,52*1,65*1,66*2,67*2,75*1,76*1,79*1,88*2,89*1,93*1,94*2,95*2,113*1,114*1,115*1,133*12,135*2,136*1,175*1";
w["called."]="78*1";
w["calls."]="175*1";
w["cannot"]="104*1";
w["capabl"]="1*3,27*1,36*1,40*1,43*3,45*1,63*3,65*1,89*1,91*3,93*1,136*1,140*3";
w["captur"]="3*1,14*53,15*1,16*1,17*1,18*1,77*1,192*4";
w["capture_off"]="186*1";
w["capture_uart"]="186*1";
w["cas"]="59*1";
w["case"]="89*1,131*1,132*1,135*1,136*2,137*1,138*1,148*1";
w["case."]="131*1,132*1,137*1";
w["caus"]="110*1,129*1";
w["cdc"]="0*2,1*2,186*3";
w["ceas"]="49*1";
w["ceil"]="182*1";
w["chain"]="48*1,51*42,62*2,88*2,91*3,140*3,182*1,185*1";
w["chang"]="135*1,171*1,172*1";
w["channel."]="188*1";
w["check"]="21*1,62*1,78*1,91*3";
w["checksum"]="112*1";
w["chip"]="1*2,71*1,111*2,140*6,141*1,147*52,165*1";
w["chop"]="23*1";
w["cleanup"]="37*1";
w["clear"]="92*3,118*48,120*48,121*46,139*3,140*9,148*1,165*2";
w["clear:"]="139*2";
w["cleared."]="148*1";
w["clock"]="52*1,63*3,78*2,88*1,91*6,135*2,140*18,143*1,144*1,159*1,182*1,185*6";
w["cmd"]="24*1,39*1,40*41,44*3,45*41,46*41,47*41";
w["cmd-rsp"]="24*1";
w["cmd1"]="145*1,147*1,148*1,149*1,152*1,153*1";
w["cmd2"]="145*1,147*1,148*1,152*1,153*1";
w["cmd3"]="145*1,147*1,148*1,152*1,153*1";
w["cmd4"]="145*1,147*1,152*1,153*1";
w["cmd:"]="39*1,40*41,44*3,45*41,46*41,47*41";
w["cmd_activate_phys"]="175*1,180*1";
w["cmd_activate_physical."]="175*1,180*1";
w["cmd_avr8_activate_phys"]="140*1";
w["cmd_avr8_attach"]="99*1,140*1";
w["cmd_avr8_crc"]="112*1,140*1";
w["cmd_avr8_deactivate_phys"]="97*1,140*1";
w["cmd_avr8_detach"]="100*1,140*1";
w["cmd_avr8_disable_debugwir"]="110*1,140*1";
w["cmd_avr8_eras"]="111*1,140*1";
w["cmd_avr8_get"]="140*1";
w["cmd_avr8_get_id"]="98*1,140*1";
w["cmd_avr8_hw_break_clear"]="140*1";
w["cmd_avr8_hw_break_clr"]="118*1";
w["cmd_avr8_hw_break_set"]="117*1,140*1";
w["cmd_avr8_memory_read"]="113*1,114*1,140*1";
w["cmd_avr8_memory_read_mask"]="140*1";
w["cmd_avr8_memory_writ"]="115*1,140*1";
w["cmd_avr8_page_eras"]="116*1,140*1";
w["cmd_avr8_pc_read"]="106*1,140*1";
w["cmd_avr8_pc_writ"]="107*1,140*1";
w["cmd_avr8_prog_mode_ent"]="108*1,140*1";
w["cmd_avr8_prog_mode_leav"]="109*1,140*1";
w["cmd_avr8_queri"]="140*1";
w["cmd_avr8_reset"]="101*1,140*1";
w["cmd_avr8_run"]="103*1,140*1";
w["cmd_avr8_run_to_address"]="104*1,140*1";
w["cmd_avr8_set"]="140*1";
w["cmd_avr8_step"]="105*1,140*1";
w["cmd_avr8_stop"]="102*1,140*1";
w["cmd_avr8_sw_break_clear"]="140*1";
w["cmd_avr8_sw_break_clear_al"]="140*1";
w["cmd_avr8_sw_break_set"]="140*1";
w["cmd_avr8_sw_breakpoint_clr"]="120*1";
w["cmd_avr8_sw_breakpoint_clr_al"]="121*1";
w["cmd_avr8_sw_breakpoint_set"]="119*1";
w["cmd_edbg_get"]="186*1";
w["cmd_edbg_queri"]="186*1";
w["cmd_edbg_set"]="186*1";
w["cmd_end_sess"]="62*1";
w["cmd_housekeeping_cal_osc"]="52*1,63*1";
w["cmd_housekeeping_end_sess"]="49*1,63*1";
w["cmd_housekeeping_fw_upgrad"]="50*1,63*1";
w["cmd_housekeeping_get"]="63*1";
w["cmd_housekeeping_jtag_detect"]="51*1,63*1";
w["cmd_housekeeping_queri"]="63*1";
w["cmd_housekeeping_set"]="63*1";
w["cmd_housekeeping_start_sess"]="63*1,88*1,135*1,188*1";
w["cmd_load_address"]="175*1";
w["cmd_queri"]="43*1";
w["cmd_start_sess"]="62*1";
w["cmdexedelay"]="145*2";
w["cmdexedelay."]="145*1";
w["cmsis"]="0*4,1*1,2*47,3*47,24*1,171*1,177*53";
w["cmsis-dap"]="0*4,1*1,2*47,3*47,24*1,171*1,177*53";
w["co"]="1*1";
w["code"]="13*1,14*1,16*1,17*1,34*1,35*2,37*1,42*2,43*3,52*1,56*2,57*2,85*1,86*2,89*1,91*1,126*1,127*2,131*1,132*1,135*1,136*1,137*1,140*4,160*1,169*3,186*4";
w["code."]="56*1,135*1";
w["codes."]="91*1,140*1,160*1,169*1,186*1";
w["collis"]="140*3,168*1";
w["com"]="0*3,1*2,172*1";
w["combin"]="171*1";
w["command"]="0*1,3*62,4*48,5*41,6*5,7*1,8*41,9*5,10*5,22*54,23*43,24*2,26*46,27*2,28*2,29*2,32*1,35*1,37*6,38*3,39*1,40*2,42*1,43*9,44*1,45*2,46*2,47*2,48*41,49*7,50*7,51*7,52*8,54*1,57*1,63*12,64*46,65*2,66*2,67*2,68*7,69*8,70*7,71*7,72*7,73*2,74*7,75*7,76*7,77*9,78*8,79*9,81*1,86*1,87*1,88*1,89*5,90*42,91*16,92*46,93*2,94*2,95*2,96*7,97*8,98*7,99*7,100*7,101*7,102*7,103*7,104*7,105*7,106*7,107*7,108*7,109*8,110*7,111*7,112*8,113*7,114*7,115*7,116*7,117*7,118*7,119*8,120*8,121*7,123*1,127*1,130*5,135*1,136*6,137*1,138*5,139*2,140*13,141*46,142*8,143*7,144*7,145*13,146*7,147*12,148*18,149*8,150*1,151*1,152*11,153*11,154*1,155*1,156*2,157*2,158*1,159*5,160*10,161*46,162*7,163*7,164*7,165*7,166*7,167*7,168*3,169*4,172*1,173*1,174*1,175*12,177*3,178*1,180*4,184*1,186*4,187*1,188*7,189*1,190*1,191*1,192*48";
w["command."]="22*3,39*1,69*1,70*1,89*1,97*1,98*1,136*1,138*2,139*1,142*1,145*1,159*1,168*1,175*1";
w["command_handl"]="190*1";
w["commandpacket"]="23*1";
w["commands."]="88*1,135*1,175*1,180*1";
w["comment"]="170*1";
w["commit"]="171*1";
w["communic"]="22*3,36*1,50*1,139*1,171*1,172*1,175*3,176*51,180*1";
w["complet"]="89*2,105*1,131*2,132*1,135*1,136*2,138*1,146*1,148*1,163*1,171*1,181*1";
w["complete."]="131*1";
w["compon"]="171*1,172*1";
w["composit"]="0*1";
w["comput"]="13*1";
w["computer."]="13*1";
w["con"]="79*1";
w["condit"]="37*1";
w["config"]="9*2,63*3,91*3,109*1,135*2,140*3";
w["configid"]="9*2,10*1";
w["configpacket"]="9*1";
w["configur"]="3*2,8*2,9*54,10*49,38*2,87*1,88*43,89*1,91*3,133*12,134*1,135*44,136*1,138*1,140*18,165*1,178*1,182*1,184*1,186*6,191*1,192*1";
w["confus"]="23*1";
w["connect"]="0*1,69*1,89*2,91*3,97*1,136*2,140*3,145*1,171*2";
w["consequenti"]="171*1";
w["consist"]="0*1";
w["constant"]="148*1";
w["contact"]="68*1,96*1";
w["contain"]="3*1,38*1,68*1,89*1,131*1,132*1,136*1,153*1,171*1";
w["content"]="3*5,5*5,8*5,22*5,26*5,27*5,29*5,31*5,39*5,44*5,48*5,53*5,58*5,64*5,65*5,67*5,76*5,80*5,87*5,92*5,93*5,95*5,122*5,128*5,134*5,141*5,161*5,171*6,172*1,173*5,174*5,175*5,176*5,177*5,178*5,179*5,180*5,183*5,185*5,192*5";
w["context"]="27*3,28*3,29*3,30*1,39*1,40*2,44*1,45*2,46*3,47*3,65*3,66*3,67*3,93*3,94*3,95*3,109*1,133*12,135*3,140*3,182*1,184*41,185*110,187*41,189*46,190*46,191*41,193*1";
w["context."]="28*1,29*1,46*1,47*1,66*1,67*1,94*1,95*1";
w["context:"]="133*12,185*108";
w["contigu"]="79*1";
w["continu"]="88*1,99*1,135*1,138*1,185*1";
w["control"]="3*1,38*1,62*1,79*1,90*1,91*12,110*2,119*1,120*1,134*3,137*41,138*41,139*45,140*6,148*1,171*1,178*52,182*1,185*2";
w["core"]="185*1";
w["core."]="185*1";
w["coresight"]="0*1";
w["corpor"]="171*4";
w["corporation."]="171*1";
w["correct"]="133*12,135*1";
w["could"]="59*1";
w["count"]="9*1,10*1";
w["counter"]="84*2,107*1,125*2,129*1,139*1,185*1";
w["countries."]="171*2";
w["cpu"]="91*9";
w["crc"]="91*3,92*1,112*44,139*2,140*6";
w["crc:"]="139*1";
w["current"]="13*1,14*1,91*3,144*1";
w["custom"]="0*1,1*1,3*1";
w["cyc"]="110*1";
w["d"]="132*2,148*1";
w["daisi"]="88*2,91*3,140*3,182*1,185*1";
w["damag"]="171*3";
w["damages."]="171*1";
w["dap"]="0*4,1*1,2*47,3*47,24*1,171*1,177*53";
w["data"]="0*2,1*1,9*5,10*2,13*2,14*2,15*1,16*1,17*1,18*55,24*1,25*1,28*2,29*1,31*1,33*3,34*45,37*4,41*3,46*2,47*1,53*1,55*3,56*40,63*3,66*2,67*1,68*1,75*1,76*2,77*5,78*1,80*1,82*3,85*47,91*15,94*2,95*1,96*1,98*1,112*1,113*1,114*1,115*1,122*1,124*3,126*45,133*2,135*1,136*1,140*15,148*4,149*3,150*1,151*1,153*2,160*12,166*3,167*3,185*4,192*3";
w["data."]="15*1";
w["data_base_addr"]="133*1,185*1";
w["datasheet"]="83*1";
w["datatyp"]="9*2";
w["date"]="43*3,170*1,190*1";
w["dbg_en"]="186*3";
w["de"]="69*1,97*1";
w["de-activ"]="69*1,97*1";
w["deactiv"]="64*1,69*41,87*1,89*42,92*1,97*41,109*1,134*1,136*44,137*2,175*1,180*1";
w["deactivated."]="89*1,136*2,137*1";
w["debounc"]="59*1";
w["debug"]="0*4,1*6,22*1,30*1,38*3,87*1,90*42,91*6,99*1,100*1,109*2,116*1,131*6,132*4,133*12,134*1,135*1,136*1,138*43,139*1,140*9,172*1,173*1,174*1,185*3";
w["debugg"]="0*1,1*2,69*1,90*1,97*1,109*1,172*1,185*1";
w["debuggers."]="1*1";
w["debugging."]="131*1,132*1";
w["debugwir"]="1*2,38*1,92*1,110*49,111*1,117*1,118*1,131*42,136*1,140*15,183*1,185*37";
w["decid"]="148*1";
w["decod"]="37*2";
w["default"]="13*1,14*1,89*1,136*1";
w["defin"]="0*1,3*1,91*2,140*2,160*2,169*21,172*1,186*2";
w["definit"]="28*1,29*1,39*47,43*41,44*1,63*41,91*46,140*49,160*46,169*46,173*1,174*1,175*1,176*1,178*1,180*1,186*46";
w["delay"]="145*3,146*2,147*2,148*6";
w["delay."]="148*1";
w["demand"]="89*1,136*1";
w["depend"]="9*1,36*1,73*1,75*1,76*1,96*1,131*1,132*1";
w["deploy"]="135*1";
w["deprec"]="101*1,102*1,105*3,185*1";
w["des"]="109*1";
w["describ"]="1*3,9*1,22*1,25*1,52*1,90*1,159*1,168*1,175*2,192*1";
w["descript"]="3*1,4*1,6*2,9*3,10*2,13*2,14*2,15*2,16*2,17*2,18*2,19*2,20*2,21*2,23*2,24*2,25*2,27*1,28*1,29*1,30*1,32*1,33*1,34*1,35*1,38*1,40*1,41*1,42*1,45*1,46*1,47*1,49*1,50*1,51*1,52*1,54*1,55*1,56*1,57*1,59*1,60*1,61*1,65*1,66*1,67*1,68*1,69*1,70*1,71*1,72*1,73*1,74*1,75*1,76*1,77*1,78*1,79*1,81*1,82*1,83*1,84*1,85*2,86*1,93*1,94*1,95*1,96*1,97*1,98*1,99*1,100*1,101*1,102*1,103*1,104*1,105*1,106*1,107*1,108*1,109*1,110*1,111*1,112*1,113*1,114*1,115*1,116*1,117*1,118*1,119*1,120*1,121*1,123*1,124*1,125*1,126*1,127*1,129*1,130*1,142*2,143*2,144*2,145*2,146*2,147*2,148*3,149*2,152*2,153*2,159*1,162*2,163*2,164*2,165*3,166*2,167*2,168*1,171*1,181*1,182*1,184*1,185*4,187*1,188*1,189*1,190*1,191*1,193*1";
w["destin"]="37*1";
w["detach"]="92*1,100*41,136*1,138*1,140*3";
w["detail"]="7*1,23*1,172*1";
w["detect"]="48*1,51*43,62*4,63*3,68*1,89*1,96*1,135*1,136*1,140*3,168*1";
w["detected."]="135*1";
w["determin"]="148*1";
w["develop"]="172*1";
w["devic"]="0*2,1*8,6*2,7*1,10*1,18*1,22*2,30*1,38*1,51*4,63*9,68*2,70*1,73*2,78*1,79*1,83*3,89*4,90*1,91*9,96*1,113*1,114*1,115*1,131*1,132*3,133*1,135*4,136*5,140*21,148*3,153*1,160*6,173*1,174*1,175*1,180*2,182*3,185*96,192*1";
w["device-specif"]="135*1";
w["device."]="73*1,182*1";
w["device:"]="135*1";
w["devices."]="51*1";
w["dgi"]="0*2";
w["dgi."]="0*1";
w["did"]="91*3,140*6,159*1";
w["differ"]="1*1,91*2,131*1,140*2,148*3,160*2,169*2,186*2";
w["direct"]="11*1,12*1,62*1,79*1,89*1,136*1,171*1";
w["directly."]="62*1,79*1";
w["disabl"]="18*2,19*2,30*6,89*1,92*1,110*42,136*1,140*6,185*1";
w["disast"]="91*6,140*3";
w["disaster."]="140*3";
w["discard"]="91*3";
w["disclaim"]="171*2";
w["disclaimer:"]="171*1";
w["disconnect"]="69*1,89*1,91*3,97*1,136*1,140*3";
w["discov"]="27*1,38*1,40*1,45*1,65*1,93*1";
w["discoveri"]="38*1,39*49,43*44,63*3,91*3,140*3,176*1,190*41";
w["discovery_command_handl"]="43*1";
w["discovery_failed_not_support"]="43*1";
w["discovery_mnf_d"]="43*1";
w["discovery_serial_numb"]="43*1";
w["discovery_tool_nam"]="43*1";
w["divid"]="140*3";
w["divis"]="185*1";
w["do"]="37*1,135*1";
w["document"]="1*1,90*1,170*52,171*5,172*3";
w["document."]="90*1";
w["doe"]="0*1,51*1,56*1,111*1,131*1,137*1,171*1";
w["domain"]="73*1,91*3,182*1";
w["done"]="28*1,29*1,37*1,46*1,47*1,62*1,66*1,67*1,69*1,79*1,90*2,94*1,95*1,97*1,101*1,116*1,138*2";
w["dor"]="22*1";
w["down"]="0*1";
w["dr"]="77*2";
w["dummi"]="140*3";
w["dure"]="68*1,78*1,88*1,89*1,96*1,109*1,115*1,116*1,131*4,132*3,135*2,136*2,138*1,140*3,185*1";
w["dw"]="186*3";
w["dwen"]="110*1";
w["e"]="132*2,136*1,153*1,173*1,180*1";
w["e.g."]="180*1";
w["earlier"]="1*1";
w["echo"]="37*1";
w["edbg"]="0*53,1*55,2*1,3*12,4*1,5*1,6*2,7*1,8*44,9*2,10*1,11*37,12*37,13*1,14*1,15*1,16*1,17*1,18*1,19*1,20*1,21*1,22*1,23*2,24*2,25*2,26*1,27*2,28*1,29*1,30*1,31*1,32*1,33*1,34*1,35*1,36*2,37*2,38*3,39*1,40*1,41*1,42*1,43*1,44*1,45*1,46*1,47*1,48*1,49*1,50*1,51*1,52*1,53*1,54*1,55*1,56*1,57*1,58*1,59*1,60*1,61*1,62*1,63*1,64*1,65*1,66*1,67*1,68*1,69*1,70*1,71*1,72*1,73*1,74*1,75*1,76*1,77*1,78*1,79*1,80*1,81*1,82*1,83*1,84*1,85*1,86*1,87*1,88*1,89*1,90*1,91*1,92*1,93*1,94*1,95*1,96*1,97*1,98*1,99*1,100*1,101*1,102*1,103*1,104*1,105*1,106*1,107*1,108*1,109*1,110*1,111*1,112*1,113*1,114*1,115*1,116*1,117*1,118*1,119*1,120*1,121*1,122*1,123*1,124*1,125*1,126*1,127*1,128*1,129*1,130*1,131*1,132*1,133*1,134*1,135*1,136*1,137*1,138*1,139*1,140*1,141*1,142*1,143*1,144*1,145*1,146*1,147*1,148*3,149*1,150*1,151*1,152*1,153*1,154*1,155*1,156*1,157*1,158*1,159*1,160*1,161*1,162*1,163*1,164*1,165*1,166*1,167*1,168*1,169*1,170*1,171*152,172*6,173*1,174*1,175*3,176*1,177*1,178*55,179*3,180*3,181*1,182*1,183*1,184*1,185*1,186*7,187*37,188*1,189*1,190*1,191*1,192*1,193*1";
w["edbg-b"]="3*1";
w["edbg-bas"]="0*1,1*52,2*1,3*1,4*1,5*1,6*1,7*1,8*1,9*1,10*1,11*1,12*1,13*1,14*1,15*1,16*1,17*1,18*1,19*1,20*1,21*1,22*1,23*1,24*1,25*1,26*1,27*1,28*1,29*1,30*1,31*1,32*1,33*1,34*1,35*1,36*1,37*1,38*1,39*1,40*1,41*1,42*1,43*1,44*1,45*1,46*1,47*1,48*1,49*1,50*1,51*1,52*1,53*1,54*1,55*1,56*1,57*1,58*1,59*1,60*1,61*1,62*1,63*1,64*1,65*1,66*1,67*1,68*1,69*1,70*1,71*1,72*1,73*1,74*1,75*1,76*1,77*1,78*1,79*1,80*1,81*1,82*1,83*1,84*1,85*1,86*1,87*1,88*1,89*1,90*1,91*1,92*1,93*1,94*1,95*1,96*1,97*1,98*1,99*1,100*1,101*1,102*1,103*1,104*1,105*1,106*1,107*1,108*1,109*1,110*1,111*1,112*1,113*1,114*1,115*1,116*1,117*1,118*1,119*1,120*1,121*1,122*1,123*1,124*1,125*1,126*1,127*1,128*1,129*1,130*1,131*1,132*1,133*1,134*1,135*1,136*1,137*1,138*1,139*1,140*1,141*1,142*1,143*1,144*1,145*1,146*1,147*1,148*1,149*1,150*1,151*1,152*1,153*1,154*1,155*1,156*1,157*1,158*1,159*1,160*1,161*1,162*1,163*1,164*1,165*1,166*1,167*1,168*1,169*1,170*1,171*51,173*1,174*1,175*1,176*1,177*1,178*1,179*2,180*1,181*1,182*1,183*1,184*1,185*1,186*1,187*1,188*1,189*1,190*1,191*1,192*1,193*1";
w["edbg-specif"]="3*1,8*41";
w["edbg."]="0*2,38*1,172*1";
w["edbg@atmel"]="172*1";
w["<EMAIL>"]="172*1";
w["edbg_config_board_nam"]="186*1";
w["edbg_config_board_typ"]="186*1";
w["edbg_config_dgi_gpio_map"]="186*1";
w["edbg_config_extension_curr"]="186*1";
w["edbg_config_extension_manufactur"]="186*1";
w["edbg_config_extension_map"]="186*1";
w["edbg_config_extension_max_voltag"]="186*1";
w["edbg_config_extension_min_voltag"]="186*1";
w["edbg_config_extension_product"]="186*1";
w["edbg_config_extension_revis"]="186*1";
w["edbg_config_extension_seri"]="186*1";
w["edbg_config_extension_status"]="186*1";

