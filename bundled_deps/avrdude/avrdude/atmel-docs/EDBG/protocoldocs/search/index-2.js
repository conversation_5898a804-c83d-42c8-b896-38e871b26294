//Auto generated index for searching by xsl-webhelpindexer for DocBook Webhelp.# <PERSON><PERSON>, University of Moratuwa
w["edbg_config_family_nam"]="186*1";
w["edbg_config_interfac"]="186*1";
w["edbg_config_kit_data"]="186*1";
w["edbg_config_manufacturer_nam"]="186*1";
w["edbg_config_serial_numb"]="186*1";
w["edbg_config_target_chipid"]="186*1";
w["edbg_config_target_jtagid"]="186*1";
w["edbg_config_target_nam"]="186*1";
w["edbg_config_target_signatur"]="186*1";
w["edbg_config_tv"]="186*1";
w["edbg_config_usb_id"]="186*1";
w["edbg_config_vers"]="186*1";
w["edbg_config_version_minor"]="186*1";
w["edbg_configtype_array"]="186*1";
w["edbg_configtype_bit"]="186*1";
w["edbg_configtype_char"]="186*1";
w["edbg_configtype_short"]="186*1";
w["edbg_configtype_str"]="186*1";
w["edbg_configtype_word"]="186*1";
w["edbg_context_config0"]="186*1";
w["edbg_context_config1"]="186*1";
w["edbg_context_control"]="186*1";
w["edbg_control_ext_prog"]="186*1";
w["edbg_control_led_usag"]="186*1";
w["edbg_control_target_pow"]="186*1";
w["edbg_ctrl"]="38*1";
w["edbg_ctrl_ext_prog"]="30*1";
w["edbg_ctrl_led_usag"]="30*1";
w["edbg_ctrl_target_pow"]="30*1";
w["edbg_ctxt_ctrl"]="30*1";
w["edbg_failed_flash_writ"]="186*1";
w["edbg_failed_illegal_act"]="186*1";
w["edbg_failed_illegal_flag"]="186*1";
w["edbg_failed_illegal_frequ"]="186*1";
w["edbg_failed_illegal_gpio_mod"]="186*1";
w["edbg_failed_illegal_gpio_pin"]="186*1";
w["edbg_failed_illegal_interv"]="186*1";
w["edbg_failed_illegal_max_threshold"]="186*1";
w["edbg_failed_illegal_min_threshold"]="186*1";
w["edbg_failed_illegal_mod"]="186*1";
w["edbg_failed_illegal_voltage_rang"]="186*1";
w["edbg_failed_not_support"]="186*1";
w["edbg_failed_ok"]="186*1";
w["edbg_failed_overflow"]="186*1";
w["edbg_failed_unknown"]="186*1";
w["edbg_get"]="11*1";
w["edbg_if_cdc"]="186*1";
w["edbg_if_dbg_armjtag"]="186*1";
w["edbg_if_dbg_avrjtag"]="186*1";
w["edbg_if_dbg_aw"]="186*1";
w["edbg_if_dbg_dw"]="186*1";
w["edbg_if_dbg_en"]="186*1";
w["edbg_if_dbg_isp"]="186*1";
w["edbg_if_dbg_pdi"]="186*1";
w["edbg_if_dbg_swd"]="186*1";
w["edbg_if_dgi_gpio"]="186*1";
w["edbg_if_dgi_spi"]="186*1";
w["edbg_if_dgi_twi"]="186*1";
w["edbg_if_dgi_uart"]="186*1";
w["edbg_if_dgi_usart"]="186*1";
w["edbg_if_erase_pin"]="186*1";
w["edbg_query_command"]="186*1,187*1";
w["edbg_rsp_data"]="34*1";
w["edbg_rsp_fail"]="35*1";
w["edbg_rsp_list"]="33*1";
w["edbg_rsp_ok"]="32*1";
w["edbg_set"]="12*1";
w["edbgcommand"]="186*1";
w["edbgconfigtag"]="186*1";
w["edbgconfigtyp"]="186*1";
w["edbgcontrolcontextparamet"]="186*1";
w["edbgctrl"]="11*1,12*1,178*1,186*47";
w["edbgfailurecod"]="186*1";
w["edbginterfac"]="186*1";
w["edbgquerycontext"]="186*1";
w["edbgrespons"]="186*1";
w["edbgsetgetcontext"]="186*1";
w["edg"]="61*2";
w["eear"]="185*8";
w["eearh"]="185*2";
w["eearh_addr"]="185*2";
w["eearl"]="185*2";
w["eearl_addr"]="185*2";
w["eecr"]="185*2";
w["eecr_addr"]="185*2";
w["eedr"]="185*2";
w["eedr_addr"]="185*2";
w["eeprom"]="111*2,131*1,132*2,133*2,140*15,141*2,148*1,150*42,151*42,160*6,185*7";
w["eeprom_base_addr"]="133*2,185*1";
w["eeprom_page_s"]="185*3";
w["eeprom_s"]="185*3";
w["effici"]="131*1";
w["effort"]="172*1";
w["eg"]="135*1";
w["eg:"]="135*1";
w["element"]="88*1,135*1";
w["els"]="68*1";
w["embed"]="0*2,172*2,178*1";
w["embedded."]="0*1,172*1,178*1";
w["ement"]="139*1";
w["ement-level"]="139*1";
w["empti"]="140*3";
w["enabl"]="30*6,50*1,51*3,140*9,171*1,175*1,180*1,185*2";
w["encod"]="18*1,19*1";
w["encount"]="115*1";
w["end"]="48*1,49*36,112*1,135*1,148*1";
w["endian"]="173*2";
w["endian."]="180*1";
w["endpoints."]="0*1";
w["enough"]="18*1,19*1,140*3";
w["ensur"]="89*1,136*1,147*1";
w["enter"]="60*1,63*3,92*1,99*1,108*47,140*3,141*1,145*52,160*3,161*1,162*52,185*2";
w["enter_progmod"]="175*1,180*1";
w["entered."]="185*1";
w["enterprogmod"]="145*2";
w["entir"]="79*1,112*1,140*3";
w["enum"]="43*5,63*12,91*12,140*18,160*2,186*13";
w["envelop"]="3*5,4*3,22*46,23*2,24*1,25*1,177*1";
w["equival"]="136*1";
w["equivalent."]="90*1";
w["eras"]="3*1,5*1,6*42,64*2,71*42,79*50,91*12,92*2,111*54,116*48,133*3,139*2,140*39,141*1,147*53,160*3,161*1,165*62,169*3";
w["erase."]="79*1";
w["erase:"]="139*1";
w["erase_pin"]="3*1,6*2";
w["erasedelay"]="147*2";
w["erasedelay."]="147*1";
w["error"]="13*1,14*1,16*1,17*1,18*3,19*3,37*2,43*3,52*1,62*1,63*12,91*36,115*1,131*5,132*17,140*21,159*2,160*15,169*3,172*1";
w["error_overflow"]="186*1";
w["error_receiv"]="186*1";
w["es"]="59*1";
w["es."]="59*1";
w["especi"]="185*1";
w["essenti"]="148*1";
w["estoppel"]="171*1";
w["etc"]="62*1,137*1";
w["etc."]="62*1,137*1";
w["even"]="24*1,171*1,185*1";
w["event"]="3*1,4*1,22*48,25*47,44*1,58*44,59*45,60*44,61*43,63*9,101*1,102*1,104*1,105*1,109*1,128*48,129*48,130*43,139*1,140*4,160*1,169*1,171*1,174*1,177*1,185*1,188*2";
w["event:"]="58*3,59*36,60*36,61*36,128*2,129*41,130*41";
w["events."]="49*1";
w["everi"]="24*1";
w["evt_avr8_break"]="129*1,140*1";
w["evt_avr8_idr"]="130*1,140*1";
w["evt_housekeeping_ext_reset"]="61*1,63*1";
w["evt_housekeeping_pow"]="59*1,63*1";
w["evt_housekeeping_sleep"]="60*1,63*1";
w["exact"]="131*1";
w["exampl"]="89*1,114*1,136*2,185*1";
w["except"]="171*1";
w["exclus"]="62*1";
w["execut"]="27*1,32*1,35*1,40*1,42*1,43*3,45*1,54*1,57*1,62*1,63*3,65*1,81*1,86*1,91*6,93*1,102*1,103*1,104*1,123*1,127*1,137*1,138*3,139*1,140*6,145*1,159*2,168*2";
w["execute."]="137*1";
w["executed."]="119*1,120*1,138*1";
w["exist"]="85*1";
w["exists:"]="85*1";
w["exit"]="60*1,109*1";
w["expect"]="37*1,89*1,136*1";
w["expected."]="89*1,136*1";
w["explicit"]="78*1";
w["express"]="171*2";
w["extend"]="129*1,172*1";
w["extern"]="30*3,51*2,58*1,61*39,63*9,68*2,89*3,91*3,96*2,136*3";
w["extract"]="175*1";
w["fact"]="34*1,56*1,85*1,126*1";
w["factor"]="185*1";
w["fail"]="6*1,9*1,10*1,27*1,28*1,29*1,31*1,35*42,37*1,39*1,40*1,42*42,43*3,45*1,46*1,47*1,49*1,50*1,51*1,52*1,53*1,57*37,63*9,65*1,66*1,67*1,68*2,69*1,70*1,71*1,72*1,73*1,74*1,75*1,76*1,77*1,78*1,79*1,80*1,86*42,89*1,91*3,93*1,94*1,95*1,96*2,97*1,98*1,99*1,100*1,101*1,102*1,103*1,104*1,105*1,106*1,107*1,108*1,109*1,110*1,111*1,112*1,113*1,114*1,115*1,116*1,117*1,118*1,119*1,120*1,121*1,122*1,127*42,140*18,160*3,168*1,188*1";
w["failed."]="89*1,136*1";
w["failur"]="35*2,37*1,42*2,43*6,57*2,78*1,86*2,91*4,127*2,139*1,140*7,160*1,169*1,186*4";
w["failure_ok"]="43*1";
w["failure_unknown"]="43*1";
w["failure_usb_previous_underrun"]="37*1,43*1";
w["fall"]="61*1";
w["famili"]="0*1,51*1,139*1,140*3,185*1";
w["families."]="0*1";
w["family."]="111*1";
w["far"]="175*1";
w["fast"]="18*1,19*1";
w["featur"]="37*2,38*1,91*3,140*3";
w["fell"]="63*3";
w["few"]="43*3,91*3,140*3";
w["field"]="6*2,9*3,10*2,13*2,14*2,15*2,16*2,17*2,18*2,19*2,20*2,21*2,23*3,24*2,25*2,27*1,28*2,29*2,32*1,33*1,34*1,35*1,37*5,38*2,40*1,41*1,42*1,45*1,46*2,47*2,49*1,50*1,51*1,52*1,54*1,55*1,56*1,57*1,59*1,60*1,61*1,65*1,66*2,67*2,68*1,69*1,70*1,71*1,72*1,73*1,74*1,75*1,76*1,77*1,78*1,79*1,81*1,82*1,83*1,84*1,85*2,86*1,93*1,94*2,95*2,96*1,97*1,98*1,99*1,100*1,101*1,102*1,103*1,104*1,105*1,106*1,107*1,108*1,109*1,110*1,111*1,112*1,113*1,114*1,115*1,116*1,117*1,118*1,119*1,120*1,121*1,123*1,124*1,125*1,126*1,127*1,129*1,130*1,142*2,143*2,144*2,145*2,146*2,147*2,148*2,149*2,152*2,153*2,162*2,163*2,164*2,165*2,166*2,167*2,180*1,188*1";
w["fifo"]="114*1";
w["file"]="91*4,135*1,140*4,148*1,160*1,169*1,175*1,181*2,186*1";
w["filter"]="131*2,132*1";
w["finish"]="147*1";
w["firmwar"]="1*1,48*1,50*36,63*12,149*1,193*3";
w["firmware."]="149*1";
w["first"]="9*1,15*2,18*1,19*1,20*1,23*1,24*1,25*1,37*3,77*1,79*1,112*1,115*2,140*3,142*1,148*1,149*1";
w["first."]="173*2";
w["fit"]="171*1";
w["fix"]="39*1,60*1";
w["flag"]="79*1,109*4";
w["flash"]="79*7,91*18,111*1,112*2,116*1,119*1,120*1,131*6,132*6,133*4,139*1,140*12,141*2,148*58,149*53,150*1,151*1,160*6,181*2,182*3,185*9";
w["flash."]="112*1,131*1";
w["flash_bas"]="185*2";
w["flash_byt"]="185*2";
w["flash_page_byt"]="185*3";
w["fli"]="135*1";
w["flow"]="119*1,120*1,134*1,139*44";
w["foll"]="175*1";
w["follow"]="24*1,38*1,88*1,89*1,115*1,135*1,136*1,138*1,139*1,148*1";
w["forgotten"]="121*1";
w["form"]="190*1";
w["format"]="9*6,32*5,33*5,34*5,35*5,37*16,38*1,41*5,42*5,49*5,50*5,51*5,52*5,54*5,55*5,56*5,57*5,59*5,60*5,61*5,68*5,69*5,70*5,71*5,72*5,74*5,75*5,76*5,77*5,78*5,79*5,81*5,82*5,83*5,84*5,85*10,86*5,96*5,97*5,98*5,99*5,100*5,101*5,102*5,103*5,104*5,105*5,106*5,107*5,108*5,109*5,110*5,111*5,112*5,113*5,114*5,115*5,116*5,117*5,118*5,119*5,120*5,121*5,123*5,124*5,125*5,126*5,127*5,129*5,130*5,136*7,142*10,143*5,144*5,145*5,146*5,147*5,148*5,149*5,152*5,153*5,162*5,163*5,164*5,165*5,166*5,167*5,188*5";
w["format."]="38*2";
w["forth"]="171*1";
w["forward"]="37*2,192*1";
w["forwarding."]="192*1";
w["found"]="2*1,51*1";
w["fragment"]="23*4,24*1";
w["fragmentcod"]="23*1";
w["fragmentinfo"]="23*1,24*2";
w["frame"]="18*1,19*1,37*62,38*2,176*1";
w["free"]="3*1";
w["freeli"]="135*1";
w["frequenc"]="88*1,143*1,144*1,182*1,185*8";
w["frequency."]="60*1";
w["from"]="0*1,1*1,7*1,13*1,14*1,18*1,24*1,25*1,37*2,60*1,69*1,70*1,89*2,91*6,97*1,98*1,113*1,120*1,130*2,131*3,133*13,135*1,136*3,139*1,140*3,148*1,149*2,151*1,153*1,167*3,175*2,185*3";
w["front"]="135*1";
w["front-end"]="135*1";
w["full"]="23*1,24*1,131*2,132*6,133*6,181*2";
w["functi"]="0*1";
w["function"]="3*1,24*1,36*1,37*2,38*1,39*1,43*3,62*2,90*1,91*3,111*1,135*1,137*1,138*1,139*2,140*6,175*1,185*1";
w["function."]="138*1";
w["function:"]="135*1";
w["functionality."]="62*1";
w["functionl"]="111*1";
w["functions."]="3*1";
w["further"]="2*1,50*1";
w["fuse"]="110*1,132*2,133*1,138*1,140*3,141*2,152*52,153*52,154*1,155*1,156*1,157*1,160*6,185*1";
w["fuse_base_addr"]="133*1,185*1";
w["futur"]="1*1";
w["fw"]="1*2";
w["fwrev_maj"]="193*1";
w["fwrev_min"]="193*1";
w["g"]="52*1,89*1,138*1,180*1";
w["gain"]="6*1,89*1,136*1";
w["gateway"]="0*1,1*1";
w["general"]="37*3,133*1,175*1";
w["generat"]="60*1,105*1";
w["generic"]="43*3,171*2,173*51,174*52";
w["get"]="3*1,8*2,9*41,11*36,18*41,19*41,20*41,26*1,28*1,29*48,30*41,44*2,46*2,47*48,63*3,64*2,66*2,67*49,70*41,88*1,91*3,92*2,94*2,95*49,98*41,135*1,136*1,140*3,141*1,144*51,182*41,185*41,192*3,193*46";
w["get_buffer_s"]="20*2";
w["get_config"]="3*1,9*2";
w["get_data"]="18*2";
w["get_id"]="89*1";
w["get_request"]="3*1";
w["get_status"]="19*2";
w["given"]="3*1,38*1,83*1,91*3,104*1,136*1,140*3,185*1";
w["gone"]="59*1";
w["gpio"]="186*3";
w["grant"]="171*1";
w["ground"]="182*1";
w["had"]="43*3";
w["half"]="181*1";
w["half-word"]="181*1";
w["halt"]="64*1,72*43";
w["handl"]="148*1,149*1";
w["handler"]="37*11,38*2,39*1,43*3,62*1,175*1,180*1,190*1";
w["handler."]="62*1";
w["hardwar"]="13*1,20*1,21*1,63*3,91*6,92*2,117*45,118*45,139*2,159*1,192*1,193*1";
w["hardware."]="15*1,20*1,21*1";
w["header"]="91*1,140*1,160*1,169*1,186*1";
w["heavili"]="131*1,132*1";
w["held"]="137*2";
w["help"]="51*1";
w["here"]="1*3,3*1,9*1,136*1,172*1";
w["here."]="1*3,172*1";
w["here:"]="3*1,9*1,136*1";
w["herein"]="171*1";
w["herein."]="171*1";
w["hhb"]="3*1,10*1";
w["hid"]="0*1,13*2,23*1";
w["high"]="6*1,63*3,148*1,149*1";
w["hint"]="44*1,62*41,87*46,134*46,173*1,174*1";
w["hk"]="38*1";
w["hk_context_analog"]="63*1,193*1";
w["hk_context_config"]="63*1,193*1";
w["hk_query_command"]="63*1,189*1";
w["hold"]="101*1,135*1";
w["host"]="0*1,13*2,37*3,88*1,89*1,90*1,130*1,135*2,136*1,188*1";
w["host."]="130*1";
w["housekeep"]="38*2,44*51,48*41,53*41,62*6,63*41,176*1,189*41,193*41";
w["housekeeping_analog_vtref"]="63*1";
w["housekeeping_awak"]="63*1";
w["housekeeping_config_build"]="63*1";
w["housekeeping_config_fwrev_maj"]="63*1";
w["housekeeping_config_fwrev_min"]="63*1";
w["housekeeping_config_hwrev"]="63*1";
w["housekeeping_failed_invalid_key"]="63*1";
w["housekeeping_failed_invalid_paramet"]="63*1";
w["housekeeping_failed_invalid_parameter_valu"]="63*1";
w["housekeeping_failed_jtag_detect_jtagm_error"]="63*1";
w["housekeeping_failed_jtag_detect_jtagm_init_error"]="63*1";
w["housekeeping_failed_jtag_detect_no_devic"]="63*1";
w["housekeeping_failed_jtag_detect_too_many_devic"]="63*1";
w["housekeeping_failed_no_target_pow"]="63*1";
w["housekeeping_failed_not_support"]="63*1";
w["housekeeping_failed_ok"]="63*1";
w["housekeeping_failed_osccal_fail"]="63*1";
w["housekeeping_failed_osccal_fw_error"]="63*1";
w["housekeeping_failed_osccal_invalid_mod"]="63*1";
w["housekeeping_failed_osccal_invalid_phys"]="63*1";
w["housekeeping_power_off"]="63*1";
w["housekeeping_power_on"]="63*1";
w["housekeeping_reset_appli"]="63*1";
w["housekeeping_reset_releas"]="63*1";
w["housekeeping_sleep"]="63*1";
w["how"]="148*1,185*1,192*2";
w["howev"]="22*1,137*1";
w["hwrev"]="193*1";
w["ice"]="1*1";
w["id"]="3*1,4*1,9*2,10*1,27*1,28*3,29*3,30*1,32*1,33*1,34*1,35*1,37*14,38*3,39*1,40*1,41*1,42*1,43*41,44*1,45*1,46*2,47*2,49*1,50*1,51*2,52*1,54*1,55*1,56*1,57*1,59*1,60*1,61*1,63*41,64*1,65*1,66*2,67*2,68*3,69*1,70*52,71*1,72*1,73*1,74*1,75*1,76*1,77*1,78*1,79*1,80*1,81*1,82*1,83*45,84*1,85*2,86*1,89*3,91*53,92*1,93*1,94*2,95*2,96*2,97*1,98*49,99*1,100*1,101*1,102*1,103*1,104*1,105*1,106*1,107*1,108*1,109*1,110*1,111*1,112*1,113*1,114*1,115*1,116*1,117*1,118*1,119*1,120*1,121*1,123*1,124*1,125*1,126*1,127*1,129*1,130*1,131*1,132*1,133*1,136*6,140*53,142*2,143*2,144*2,145*2,146*2,147*2,148*2,149*2,152*2,153*2,160*47,162*2,163*2,164*3,165*2,166*2,167*2,169*47,173*1,174*1,175*1,178*1,180*1,181*1,182*1,184*1,185*1,186*47,187*1,188*1,189*1,190*1,191*1,193*1";
w["ident"]="150*1,151*1,154*1,155*1,156*1,157*1";
w["identifi"]="37*2";
w["idr"]="128*1,130*47,140*3,185*1";
w["ie"]="24*1,25*1";
w["ie:"]="24*1,25*1";
w["if"]="30*2,37*1,51*2,68*2,78*1,79*1,89*1,96*4,109*3,121*1,131*4,132*10,135*1,136*2,137*3,138*2,148*3,171*1,185*4";
w["ignor"]="24*1,25*1,111*1,165*1,166*1";
w["ignored."]="24*1,25*1,111*1";
w["illeg"]="91*3,140*15";
w["immedi"]="102*1,121*1,138*1,139*1";
w["immediately."]="121*1";
w["implement"]="0*4,1*56,38*2,59*1,91*3,114*1,140*3,153*1,158*1,175*1,179*1,180*1,192*1";
w["implementation."]="158*1,175*2";
w["implent"]="63*3";
w["impli"]="89*1,136*1,171*3";
w["implicit"]="69*1,97*1,136*1";
w["inabl"]="171*1";
w["incident"]="171*1";
w["includ"]="62*1,91*1,140*1,160*1,169*1,171*2,175*1,186*1";
w["incom"]="14*1,37*2";
w["incorrect"]="91*6,135*1";
w["increas"]="140*3";
w["increment"]="37*2";
w["independ"]="37*1,62*1";
w["index"]="181*2";
w["indic"]="23*1,24*1,25*1,34*1,56*1,59*1,60*1,61*1,79*1,85*1,126*1,129*1,139*1,148*2,153*1,182*1";
w["indirect"]="171*1";
w["ineffici"]="131*1";
w["info"]="63*3,91*3,129*1";
w["inform"]="2*1,9*1,18*1,19*1,135*1,171*3";
w["information."]="9*1,18*1,19*1,39*1,135*1";
w["infringement."]="171*1";
w["init"]="63*3,91*6";
w["initialis"]="63*3,140*3,164*1,185*1";
w["initiated."]="105*1";
w["input"]="0*1";
w["insert"]="119*2,139*1";
w["instanc"]="35*1,42*1,57*1,86*1,127*1";
w["instruct"]="74*1,78*1,91*3,105*1,139*3,148*1";
w["instruction-level"]="139*1";
w["insuffici"]="91*3";
w["intellectu"]="171*1";
w["intend"]="1*1,171*2,172*1";
w["intent"]="140*3";
w["interact"]="172*1";
w["interfac"]="0*53,1*2,22*1,23*1,30*3,62*3,68*3,69*1,88*2,89*1,91*6,96*4,97*1,110*1,131*1,135*2,136*2,140*9,153*2,164*1,175*1,179*1,180*1,182*1,192*1";
w["interface."]="68*1,96*1,110*1,131*1,180*1";
w["interface:"]="88*1";
w["interfaces:"]="0*1";
w["intern"]="63*3,91*3,104*1,170*1,185*1";
w["interrog"]="39*1";
w["interrupt"]="171*1,185*1";
w["interv"]="185*1";
w["into"]="38*1,77*1,78*1,105*1,137*2,138*1,139*1,148*2,150*1,166*1";
w["introduct"]="171*1,179*51";
w["invalid"]="91*9,140*6";
w["invok"]="50*1";
w["io"]="131*1,132*1,140*3";
w["ir"]="77*1,78*1,136*1,182*2,185*2";
w["ir-bit"]="182*2,185*2";
w["ir."]="78*1";
w["ire"]="109*1";
w["isp"]="171*1,175*52,186*3";
w["isprotect"]="78*1";
w["iss"]="148*1";
w["issu"]="24*1,148*1";
w["issued."]="24*1";
w["it"]="38*1,39*1,171*1";
w["ite"]="137*1";
w["item"]="43*3,63*3,91*3,140*3";
w["iter"]="15*1";
w["itm"]="7*1";
w["itself"]="1*1,23*1,131*1,185*3";
w["itself."]="131*1";
w["jtag"]="1*6,38*2,48*1,51*41,62*3,63*6,77*1,78*1,88*4,90*1,91*30,111*1,116*1,117*1,118*1,132*46,136*4,140*21,180*1,182*3,183*1,185*35,192*1";
w["jtag."]="51*1,136*1,180*1";
w["jtag_clock"]="88*1";
w["jtag_clock:"]="88*1";
w["jtag_daisi"]="88*1";
w["jtag_daisy:"]="88*1";
w["jtagice3"]="1*3,174*1";
w["jtagice3discoveri"]="43*1";
w["jtagice3discoverycommand"]="43*1";
w["jtagice3discoveryfailurecod"]="43*1";
w["jtagice3discoveryrespons"]="43*1";
w["jtagice3failurecod"]="43*1";
w["jtagice3housekeepinganalogcontextparamet"]="63*1";
w["jtagice3housekeepingcommand"]="63*1";
w["jtagice3housekeepingconfigcontextparamet"]="63*1";
w["jtagice3housekeepingev"]="63*1";
w["jtagice3housekeepingfailurecod"]="63*1";
w["jtagice3housekeepingpowerev"]="63*1";
w["jtagice3housekeepingquerycontext"]="63*1";
w["jtagice3housekeepingresetev"]="63*1";
w["jtagice3housekeepingrespons"]="63*1";
w["jtagice3housekeepingsetgetcontext"]="63*1";
w["jtagice3housekeepingsleepev"]="63*1";
w["jtagice3setgetfailurecod"]="63*1";
w["jtagid"]="68*1,70*1";
w["jtagm"]="63*3,140*9";
w["k"]="96*1";
w["kbit"]="143*1,144*1";
w["kbps"]="182*1";
w["keep"]="140*3,172*1";
w["keil"]="0*1,2*1";
w["key"]="50*2,63*3";
w["khz"]="52*1,88*1,182*1,185*4";
w["kit"]="1*1,172*1";
w["l"]="171*1,182*1";
w["l."]="182*1";
w["larg"]="23*1";
w["last"]="112*1,148*1";
w["later"]="1*1";
w["layer"]="37*1";
w["le"]="192*1";
w["least"]="37*3,173*1";
w["leav"]="90*1,92*1,109*47,136*1,137*1,140*3,141*1,146*52,148*1,160*3,161*1,163*52";
w["leave."]="109*1";
w["leave_progmod"]="175*1,180*1";
w["led"]="30*3,91*3";
w["legal"]="140*3";
w["length"]="91*6,96*1,166*1,167*1,180*1";
w["less"]="185*1";
w["level"]="74*1,77*1,91*3,101*1,102*1,105*3,139*3,140*6";
w["liabil"]="171*1";
w["liabl"]="171*1";
w["licens"]="171*1";
w["lie"]="136*1";
w["life."]="171*1";
w["limit"]="1*1,171*2,185*3";
w["line"]="182*2";
w["line:"]="182*1";
w["ling"]="110*1";
w["link"]="0*1";
w["list"]="1*1,27*2,28*1,29*1,31*1,33*42,38*1,39*1,40*2,41*42,43*6,45*2,46*1,47*1,51*1,53*1,55*37,63*6,65*2,66*1,67*1,80*1,82*42,91*18,93*2,94*1,95*1,119*1,120*1,122*1,124*42,140*15,184*8,187*2,189*2,190*2,191*8";
w["littl"]="90*1,173*1";
w["load"]="141*1,142*52,148*4,160*3";
w["locat"]="104*1,114*5,171*1";
w["location."]="104*1";
w["lock"]="91*3,140*3,141*2,154*42,155*42,160*6,185*1";
w["lockbit"]="132*2,133*1,165*1";
w["lockbit_base_addr"]="133*1,185*1";
w["logo"]="171*1";
w["longer"]="1*1,110*1";
w["loop"]="145*1";
w["loss"]="171*2";
w["low"]="63*3,74*1,77*1,91*3,139*1,140*3,148*1,149*1,182*1";
w["low-level"]="91*3,139*1";
w["lower"]="159*1";
w["lowest"]="173*1";
w["lsb"]="77*1";
w["ltd"]="171*1";
w["ltd."]="171*1";
w["m"]="23*3,24*2";
w["made"]="172*1";
w["main"]="38*2,101*1,140*3,185*1";
w["maintain"]="185*1";
w["major"]="193*1";
w["make"]="68*1,89*1,96*1,136*1,171*3";
w["mal"]="132*1";
w["mal-align"]="132*1";
w["mani"]="63*3,89*1,91*3,136*1,140*3";
w["manner"]="25*1";
w["manual"]="62*1";
w["manufactur"]="43*3,51*1,190*1";
w["map"]="11*1,12*1,91*3";
w["market"]="135*1";
w["mask"]="92*1,114*49,131*1,132*1,140*3";
w["mask."]="114*1";
w["master"]="91*15,140*3";
w["match"]="131*1";
w["max_sw_break"]="119*1,120*1";
w["maximum"]="88*1,91*3,182*3";
w["may"]="36*2,38*1,69*1,97*1,131*3,132*1,135*2,136*2,171*1,188*1";
w["mcu"]="130*1,140*6,171*101";
w["mean"]="24*1,148*1,173*1";
w["meanings:"]="148*1";
w["means."]="104*1";
w["medbg"]="0*1,1*1";
w["mega"]="185*5";
w["mega48"]="135*1";
w["megaavr"]="38*2,52*1,111*2,116*1,117*2,118*2,132*41,140*12,175*1,183*1,185*33";
w["memori"]="75*5,76*6,79*2,91*9,92*3,113*46,114*46,115*46,137*1,139*4,140*27,148*10,149*2,150*1,151*1,161*2,166*53,167*54,169*3,174*1,181*37,183*46";
w["memory-typ"]="139*1";
w["memory_word_access"]="78*1";
w["memtyp"]="91*9,131*47,132*46,133*46,140*9,181*5,183*3,184*2,191*2";
w["memtype_appl_flash"]="133*1,140*1";
w["memtype_appl_flash_atom"]="133*1,140*1";
w["memtype_boot_flash"]="133*1,140*1";
w["memtype_boot_flash_atom"]="133*1,140*1";
w["memtype_calibration_signatur"]="133*1,140*1";
w["memtype_eeprom"]="131*1,132*1,133*1,140*1";
w["memtype_eeprom_atom"]="133*1,140*1";
w["memtype_eeprom_pag"]="132*1,140*1";
w["memtype_flash_pag"]="131*1,132*1,140*1";
w["memtype_fus"]="132*1,133*1,140*1";
w["memtype_lock_bit"]="133*1";
w["memtype_lockbit"]="132*1,140*1";
w["memtype_oscc"]="132*1,140*1";
w["memtype_regfil"]="133*1,140*1";
w["memtype_signatur"]="131*1,132*1,133*1,140*1";
w["memtype_spm"]="131*1,132*1,140*1";
w["memtype_sram"]="131*1,132*1,133*1,140*1";
w["memtype_user_signatur"]="132*1,133*1,140*1";
w["merchant"]="171*1";
w["messag"]="60*1,128*1,130*49,140*9,185*3";
w["messages."]="185*1";
w["method"]="147*1,148*1";
w["method:"]="147*1";
w["microcontrol"]="38*1,173*1";
w["microsoft"]="171*1";
w["might"]="75*1,76*1";
w["mini"]="1*1";
w["minor"]="193*1";
w["misc"]="63*3";
w["mismatch"]="140*3";
w["mkii"]="175*1,180*1";
w["mkii."]="180*1";
w["mnf_date"]="190*1";
w["mode"]="13*54,14*54,50*2,63*6,88*2,91*18,92*2,99*1,105*1,108*47,109*48,111*4,112*1,113*1,114*1,115*1,116*2,117*1,131*1,132*14,133*13,136*1,137*3,138*2,139*3,140*9,141*2,145*52,146*52,148*27,160*6,161*2,162*52,163*52,165*8,166*1,169*3,185*2,192*2";
w["mode."]="50*1,88*1,113*1,114*1,115*1,132*7,137*1,138*1,148*1,185*3";
w["modebit"]="148*1";
w["modifi"]="107*1";
w["modul"]="91*3,110*2,117*1,118*1,131*1,135*1,138*1,140*6,182*1,185*1";
w["module."]="117*1,118*1,138*1";
w["monitor"]="131*1,132*1,138*1";
w["more"]="7*1,23*1,24*1,37*1,38*1,63*3,132*3";
w["most"]="173*1";
w["motor"]="185*1";
w["mount"]="1*3";
w["ms"]="145*4,146*2,147*1";
w["ms."]="185*1";
w["msb"]="9*1,15*2,18*1,19*1,20*1,23*1,24*1,25*1,142*1,148*1,149*1";
w["multi"]="141*1,158*41,180*1";
w["multi-byt"]="180*1";
w["multipl"]="28*1,29*1,46*1,47*1,66*1,67*1,94*1,95*1,131*1,132*3";
w["must"]="22*2,23*1,24*3,25*1,52*1,62*2,88*4,89*6,131*2,132*1,135*4,136*5,139*1,140*3,148*3,156*1,157*1,165*1,181*1,182*1,185*2";
w["mv"]="193*1";
w["n"]="9*2,10*1,18*1,23*4,24*3,25*1,28*1,33*1,34*1,37*3,41*1,46*1,55*1,56*1,66*1,76*1,77*3,82*1,85*2,94*1,114*1,115*1,119*4,120*4,124*1,126*1,129*1,138*1,148*1,166*1,181*1,184*4,187*1,189*1,190*3,191*4";
w["nack"]="91*3";
w["name"]="38*1,43*3,135*1,171*1,185*3,190*1";
w["name."]="135*1";
w["nativ"]="0*1,172*1";
w["natur"]="24*1";
w["nb"]="132*1";
w["nb:"]="132*1";
w["nbsi"]="159*1";
w["near"]="91*3";
w["necessari"]="37*1,68*1,96*1,135*1";
w["necessarili"]="51*1";
w["need"]="135*2";
w["new"]="136*1,174*1";
w["newer"]="174*2";
w["next"]="23*1,37*1,102*1,119*1,120*1,139*2,142*1";
w["next."]="23*1";
w["nexus"]="91*3,181*3";
w["ng"]="37*1";
w["nibbl"]="23*1";
w["no"]="1*1,24*1,25*1,43*3,49*1,50*1,51*1,63*3,68*2,69*1,89*1,91*9,96*2,97*1,110*1,117*1,118*1,136*1,140*6,145*1,171*4,175*1,180*1";
w["non"]="171*1";
w["non-infring"]="171*1";
w["non-infringement."]="171*1";
w["none"]="182*1";
w["normal"]="137*1,138*1";
w["note"]="0*40,1*2,18*1,24*1,28*1,29*1,37*1,46*1,47*1,52*1,56*1,59*1,60*1,66*1,67*1,69*1,75*1,76*1,79*1,88*1,94*1,95*1,97*1,111*3,112*1,115*1,116*1,117*3,118*3,119*1,120*1,135*2,148*1,153*1,175*42,180*1,182*1,185*6,192*1";
w["note:"]="28*1,29*1,46*1,47*1,56*1,59*1,60*1,66*1,67*1,69*1,79*1,94*1,95*1,97*1,111*3,112*1,115*1,116*1,117*3,118*3,119*1,120*1,148*1,153*1,185*4";
w["notic"]="171*1,172*1";
w["notice."]="171*1,172*1";
w["now"]="60*2,188*1";
w["ntroller"]="1*1";
w["number"]="9*2,10*1,18*1,19*1,23*3,24*1,25*1,28*1,29*1,43*3,46*1,47*1,60*1,63*3,66*1,67*1,73*1,75*2,76*2,77*1,79*1,91*9,94*1,95*1,113*1,114*1,115*1,117*2,118*2,131*5,132*4,133*10,145*1,148*1,149*1,166*1,167*1,182*3,190*1,193*1";
w["numbyt"]="9*1,148*1,149*1";
w["nvm"]="140*6,185*1";
w["nvm_base"]="133*1,185*1";
w["o"]="38*1,96*1";
w["occur"]="22*1,59*1,63*3,91*3,140*3";
w["occurred."]="59*1";
w["ocd"]="117*1,118*1,132*46,135*2,138*1,139*1,140*12,183*1,185*3";
w["ocd_rev"]="185*2";
w["ocdr"]="131*1,132*1,185*2";
w["ocdr_addr"]="185*2";
w["odd"]="131*1,132*1";
w["off"]="13*1,14*1,49*1,59*1,62*2,63*3";
w["offer"]="172*1";
w["offset"]="185*9";
w["often"]="185*1";
w["ok"]="13*1,14*1,16*1,17*1,21*1,28*1,31*1,32*41,46*1,49*1,50*1,52*1,53*1,54*36,63*9,66*1,69*1,71*1,72*1,73*1,76*1,79*1,80*1,81*41,91*6,94*1,96*1,97*1,99*1,100*1,101*1,102*1,103*1,104*1,105*2,107*1,108*1,109*1,110*1,111*1,115*1,116*1,117*1,118*1,119*1,120*1,121*1,122*1,123*41,140*6,159*1,160*3,168*1,188*1";
w["ok."]="34*1,56*1,85*1,126*1";
w["on"]="0*1";
w["onc"]="89*1,105*2,136*1";
w["ondit"]="171*1";
w["one"]="22*2,24*3,28*1,29*1,38*2,46*1,47*1,66*1,67*1,74*1,91*3,94*1,95*1,131*2,132*5,148*1,153*1,182*1,185*4";
w["onli"]="1*2,3*1,10*1,13*1,14*1,22*1,24*1,28*1,29*1,39*1,46*1,47*1,66*1,67*1,77*1,94*1,95*1,111*1,112*1,113*1,114*1,115*1,116*2,119*1,120*1,131*1,132*2,133*3,135*1,139*2,140*12,148*3,180*1,181*1,185*4,192*2";
w["only."]="6*1,9*1,133*3,139*1";
w["onto"]="188*1";
w["oper"]="34*1,56*1,85*1,126*1,131*2,132*5,137*1,148*2";
w["operation."]="78*1,131*2,132*5";
w["option"]="135*2,140*3,185*2";
w["option-rel"]="140*3";
w["options:"]="135*1";
w["order"]="22*1,37*2,89*2,135*1,136*2,173*1";
w["origin"]="139*1";
w["os"]="1*1";
w["osccal"]="63*3,132*1,141*1,157*43,160*3,185*2";
w["osccal_addr"]="185*2";
w["oscil"]="48*1,52*42,63*3,140*3";
w["oth"]="171*1";
w["other"]="9*1,10*1,39*1,62*1,89*1,104*1,136*1,165*1,171*4,172*1";
w["others."]="171*1";
w["otherwis"]="79*1,171*1";
w["out"]="19*1,20*1,24*1,63*6,77*1,78*1,89*1,96*1,105*1,131*2,132*1,135*1,136*1,140*12,145*1,153*1,159*3,168*1,171*1,192*1";
w["out."]="77*1,131*2,132*1";
w["outlin"]="37*1";
w["output"]="0*1,7*1,77*1,185*1,192*1";
w["outsid"]="132*9";
w["over"]="22*1,24*1,105*1";
w["overflow"]="91*9";
w["overridden"]="89*1,136*1";
w["overrun"]="18*1,19*1";
w["overview"]="0*46,36*46,38*46,113*1,114*1,115*1,176*2,179*1";
w["owe"]="175*1";
w["own"]="38*1,172*2";
w["p"]="78*1";
w["packet"]="9*10,10*1,23*6,24*3,25*2,37*6,89*1,91*9,136*1,159*1,168*1";
w["packet_numb"]="23*1";
w["page"]="79*5,91*9,92*1,111*4,116*43,131*3,132*7,133*9,140*24,148*21,166*1,181*4,182*2,185*6";
w["page-eras"]="116*1";
w["page."]="182*1";
w["pagebit"]="148*1";
w["pages."]="132*6";
w["pair"]="0*3";
w["paramet"]="9*3,10*1,28*4,29*5,30*41,37*1,38*1,44*1,46*5,47*5,51*1,63*21,66*5,67*6,88*1,91*9,94*5,95*6,111*2,136*1,138*1,140*6,148*1,161*1,164*55,168*1,169*3,175*1,178*1,182*41,184*1,185*41,191*1,193*46";
w["parameter."]="89*1,136*1";
w["parameters."]="10*1,38*1,111*1";
w["paramt"]="28*1,29*1,46*1,47*1,66*1,67*1,94*1,95*1";
w["pariti"]="140*3";
w["part"]="1*1,3*1,185*2";
w["partial"]="91*3";
w["particular"]="1*1,171*1";
w["pattern"]="78*1";
w["payload"]="37*3,38*1,91*6,113*1,114*1,115*2,119*1,120*1,131*1,132*1,175*2,180*2,182*2";
w["pc"]="0*1,13*1,74*1,80*1,84*42,91*3,92*2,106*43,107*43,122*1,125*42,129*1,139*1,140*12,148*1";
w["pdi"]="1*4,38*1,136*1,140*12,180*1,185*10,186*3";
w["peb01"]="78*1";
w["per"]="114*1,139*1";
w["perform"]="3*2,51*1,52*1,62*1,71*1,74*1,105*1,111*1,116*1,136*1,139*5,147*1,165*1,192*1";
w["performed."]="79*1";
w["period"]="22*1";
w["phi"]="63*3,140*3";
w["physic"]="36*1,52*1,62*1,64*2,68*47,69*47,70*1,87*1,88*6,89*46,91*24,92*2,96*47,97*47,98*1,109*1,134*1,135*4,136*48,137*2,138*2,140*27,160*3,168*1,175*1,180*1,182*2,185*1";
w["physical."]="109*1,182*1,185*1";
w["physical:"]="135*1";
w["pid"]="1*1";
w["pin"]="0*2,3*1,5*1,6*42,7*1,110*2,145*1,159*1,192*2";
w["pin."]="110*1,192*1";
w["place"]="88*1,135*1,138*1";
w["platform"]="1*1";
w["point"]="137*2";
w["point."]="89*1,136*1";
w["pointer"]="114*1";
w["poll"]="3*2,4*2,22*2,24*1,25*1,60*1,138*2,140*3,145*1,147*1,148*10,185*3";
w["poll1"]="148*2";
w["poll2"]="148*2";
w["pollindex"]="145*2,153*1";
w["pollindex."]="145*1,153*1";
w["polling."]="148*1";
w["pollmethod"]="147*2";
w["pollmethod."]="147*1";
w["pollvalu"]="145*2";
w["pollvalue."]="145*1";
w["port"]="0*3,1*2";
w["port."]="0*1";
w["possi"]="160*1";
w["possibl"]="91*1,96*1,137*1,140*1,169*1,171*2,185*1,186*1";
w["possible."]="138*1";
w["post"]="146*1";
w["post-delay"]="146*1";
w["postdelay"]="146*2";
w["postdelay."]="146*1";
w["power"]="30*4,58*1,59*39,63*12,91*3,110*1,140*3";
w["pre"]="146*1";
w["pre-delay"]="146*1";
w["predelay"]="146*2";
w["predelay."]="146*1";
w["prefac"]="171*1,172*50";
w["preliminari"]="170*2";
w["present"]="30*2";
w["present."]="30*2";
w["preserv"]="24*1";
w["prevent"]="114*1,135*1,185*1";
w["previous"]="43*3,70*1,98*1";
w["prior"]="172*1";
w["pro"]="1*1";
w["process"]="23*1,24*1,89*1,90*1,136*1,138*3";
w["process."]="89*1,136*1";
w["prod_sign_base_addr"]="133*1,185*1";
w["product"]="171*7,185*1";
w["products."]="171*2";
w["profit"]="171*1";
w["prog"]="92*2,108*41,109*42,131*1,132*1,133*1";
w["program"]="0*3,1*4,22*1,38*5,84*2,87*1,90*42,91*3,107*1,108*1,109*2,111*1,116*1,117*2,125*2,129*2,131*2,132*14,133*11,134*1,135*1,136*1,137*45,138*2,139*3,140*21,141*52,142*1,143*1,144*1,145*53,146*53,148*73,149*2,150*44,151*1,152*53,153*1,154*44,155*1,156*1,157*1,159*46,160*18,161*2,162*53,163*53,166*1,167*1,168*46,172*1,173*1,174*1,175*3,180*3,185*4";
w["programmin"]="138*1";
w["programming."]="111*1,148*2";
w["properti"]="171*1";
w["proport"]="131*1,132*1";
w["protect"]="64*1,78*47,91*3";
w["protocol"]="0*1,1*5,2*48,3*5,4*4,5*1,6*1,7*2,8*1,9*1,10*1,11*2,12*2,13*1,14*1,15*1,16*1,17*1,18*1,19*1,20*1,21*1,22*4,23*1,24*1,25*1,26*47,27*1,28*1,29*1,30*1,31*1,32*1,33*1,34*1,35*1,36*3,37*14,38*50,39*48,40*1,41*1,42*1,43*42,44*47,45*1,46*1,47*1,48*1,49*1,50*1,51*1,52*2,53*1,54*1,55*1,56*1,57*1,58*1,59*1,60*1,61*1,62*4,63*1,64*47,65*1,66*1,67*1,68*1,69*1,70*1,71*1,72*1,73*1,74*1,75*1,76*1,77*1,78*1,79*1,80*1,81*1,82*1,83*1,84*1,85*1,86*1,87*1,88*1,89*1,90*2,91*2,92*47,93*1,94*1,95*1,96*1,97*1,98*1,99*1,100*1,101*1,102*1,103*1,104*1,105*1,106*1,107*1,108*1,109*1,110*1,111*1,112*1,113*1,114*1,115*1,116*1,117*1,118*1,119*1,120*1,121*1,122*1,123*1,124*1,125*1,126*1,127*1,128*1,129*1,130*1,131*1,132*1,133*1,134*1,135*1,136*1,137*1,138*1,139*2,140*11,141*47,142*1,143*1,144*1,145*1,146*1,147*1,148*1,149*1,150*1,151*1,152*1,153*1,154*1,155*1,156*1,157*1,158*1,159*47,160*8,161*47,162*1,163*1,164*1,165*1,166*1,167*1,168*47,169*1,170*1,171*157,172*3,173*56,174*55,175*62,176*55,177*2,178*54,179*1,180*57,181*1,182*1,183*1,184*2,185*1,186*11,187*2,188*1,189*2,190*2,191*2,192*1,193*1";
w["protocol-set"]="1*2";
w["protocol."]="62*1,91*1,140*1,160*1,186*1";
w["protocol:"]="62*1";
w["protocols."]="37*1";
w["provid"]="0*1,7*1,133*12,135*1,139*3,171*2,172*1";
w["provided."]="165*1";
w["publish"]="49*1,188*1";
w["pull"]="182*2";
w["puls"]="6*1";
w["punit"]="171*1";
w["pure"]="109*1";
w["purpos"]="133*1,135*1,137*1,171*1";
w["put"]="38*1,75*1,76*1,121*1,138*1,139*1,175*1,180*1";
w["pwm"]="185*1";
w["quarter"]="185*1";
w["queri"]="1*1,26*1,27*47,39*3,40*46,43*9,44*2,45*46,64*1,65*47,88*1,92*1,93*47,135*1,140*3,178*1,184*41,186*3,187*41,189*46,190*46,191*41";
w["question"]="172*1";
w["r"]="38*1,131*2,132*3,133*7,182*2,185*1,193*5";
w["rang"]="63*6,79*1,112*1,132*3,140*6,159*1";
w["rapid"]="60*1";
w["rate"]="15*55,18*1,19*1,88*1,143*1,159*1,160*6,182*1,192*1";
w["rather"]="1*1,135*1";
w["ratio"]="140*3";
w["raw"]="192*1";
w["rdi"]="147*1,148*3,159*1";
w["re"]="70*1,89*2,98*1,136*1";
w["re-connect"]="89*1,136*1";
w["re-read"]="70*1,98*1";
w["rea"]="148*1";
w["reach"]="104*1";
w["read"]="18*1,19*3,20*1,24*1,29*1,34*1,47*1,56*1,62*1,64*1,67*1,70*2,75*43,76*1,85*1,89*2,91*12,92*3,95*1,96*1,98*2,106*47,113*47,114*51,126*1,131*11,132*21,133*10,135*2,136*2,137*1,139*4,140*30,141*6,144*1,148*5,149*55,151*43,153*53,155*43,156*44,157*44,160*21,161*1,167*55,175*1,182*1,192*1,193*1";
w["read."]="75*1,76*1,148*1";
w["readabl"]="184*1,191*1";
w["reading."]="114*1";
w["reason"]="89*1,136*1";
w["receiv"]="18*4,19*3,20*1,23*1,24*2,25*1,78*1,91*18,139*1,140*6,145*1";
w["received."]="78*1";
w["receiver_dis"]="186*1";
w["refer"]="52*1,63*3,135*1";
w["regain"]="110*1";
w["region"]="79*2";
w["regist"]="0*1,72*1,73*6,77*2,91*12,131*1,132*1,133*1,140*9,171*3,181*4,185*5";
w["reject"]="37*1";
w["rela"]="171*1";
w["relat"]="63*3,91*9,140*9";
w["relationship"]="23*1";
w["relationship:"]="23*1";
w["relay"]="140*3";
w["releas"]="61*1,63*3,91*3,109*1,135*1,170*2,182*3";
w["release."]="170*2";
w["relev"]="135*2,173*1,178*1";
w["remov"]="120*1,121*1,139*2";
w["repetit"]="139*1";
w["repli"]="115*2";
w["report"]="37*1,115*1";
w["report_s"]="23*1";
w["report_size."]="23*1";
w["represent"]="171*1";
w["reprogram"]="138*1";
w["request"]="8*2,11*36,12*36,15*1,79*2,89*1,91*3,131*3,132*2,136*1";
w["requir"]="6*1,23*1,24*1,88*1,111*1,135*4,136*1,138*2";
w["required."]="50*1,135*1";
w["required:"]="88*1,135*1";
w["reserv"]="171*1,172*1";
w["reserved."]="171*1";
w["reset"]="3*2,49*2,51*4,58*1,61*44,63*9,64*1,68*3,73*49,89*2,91*9,92*1,96*3,101*45,109*1,110*3,135*1,136*3,137*2,139*3,140*6,182*5,185*1";
w["reset."]="101*1,182*1";
w["reset:"]="139*1";
w["resetting."]="185*1";
w["resourc"]="117*2,118*2,139*1,140*3";
w["resources."]="117*1,118*1";
w["respect"]="171*1";
w["respond"]="23*1";
w["respons"]="3*1,4*1,6*6,9*6,10*6,13*6,14*6,15*6,16*6,17*6,18*6,19*6,20*6,21*6,22*48,23*1,24*50,25*2,27*1,28*1,29*1,31*46,32*7,33*7,34*7,35*7,37*1,38*1,40*1,41*7,42*7,44*1,45*1,46*1,47*1,49*1,50*1,51*1,52*1,53*41,54*7,55*7,56*7,57*7,65*1,66*1,67*1,68*2,69*1,70*1,71*1,72*1,73*1,74*1,75*1,76*1,77*1,78*1,79*1,80*46,81*7,82*7,83*7,84*7,85*14,86*7,91*7,93*1,94*1,95*1,96*2,97*1,98*1,99*1,100*1,101*1,102*1,103*1,104*1,105*1,106*1,107*1,108*1,109*1,110*1,111*1,112*1,113*1,114*1,115*1,116*1,117*1,118*1,119*1,120*1,121*1,122*46,123*7,124*7,125*7,126*7,127*7,140*7,142*6,143*6,144*6,145*6,146*6,147*6,148*6,149*6,152*6,153*6,159*52,160*4,162*6,163*6,164*6,165*6,166*6,167*6,168*52,169*1,173*1,174*1,175*2,177*1,178*1,180*2,182*1,184*1,186*7,187*1,188*1,189*1,190*1,191*1";
w["response."]="22*1,77*1";
w["response:"]="6*1,9*1,10*1,13*1,14*1,15*1,16*1,17*1,18*1,19*1,20*1,21*1,142*1,143*1,144*1,145*1,146*1,147*1,148*1,149*1,152*1,153*1,162*1,163*1,164*1,165*1,166*1,167*1";
w["responses."]="25*1";
w["responses:"]="27*1,28*1,29*1,40*1,45*1,46*1,47*1,49*1,50*1,51*1,52*1,65*1,66*1,67*1,68*1,69*1,70*1,71*1,72*1,73*1,74*1,75*1,76*1,77*1,78*1,79*1,93*1,94*1,95*1,96*1,97*1,98*1,99*1,100*1,101*1,102*1,103*1,104*1,105*1,106*1,107*1,108*1,109*1,110*1,111*1,112*1,113*1,114*1,115*1,116*1,117*1,118*1,119*1,120*1,121*1,188*1";
w["rest"]="24*1,25*1";
w["restor"]="59*1,137*1,139*1";
w["restrict"]="62*1,75*1,76*1,113*1,114*1,115*2,119*1,120*1,131*1,132*7,133*3,181*1";
w["result"]="22*1,24*1,37*1,131*1,132*7";
w["resum"]="72*1,103*1,104*1,140*6";
w["retaddr"]="153*2";
w["retri"]="159*1";
w["retried."]="51*1";
w["retriev"]="3*1,4*1,9*2,18*2,70*1,89*1,98*1,136*1";
w["return"]="9*1,33*2,34*3,37*1,41*2,43*3,52*1,55*2,56*3,63*6,68*2,70*1,77*1,78*2,82*2,84*1,85*4,89*2,91*12,96*3,98*1,105*1,124*2,125*1,126*3,131*4,132*10,136*3,139*1,140*9,153*2";
w["returned."]="34*1,52*1,68*1,85*1,96*2,132*1";
w["rev."]="171*1";
w["rev.:"]="171*1";
w["revis"]="63*9,170*52,171*1,185*2";
w["right"]="171*3,172*1";
w["ring"]="153*1";
w["rise"]="61*1";
w["risk"]="172*1";
w["risk."]="172*1";
w["rout"]="185*1";
w["routin"]="185*1";
w["rsp"]="24*1,39*2,41*41,42*41";
w["rsp:"]="39*2,41*41,42*41";
w["rsp_avr8_activate_phys"]="96*1";
w["rsp_avr8_data"]="126*1,140*1";
w["rsp_avr8_fail"]="127*1,140*1";
w["rsp_avr8_list"]="140*1";
w["rsp_avr8_ok"]="123*1,140*1";
w["rsp_avr8_pc"]="125*1,140*1";
w["rsp_discovery_fail"]="43*1";
w["rsp_discovery_list"]="43*1";
w["rsp_edbg_data"]="186*1";
w["rsp_edbg_fail"]="186*1";
w["rsp_edbg_list"]="186*1";
w["rsp_edbg_ok"]="186*1";
w["rsp_fail"]="37*1,42*1,140*3,186*3";
w["rsp_housekeeping_data"]="56*2,63*1";
w["rsp_housekeeping_fail"]="57*1,63*1";
w["rsp_housekeeping_failed_with_data"]="63*1";
w["rsp_housekeeping_list"]="55*1,63*1";
w["rsp_housekeeping_ok"]="54*1,63*1";
w["rsp_list"]="41*1";
w["rt"]="1*1";
w["rule"]="131*1,132*1,133*1,139*1";
w["run"]="24*1,89*1,92*2,99*1,103*41,104*42,109*3,135*1,136*1,137*1,138*3,139*3,140*12,182*2,185*6";
w["run:"]="139*1";
w["runaway"]="135*1";
w["runnin"]="52*1";
w["rw"]="30*3,131*3,132*8,133*10,181*8,182*4,185*1";
w["rwise"]="171*1";

