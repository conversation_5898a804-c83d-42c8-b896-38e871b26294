tr th .added { color: #E6E6FA; } 
tr th .changed {color: #99ff99; }
div.added tr, div.added    { background-color: #E6E6FA; }
div.deleted tr, div.deleted  { text-decoration: line-through;
               background-color: #FF7F7F; }
div.changed tr, div.changed  { background-color: #99ff99; }
div.off      {  }

span.added   { background-color: #E6E6FA; }
span.deleted { text-decoration: line-through;
               background-color: #FF7F7F; }
span.changed { background-color: #99ff99; }
span.off     {  }


body { font: 12px Verdana, Geneva, sans-serif; }
p, ul, ol, li { font: 10pt Verdana, Geneva, sans-serif; }
h1 { font: 15pt Arial, Helvetica, geneva;
     color: black!important;
}
h2 { font: normal 12pt Arial, Helvetica, geneva; }

#header {
    background: white;
    position: fixed;
    width: 100%;
    height: 99px;
    top: 0;
    right: 0;
    bottom: auto;
    left: 0;
    border-bottom: 1px solid #bbc4c5;
    z-index: 2000;
}

#header h1 {
    margin-left: 310px;
    position: fixed;
    top: 20px;
    left: -15px;
    color: #404040 !important;
}


#header h1 {
	margin-top: 2px;
}


p.breadcrumbs {
    margin-top: 30px;
    margin-left: 310px;
}

#header img {
    float: left;
    margin-left: 20px;
}

#header p.breadcrumbs a {
    color: #bbb;
}

#leftnavigation {
    overflow: auto;
    position: fixed;
    height: auto;
    top:100px;
    /*right:10px;*/
    /*left:10px;*/
    bottom: 0;
    left: 0;
    width:inherit;
    z-index: 1500;
    border-right:2px solid #bbc4c5;
    padding:1px;
    background-color: #ededed!important;
}

#treeDiv {
    overflow: auto;
   /* position: fixed;*/
    height: auto;
    top: 136px;
    bottom: 0;
    left: 0;
   /* width: 18%;*/
    z-index: 1500;
   /* border-right:2px solid #CCCCCC;
    background-color: #f0f0f0!important;*/
}

/*#searchDiv {
    overflow: auto;
    position: fixed;
    height: auto;
    top: 138px;
    bottom: 0;
    left: 0;
    width: 243px;
    z-index: 1500;
    border-right:2px solid #CCCCCC;
    background-color: #f0f0f0!important;
}*/

#content {
    position: relative;
    top: 90px;  /*left: 240px;*/
    right: auto;   bottom: 20px;  
    /*margin: 0px 0px 0px 280px;*/
    width: auto;
    height: inherit;
    padding-left: 5px;
    padding-right: 30px;
    border-left: 1px solid #cccccc;
    overflow :scroll;
    overflow-x:auto;
    z-index: 1000;
 
}

#navheader {
    position: fixed;
    background: #DCDCDC;
    padding-left: 10px;
    right: 0px;
    top: 10px;
    text-align: right;
}

#content h1, #content h2 { 
color: #404040 !important; 
font-size: 170%;
font-weight: normal;
}
.navfooter { bottom: 2%; }
.highlight { background-color: #c5d3c3; }
.highlightButton{ font-size: 0; }

/*  Show Hide TOC tree */
.pointLeft {
    padding-right: 15px;
    display: block;
    cursor: pointer;
}
.pointRight {
    padding-right: 15px;
    display: block;
    cursor: pointer;
}

/* Search results Styling */
.searchExpression {
    color: #0050A0;
    background-color: #EBEFF8;
    font-size: 12pt;
}
.searchresult li a {
    text-decoration: none;
    color: #0050A0;
}
.searchresult li { color: #0050A0; }
.shortdesclink { color: gray; font-size: 9pt; }
.searchText { float:left;width:150px; }
.searchButton {
    padding: 2px 12px 2px 12px;
    background-color:#bbb;
    border:#bbb solid 1pt;
    font-weight: bold;
    font-size: 10pt
}
.searchButton:hover{
    background-color: #cccccc;
}
.searchFieldSet {}

.title, div.toc>p{ font-weight: bold; }	

p.breadcrumbs {
        display: inline;
	margin-bottom: 0px;
	margin-top: 33px;
}

p.breadcrumbs a {
	padding-right: 12px;
	margin-right: 5px;
	text-decoration: none;
	color: #575757;
	text-transform: uppercase;
	font-size: 10px;
}

p.breadcrumbs a:first-child {background: url(../images/breadcrumb-arrow-white.png) no-repeat right center;}

p.breadcrumbs a:hover {text-decoration: underline;}

#star ul.star { 
    LIST-STYLE: none; 
    MARGIN: 0; 
    PADDING: 0; 
    WIDTH: 85px;
    /* was 100 */ 
    HEIGHT: 20px;
    LEFT: 1px; 
    TOP: -5px; 
    POSITION: relative; 
    FLOAT: right; 
    BACKGROUND: url('../images/starsSmall.png') repeat-x 0 -25px;
}
#star li { 
    PADDING: 0; 
    MARGIN: 0; 
    FLOAT: right; 
    DISPLAY: block; 
    WIDTH: 85px;
    /* was 100 */
    HEIGHT: 20px; 
    TEXT-DECORATION: none; 
    text-indent: -9000px; 
    Z-INDEX: 20; 
    POSITION: absolute; 
    PADDING: 0; 
}
#star li.curr { 
    BACKGROUND: url('../images/starsSmall.png') left 25px; 
    FONT-SIZE: 1px; 
}

table.navLinks {margin-right: 20px;}

table.navLinks td a {
	text-decoration: none;
	text-transform: uppercase;
	color: black;
	font-size: 11px;
}

a.navLinkPrevious {
	padding-left: 12px;
	background: url(../images/previous-arrow.png) no-repeat left center;
}

a.navLinkNext {
	padding-right: 12px;
	background: url(../images/next-arrow.png) no-repeat right center;
}

a#showHideButton {
	padding-left: 20px;
	background: url(../images/sidebar.png) no-repeat left center;
}

	
.filetree li span a { color: #777; }

#treediv { -webkit-box-shadow: #CCC 0px 1px 2px 0px inset; }

.legal, .legal *{
 color: #555;
 text-align: center;
 padding-bottom: 10px;
}

.internal { color : #0000CC;}

.writeronly {color : red;}

.remark, .remark .added, .remark .changed, .remark .deleted{ background: yellow;} 

tr th, tr th .internal, tr th .added, tr th .changed {
	background: #00589E;
	color: white;
	font-weight: bold;
	text-align: left;
}

.statustext{
    position:fixed;
    top:105px;
    width: 0%;
    height: 0%;
    opacity: .3;
    -webkit-transform: rotate(90deg);
    -moz-transform: rotate(90deg);
    -o-transform: rotate(90deg);
    white-space: nowrap;
    color: red;
    font-weight: bold;
    font-size: 2em;
    margin-top: 30px;
}

#toolbar {
	width: 100%;
	height: 33px;
	position: fixed;
	top: 93px;
	z-index: 99;
	left: 280px;
	color: #333;
	line-height: 28px;
	padding-left: 10px;
}

#toolbar-left {
	position: relative;
	left: 0px;
}
 
body p.breadcrumbs {
	margin: 0px;
	padding: 0px;
	line-height: 28px;
}

/*body #content {
	position: static;
	margin-top: 126px;
	top: 0px;
}*/

body.sidebar #toolbar{left: 0px;}

body.sidebar #toolbar-left{left: 0px;}

div#toolbar-left img {vertical-align: text-top;}

div.note *, div.caution *, div.important *, div.tip *, div.warning * {
    background: inherit !important;
    color: inherit !important;
    border: inherit  /*!important*/;    
}

#content table thead, #content table th, #content table th p{
    color: white;
    font-weight: bold;
}

#content table caption{font-weight: bold;}

#content table td, #content table {border: 1px solid black;}

#content table td, #content table th { padding: 5px;}

#content table {margin-bottom: 20px;}

*[align = 'center']{ text-align: center;}

#content .qandaset>table, #content .qandaset>table td, #content .calloutlist table, #content .calloutlist table td, #content .navfooter table, #content .navfooter table td {
    border: 0px solid;
}

#sidebar 
{ 
    position: fixed;
    margin: 0px;
    left: 0px;
    right: auto;
    top: 99px;
    bottom: 0px;
    height: 543px;
    z-index: 0;
    display: block;
    visibility: visible;
    width: 280px;
}

@media print {

    body * {
        visibility: hidden;
    }

    #content, #content * {
        visibility: visible;
    }

   #sidebar, .navfooter {
       display: none;
   }

   #content {
	margin: 0 0 0 0;
    }

}

#expanders {
    float: left;
    width: 100%;
    padding-bottom: 1em;
}

#expanders dt {        
    padding-bottom: 4px;
    border-bottom: 2px solid #cccccc;
    margin-top: 1em;
    margin-bottom: 1em;  
    background: url(../images/plus.png) 0px 7px no-repeat;
    /*background: pink;*/
    cursor: pointer;
}

#expanders dt h2 {
    font: bold 14pt IntervalLight, sans-serif;
    text-decoration: none;
    color: #0066CB;
    /*background-position: -16px 0;*/
    padding-left: 13px;
}

#expanders dt.plus {
        background: url(../images/plus.png) 0px 7px no-repeat;
}

#expanders dt.minus {
        background: url(../images/minus.png) 0px 7px no-repeat;
}


#expanders dd {    
    display: none;
    margin-bottom: 3em;
    /*background: yellow;*/
}

#expanders .hitarea {
	background: url(../images/ui-icons_217bc0_256x240.png) 0 -208px no-repeat;
	height: 16px;
	width: 16px;	
	float: left;
	cursor: pointer;
}
/* fix for IE6 */
/** html .hitarea {
	display: inline;
	float:none;
}*/



#expanders .prod
{
    width: 300px;
    border: #DDD solid 1px;
    float: left;
    margin: 1px;
    height: 160px;
    margin-top: 0px;
}

#expanders .prodimg
{
    /*border: #DDD solid 1px;*/
    float: left;
}

.prodimg img {
    display:        block;
    margin-left:    3px;
    margin-top:     auto;
    margin-bottom:  auto;
    width:          100px;
    vertical-align: middle;
}

#expanders .prodtext
{
    /*background: #F8F8F8;*/
    width: 165px;
    float: left;
    margin-left: 1em;
}

#expanders .prod p {
    clear: both;
}

#expanders ul {
    margin: 0;
    padding: 0;
    list-style-type: none;
}

#expanders li {
    padding-left: 0.5em;
}

a.external {
    background: url("../images/external_link.gif") no-repeat scroll right top transparent;
    padding:    0 13px 0 0;   
}